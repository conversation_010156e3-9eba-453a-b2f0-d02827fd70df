<!--
  页面名称：收款信息标签页
  功能描述：展示家政服务订单的收款信息
-->
<template>
  <div class="payment-info-tab">
    <div class="payment-content">
      <el-row :gutter="40">
        <el-col :span="12">
          <div class="payment-details">
            <div class="detail-item">
              <label>订单金额：</label>
              <span class="amount">¥{{ formatAmount(paymentInfo.orderAmount) }}</span>
            </div>
            <div class="detail-item">
              <label>收款方式：</label>
              <span>{{ paymentInfo.paymentMethod }}</span>
            </div>
            <div class="detail-item">
              <label>收款备注：</label>
              <span>{{ paymentInfo.paymentNotes }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="payment-details">
            <div class="detail-item">
              <label>支付状态：</label>
              <el-tag type="success" size="small">{{
                getPaymentStatusText(paymentInfo.paymentStatus)
              }}</el-tag>
            </div>
            <div class="detail-item">
              <label>收款日期：</label>
              <span>{{ paymentInfo.paymentDate }}</span>
            </div>
            <div class="detail-item">
              <label>收款金额：</label>
              <span class="amount">¥{{ formatAmount(paymentInfo.receivedAmount) }}</span>
            </div>
            <div class="detail-item">
              <label>操作人：</label>
              <span>{{ paymentInfo.operator }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { Money } from '@element-plus/icons-vue'

interface Props {
  orderDetail: any
}

defineProps<Props>()

// 收款信息数据
const paymentInfo = reactive({
  orderAmount: 12800,
  paymentMethod: '银行转账',
  paymentNotes: '客户通过银行转账支付,已确认到账',
  paymentStatus: 'paid',
  paymentDate: '2024-05-30',
  receivedAmount: 12800,
  operator: '李小明(财务)'
})

const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

const getPaymentStatusText = (status: string): string => {
  const statusMap = {
    unpaid: '未支付',
    paid: '已支付',
    partial: '部分支付'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped lang="scss">
.payment-info-tab {
  .payment-content {
    .payment-details {
      .detail-item {
        margin-bottom: 16px;
        display: flex;
        align-items: center;

        label {
          min-width: 80px;
          color: #606266;
          font-weight: 500;
          margin-right: 8px;
        }

        span {
          color: #303133;
          flex: 1;

          &.amount {
            color: #f56c6c;
            font-weight: 500;
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>
