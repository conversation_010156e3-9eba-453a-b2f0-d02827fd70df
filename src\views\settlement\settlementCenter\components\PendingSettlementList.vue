<!--
  页面名称：待结算列表
  功能描述：展示待结算订单列表，支持搜索筛选、分页、批量选择、生成对账单等操作
-->
<template>
  <div class="pending-settlement-list">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="年-月-日"
            end-placeholder="年-月-日"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="机构名称">
          <el-input
            v-model="searchForm.agencyName"
            placeholder="请输入机构名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="阿姨姓名">
          <el-input
            v-model="searchForm.practitionerName"
            placeholder="请输入阿姨姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.orderStatus"
            placeholder="所有订单状态"
            clearable
            style="width: 150px"
          >
            <el-option label="所有订单状态" value="" />
            <el-option label="已完成" value="completed" />
            <el-option label="服务中" value="in_service" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="已退款" value="refunded" />
          </el-select>
        </el-form-item>
        <el-form-item label="结算状态">
          <el-select
            v-model="searchForm.settlementStatus"
            placeholder="所有结算状态"
            clearable
            style="width: 150px"
          >
            <el-option label="所有结算状态" value="" />
            <el-option label="待结算" value="pending" />
            <el-option label="结算中" value="settling" />
            <el-option label="已结算" value="settled" />
            <el-option label="结算失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            筛选
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="left-section">
        <el-checkbox
          v-model="selectAll"
          :indeterminate="isIndeterminate"
          @change="handleSelectAllChange"
        >
          全选
        </el-checkbox>
        <span class="selected-count">已选择 {{ selectedOrders.length }} 个订单</span>
      </div>
      <div class="right-section">
        <el-button
          type="success"
          :disabled="selectedOrders.length === 0"
          @click="handleGenerateReconciliation"
        >
          <el-icon><Document /></el-icon>
          生成对账单
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="orderNo" label="订单编号" width="180" />
        <el-table-column prop="packageName" label="套餐名称" min-width="200" />
        <el-table-column prop="agencyName" label="家政机构" width="150" />
        <el-table-column prop="orderTime" label="下单时间" width="160" />
        <el-table-column prop="completionTime" label="完成时间" width="160" />
        <el-table-column prop="practitionerName" label="阿姨姓名" width="120" />
        <el-table-column prop="orderStatus" label="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getOrderStatusType(row.orderStatus)" size="small">
              {{ getOrderStatusText(row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="settlementStatus" label="结算状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getSettlementStatusType(row.settlementStatus)" size="small">
              {{ getSettlementStatusText(row.settlementStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderAmount" label="订单金额" width="120">
          <template #default="{ row }">
            <span class="amount-text">¥{{ formatAmount(row.orderAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="info" size="small" text @click="handleViewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 生成对账单弹窗 -->
    <GenerateReconciliationDialog
      v-model:visible="reconciliationDialogVisible"
      :selected-orders="selectedOrders"
      @success="handleReconciliationSuccess"
    />

    <!-- 订单详情弹窗 -->
    <OrderDetailDialog v-model:visible="detailDialogVisible" :order-data="currentOrder" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Document } from '@element-plus/icons-vue'
import GenerateReconciliationDialog from './GenerateReconciliationDialog.vue'
import OrderDetailDialog from './OrderDetailDialog.vue'
import { getPendingSettlementList, generateReconciliationBill } from '@/api/settlement/settlement'

/** 搜索表单数据 */
const searchForm = reactive({
  dateRange: [],
  agencyName: '',
  practitionerName: '',
  orderStatus: '',
  settlementStatus: ''
})

/** 表格数据 */
const tableData = ref([])

/** 加载状态 */
const loading = ref(false)

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 选中的订单 */
const selectedOrders = ref([])

/** 全选状态 */
const selectAll = ref(false)

/** 生成对账单弹窗显示状态 */
const reconciliationDialogVisible = ref(false)

/** 详情弹窗显示状态 */
const detailDialogVisible = ref(false)

/** 当前选中的订单数据 */
const currentOrder = ref(null)

/** 是否半选状态 */
const isIndeterminate = computed(() => {
  return selectedOrders.value.length > 0 && selectedOrders.value.length < tableData.value.length
})

/** 获取待结算列表 */
const fetchList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      size: pagination.size,
      startDate: searchForm.dateRange?.[0] || '',
      endDate: searchForm.dateRange?.[1] || ''
    }

    const response = await getPendingSettlementList(params)
    tableData.value = response.data?.list || []
    pagination.total = response.data?.total || 0
  } catch (error) {
    ElMessage.error('获取待结算列表失败')
    console.error('获取待结算列表失败:', error)
  } finally {
    loading.value = false
  }
}

/** 处理搜索 */
const handleSearch = () => {
  pagination.page = 1
  fetchList()
}

/** 处理重置 */
const handleReset = () => {
  Object.assign(searchForm, {
    dateRange: [],
    agencyName: '',
    practitionerName: '',
    orderStatus: '',
    settlementStatus: ''
  })
  pagination.page = 1
  selectedOrders.value = []
  selectAll.value = false
  fetchList()
}

/** 处理全选变化 */
const handleSelectAllChange = (val: boolean) => {
  if (val) {
    selectedOrders.value = [...tableData.value]
  } else {
    selectedOrders.value = []
  }
}

/** 处理选择变化 */
const handleSelectionChange = (selection: any[]) => {
  selectedOrders.value = selection
  selectAll.value = selection.length === tableData.value.length
}

/** 处理生成对账单 */
const handleGenerateReconciliation = () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要生成对账单的订单')
    return
  }
  reconciliationDialogVisible.value = true
}

/** 处理对账单生成成功 */
const handleReconciliationSuccess = () => {
  reconciliationDialogVisible.value = false
  selectedOrders.value = []
  selectAll.value = false
  fetchList()
  ElMessage.success('对账单生成成功')
}

/** 处理查看详情 */
const handleViewDetail = (row: any) => {
  currentOrder.value = row
  detailDialogVisible.value = true
}

/** 处理分页大小变化 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

/** 处理当前页变化 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

/** 获取订单状态类型 */
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    completed: 'success',
    in_service: 'warning',
    cancelled: 'danger',
    refunded: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    completed: '已完成',
    in_service: '服务中',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return statusMap[status] || status
}

/** 获取结算状态类型 */
const getSettlementStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    settling: 'warning',
    settled: 'success',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取结算状态文本 */
const getSettlementStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待结算',
    settling: '结算中',
    settled: '已结算',
    failed: '结算失败'
  }
  return statusMap[status] || status
}

/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount?.toFixed(2) || '0.00'
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.pending-settlement-list {
  .search-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .search-form {
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 20px;
      }
    }
  }

  .action-bar {
    background: white;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-section {
      display: flex;
      align-items: center;
      gap: 16px;

      .selected-count {
        color: #606266;
        font-size: 14px;
      }
    }

    .right-section {
      display: flex;
      gap: 12px;
    }
  }

  .table-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .amount-text {
      color: #f56c6c;
      font-weight: 600;
    }

    .pagination-section {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
