/**
 * 日志解析器工具
 * 用于解析操作日志中的变更内容，提取字段变更信息
 */

export interface LogChangeItem {
  field: string
  oldValue: string
  newValue: string
  hasChanged: boolean
}

export interface ParsedLogContent {
  changes: LogChangeItem[]
  hasChanges: boolean
  rawContent: string
}

/**
 * 解析日志内容中的变更信息
 * @param logContent 日志内容字符串
 * @returns 解析后的变更信息
 */
export function parseLogContent(logContent: string): ParsedLogContent {
  if (!logContent) {
    return {
      changes: [],
      hasChanges: false,
      rawContent: logContent
    }
  }

  const changes: LogChangeItem[] = []

  // 常见的变更格式模式
  const patterns = [
    // 格式1: 字段名: 旧值 → 新值
    /(\w+):\s*([^→]+)→\s*([^\n]+)/g,
    // 格式2: 字段名从"旧值"变更为"新值"
    /(\w+)从"([^"]*)"变更为"([^"]*)"/g,
    // 格式3: 字段名: 旧值 -> 新值
    /(\w+):\s*([^->]+)->\s*([^\n]+)/g,
    // 格式4: 字段名由"旧值"改为"新值"
    /(\w+)由"([^"]*)"改为"([^"]*)"/g
  ]

  patterns.forEach((pattern) => {
    let match
    while ((match = pattern.exec(logContent)) !== null) {
      const field = match[1].trim()
      const oldValue = match[2].trim()
      const newValue = match[3].trim()

      // 检查值是否真的发生了变化
      const hasChanged =
        oldValue !== newValue &&
        oldValue !== '' &&
        newValue !== '' &&
        oldValue !== 'null' &&
        newValue !== 'null' &&
        oldValue !== 'undefined' &&
        newValue !== 'undefined'

      if (hasChanged) {
        changes.push({
          field,
          oldValue,
          newValue,
          hasChanged: true
        })
      }
    }
  })

  // 如果没有找到标准格式，尝试解析JSON格式的变更
  if (changes.length === 0) {
    try {
      // 尝试解析JSON格式的变更日志
      const jsonMatch = logContent.match(/\{.*\}/)
      if (jsonMatch) {
        const changeData = JSON.parse(jsonMatch[0])
        if (changeData.changes && Array.isArray(changeData.changes)) {
          changeData.changes.forEach((change: any) => {
            if (change.field && change.oldValue !== change.newValue) {
              changes.push({
                field: change.field,
                oldValue: String(change.oldValue || ''),
                newValue: String(change.newValue || ''),
                hasChanged: true
              })
            }
          })
        }
      }
    } catch (error) {
      // JSON解析失败，忽略
      console.warn('Failed to parse JSON log content:', error)
    }
  }

  return {
    changes,
    hasChanges: changes.length > 0,
    rawContent: logContent
  }
}

/**
 * 格式化字段名称显示
 * @param field 字段名
 * @returns 格式化后的字段名
 */
export function formatFieldName(field: string): string {
  const fieldMap: Record<string, string> = {
    projectName: '项目名称',
    universityName: '合作高校',
    enterpriseName: '合作企业',
    startDate: '开始日期',
    endDate: '结束日期',
    totalAmount: '总金额',
    managerName: '负责人',
    contractType: '合同类型',
    paymentStatus: '支付状态',
    orderStatus: '订单状态',
    opportunityId: '关联商机',
    leadId: '关联线索',
    customerName: '客户名称',
    businessType: '业务类型',
    businessStage: '业务阶段',
    collectionAmount: '收款金额',
    collectionMethod: '收款方式',
    collectionDate: '收款日期',
    remark: '备注'
  }

  return fieldMap[field] || field
}

/**
 * 格式化字段值显示
 * @param value 字段值
 * @param field 字段名
 * @returns 格式化后的字段值
 */
export function formatFieldValue(value: string, field: string): string {
  if (!value || value === 'null' || value === 'undefined') {
    return '-'
  }

  // 特殊字段的格式化
  switch (field) {
    case 'totalAmount':
    case 'collectionAmount':
      return `¥${Number(value).toFixed(2)}`
    case 'startDate':
    case 'endDate':
    case 'collectionDate':
      return formatDate(value)
    case 'paymentStatus':
      return formatPaymentStatus(value)
    case 'orderStatus':
      return formatOrderStatus(value)
    case 'contractType':
      return formatContractType(value)
    default:
      return value
  }
}

/**
 * 格式化日期
 * @param dateString 日期字符串
 * @returns 格式化后的日期
 */
function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN')
  } catch {
    return dateString
  }
}

/**
 * 格式化支付状态
 * @param status 状态值
 * @returns 格式化后的状态
 */
function formatPaymentStatus(status: string): string {
  const statusMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    refunded: '已退款',
    partial: '部分支付'
  }
  return statusMap[status] || status
}

/**
 * 格式化订单状态
 * @param status 状态值
 * @returns 格式化后的状态
 */
function formatOrderStatus(status: string): string {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    approved: '已批准',
    rejected: '已拒绝',
    executing: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/**
 * 格式化合同类型
 * @param type 类型值
 * @returns 格式化后的类型
 */
function formatContractType(type: string): string {
  const typeMap: Record<string, string> = {
    electronic: '电子合同',
    paper: '纸质合同',
    both: '电子+纸质'
  }
  return typeMap[type] || type
}

