import request from '@/config/axios'

export interface EnterpriseTrainingOrder {
  id?: number
  orderNumber?: string
  businessOpportunity?: string
  lead?: string
  companyName: string
  contactPerson?: string
  contactPhone?: string
  companyAddress?: string
  trainingProject: string
  trainingType?: string
  traineeCount: number
  trainingPeriod: string[] | string
  trainingLocation?: string
  trainer?: string
  trainingDescription?: string
  orderAmount: number
  orderStatus: string
  paymentStatus: string
  manager?: string
  contractType?: string
  contractFile?: any
  remark?: string
  createTime?: string
  updateTime?: string
  // 支付信息字段
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  operatorName?: string
  collectionRemark?: string
}

export interface EnterpriseTrainingOrderQuery {
  orderStatus?: string
  paymentStatus?: string
  keyword?: string
  page: number
  size: number
}

export interface EnterpriseTrainingOrderResponse {
  list: EnterpriseTrainingOrder[]
  total: number
}

/**
 * 获取企业培训订单列表
 */
export const getEnterpriseTrainingList = (params: EnterpriseTrainingOrderQuery) => {
  return request.get<EnterpriseTrainingOrderResponse>({
    url: '/order-center/enterprise-training/list',
    params
  })
}

/**
 * 获取企业培训订单详情
 */
export const getEnterpriseTrainingDetail = (id: number) => {
  return request.get<EnterpriseTrainingOrder>({
    url: `/order-center/enterprise-training/${id}`
  })
}

/**
 * 新增企业培训订单
 */
export const createEnterpriseTraining = (data: EnterpriseTrainingOrder) => {
  return request.post<EnterpriseTrainingOrder>({
    url: '/order-center/enterprise-training',
    data
  })
}

/**
 * 更新企业培训订单
 */
export const updateEnterpriseTraining = (id: number, data: EnterpriseTrainingOrder) => {
  return request.put<EnterpriseTrainingOrder>({
    url: `/order-center/enterprise-training/${id}`,
    data
  })
}

/**
 * 删除企业培训订单
 */
export const deleteEnterpriseTraining = (id: number) => {
  return request.delete({
    url: `/order-center/enterprise-training/${id}`
  })
}

/**
 * 获取企业培训订单统计信息
 */
export const getEnterpriseTrainingStats = () => {
  return request.get({
    url: '/order-center/enterprise-training/stats'
  })
}

/**
 * 获取操作日志
 */
export const getEnterpriseTrainingOptLog = (orderId: string) => {
  return request.get({
    url: `/order-center/enterprise-training/${orderId}/opt-log`
  })
}

/**
 * 临时调试接口 - 调用指定的URL
 */
export const debugEnterpriseTrainingOrder = (id: number, tenantId: number = 1) => {
  return request.get<EnterpriseTrainingOrder>({
    url: `/publicbiz/enterprise-training-order/get`,
    params: {
      id,
      tenantId
    }
  })
}

/**
 * 临时调试接口 - 调用debug-main路径
 */
export const debugEnterpriseTrainingOrderMain = (id: number) => {
  return request.get<EnterpriseTrainingOrder>({
    url: `/publicbiz/enterprise-training-order/debug-main/${id}`
  })
}

/**
 * 企业培训订单审批通过
 */
export const approveEnterpriseTrainingOrder = (data: {
  orderId: number
  orderNo: string
  approvalType: string
  comments?: string
}) => {
  // 为必填字段提供临时测试数据
  const requestData = {
    approvalId: 'AP' + Date.now() + '_' + Math.random().toString(36).substr(2, 9), // 临时测试数据：生成审批ID
    orderId: data.orderId,
    tenantId: 1, // 临时测试数据：租户ID
    approverId: 1001, // 临时测试数据：审批人ID
    approverName: '测试审批员', // 临时测试数据：审批人姓名
    approvalComments: data.comments || '测试审批通过', // 临时测试数据：审批意见
    nextStatus: 'approved' // 临时测试数据：下一状态
  }

  console.log('调用企业培训订单审批通过接口:', requestData)
  return request.post({
    url: '/publicbiz/enterprise-training-order/approve-order',
    data: requestData
  })
}

/**
 * 企业培训订单审批拒绝
 */
export const rejectEnterpriseTrainingOrder = (data: {
  orderId: number
  orderNo: string
  approvalType: string
  rejectReason: string
  comments?: string
}) => {
  // 为必填字段提供临时测试数据
  const requestData = {
    approvalId: 'AP' + Date.now() + '_' + Math.random().toString(36).substr(2, 9), // 临时测试数据：生成审批ID
    orderId: data.orderId,
    tenantId: 1, // 临时测试数据：租户ID
    approverId: 1001, // 临时测试数据：审批人ID
    approverName: '测试审批员', // 临时测试数据：审批人姓名
    rejectReason: data.rejectReason || '测试审批拒绝', // 临时测试数据：拒绝原因
    nextStatus: 'rejected' // 临时测试数据：下一状态
  }

  console.log('调用企业培训订单审批拒绝接口:', requestData)
  return request.post({
    url: '/publicbiz/enterprise-training-order/reject-order',
    data: requestData
  })
}

/**
 * 企业培训订单上传纸质合同
 */
export const uploadEnterpriseTrainingPaperContract = (data: FormData) => {
  return request.post({
    url: '/publicbiz/enterprise-training-order/enterprise-training-upload-paper-contract',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// 确认收款相关接口类型定义
export interface ConfirmPaymentParams {
  orderId: number
  tenantId: number
  paymentType: string
  paymentAmount: number
  paymentTime: string
  operatorId: number
  operatorName: string
  paymentRemark?: string
  transactionId?: string
}

export interface ConfirmPaymentResponse {
  success: boolean
  paymentNo: string
  message: string
}

/**
 * 确认企业培训订单收款
 */
export const confirmEnterpriseTrainingPayment = (data: ConfirmPaymentParams) => {
  return request.post<ConfirmPaymentResponse>({
    url: '/publicbiz/enterprise-training-order/confirm-payment',
    data
  })
}

// 审批记录相关接口类型定义
export interface ApprovalRecord {
  id: number
  approvalId: string
  approvalNo: string
  approvalType: string
  status: string
  operatorId: number
  operatorName: string
  operatorRole: string
  action: string
  comments: string
  createTime: string
  updateTime: string
}

export interface ApprovalRecordsResponse {
  code: number
  data: {
    list: ApprovalRecord[]
    total: number
  }
  message: string
}

/**
 * 获取企业培训订单审批记录
 */
export const getEnterpriseTrainingApprovalRecords = (orderId: number) => {
  return request.get<ApprovalRecordsResponse>({
    url: `/publicbiz/enterprise-training-order/approval-records/${orderId}`,
    params: {
      tenantId: 1 // 临时测试数据：租户ID
    }
  })
}

// 操作日志相关接口类型定义
export interface OperationLog {
  id: number
  orderNo: string
  logType: string
  logTitle: string
  logContent: string
  oldStatus: string
  newStatus: string
  operatorId: number
  operatorName: string
  operatorRole: string
  relatedPartyType: string
  relatedPartyName: string
  createTime: number
}

export interface OperationLogsResponse {
  code: number
  data: {
    total: number
    records: OperationLog[]
  }
  msg: string
}

/**
 * 获取企业培训订单操作日志
 */
export const getEnterpriseTrainingOperationLogs = (
  orderNo: string,
  tenantId: number = 1,
  page: number = 1,
  size: number = 50
) => {
  return request.get<OperationLogsResponse>({
    url: '/publicbiz/enterprise-training-order/operation-logs',
    params: {
      orderNo,
      tenantId,
      page,
      size
    }
  })
}
