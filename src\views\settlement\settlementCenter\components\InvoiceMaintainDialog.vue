<!--
  页面名称：维护开票信息弹窗
  功能描述：维护开票信息，支持设置开票状态、发票号码、开票日期、发票类型等
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="维护开票信息"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="invoice-maintain-dialog"
  >
    <div class="dialog-content">
      <!-- 对账单信息 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          <span>对账单信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>对账单号：</label>
            <span>{{ invoiceData?.statementNo }}</span>
          </div>
          <div class="info-item">
            <label>家政机构：</label>
            <span>{{ invoiceData?.agencyName }}</span>
          </div>
          <div class="info-item">
            <label>对账金额：</label>
            <span class="amount-text">¥{{ formatAmount(invoiceData?.reconciliationAmount) }}</span>
          </div>
          <div class="info-item">
            <label>当前状态：</label>
            <el-tag :type="getInvoiceStatusType(invoiceData?.invoiceStatus)" size="small">
              {{ getInvoiceStatusText(invoiceData?.invoiceStatus) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 开票信息维护 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Edit /></el-icon>
          <span>开票信息维护</span>
        </div>
        <el-form
          :model="invoiceForm"
          :rules="invoiceRules"
          ref="invoiceFormRef"
          label-width="120px"
        >
          <el-form-item label="开票状态" prop="invoiceStatus" required>
            <el-select
              v-model="invoiceForm.invoiceStatus"
              placeholder="请选择开票状态"
              style="width: 100%"
            >
              <el-option label="请选择开票状态" value="" />
              <el-option label="未开票" value="not_invoiced" />
              <el-option label="已开票" value="invoiced" />
              <el-option label="已作废" value="voided" />
            </el-select>
          </el-form-item>

          <el-form-item label="发票号码" prop="invoiceNo" required>
            <el-input
              v-model="invoiceForm.invoiceNo"
              placeholder="请输入发票号码"
              :disabled="invoiceForm.invoiceStatus !== 'invoiced'"
            />
          </el-form-item>

          <el-form-item label="开票日期" prop="invoiceDate" required>
            <el-date-picker
              v-model="invoiceForm.invoiceDate"
              type="date"
              placeholder="年-月-日"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="invoiceForm.invoiceStatus !== 'invoiced'"
            />
          </el-form-item>

          <el-form-item label="发票类型" prop="invoiceType" required>
            <el-select
              v-model="invoiceForm.invoiceType"
              placeholder="请选择发票类型"
              style="width: 100%"
              :disabled="invoiceForm.invoiceStatus !== 'invoiced'"
            >
              <el-option label="请选择发票类型" value="" />
              <el-option label="增值税普通发票" value="增值税普通发票" />
              <el-option label="增值税专用发票" value="增值税专用发票" />
              <el-option label="电子发票" value="电子发票" />
            </el-select>
          </el-form-item>

          <el-form-item label="发票金额" prop="invoiceAmount" required>
            <el-input
              v-model="invoiceForm.invoiceAmount"
              type="number"
              placeholder="请输入发票金额"
              :disabled="invoiceForm.invoiceStatus !== 'invoiced'"
            >
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>

          <el-form-item label="税率(%)" prop="taxRate">
            <el-select
              v-model="invoiceForm.taxRate"
              placeholder="请选择税率"
              style="width: 100%"
              :disabled="invoiceForm.invoiceStatus !== 'invoiced'"
            >
              <el-option label="0%" value="0" />
              <el-option label="1%" value="1" />
              <el-option label="3%" value="3" />
              <el-option label="6%" value="6" />
              <el-option label="9%" value="9" />
              <el-option label="13%" value="13" />
            </el-select>
          </el-form-item>

          <el-form-item label="备注说明" prop="remark">
            <el-input
              v-model="invoiceForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请填写备注说明..."
              :disabled="invoiceForm.invoiceStatus !== 'invoiced'"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          保存开票信息
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Edit } from '@element-plus/icons-vue'
import { saveInvoiceInfo } from '@/api/settlement/invoice'

/** 弹窗显示状态 */
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

/** 保存加载状态 */
const saveLoading = ref(false)

/** 发票表单引用 */
const invoiceFormRef = ref()

/** 发票表单数据 */
const invoiceForm = reactive({
  invoiceStatus: '',
  invoiceNo: '',
  invoiceDate: '',
  invoiceType: '',
  invoiceAmount: '',
  taxRate: '6',
  remark: ''
})

/** 发票表单验证规则 */
const invoiceRules = {
  invoiceStatus: [{ required: true, message: '请选择开票状态', trigger: 'change' }],
  invoiceNo: [{ required: true, message: '请输入发票号码', trigger: 'blur' }],
  invoiceDate: [{ required: true, message: '请选择开票日期', trigger: 'change' }],
  invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
  invoiceAmount: [
    { required: true, message: '请输入发票金额', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
  ]
}

/** 组件属性 */
const props = defineProps<{
  visible: boolean
  invoiceData: any
}>()

/** 组件事件 */
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

/** 监听发票数据变化，初始化表单 */
watch(
  () => props.invoiceData,
  (newData) => {
    if (newData) {
      invoiceForm.invoiceStatus = newData.invoiceStatus || ''
      invoiceForm.invoiceNo = newData.invoiceNo || ''
      invoiceForm.invoiceDate = newData.invoiceDate || ''
      invoiceForm.invoiceType = newData.invoiceType || ''
      invoiceForm.invoiceAmount = newData.reconciliationAmount?.toString() || ''
      invoiceForm.taxRate = '6'
      invoiceForm.remark = ''
    }
  },
  { immediate: true }
)

/** 处理取消 */
const handleCancel = () => {
  dialogVisible.value = false
}

/** 处理保存 */
const handleSave = async () => {
  try {
    await invoiceFormRef.value.validate()

    saveLoading.value = true
    const params = {
      statementNo: props.invoiceData?.statementNo,
      ...invoiceForm
    }

    await saveInvoiceInfo(params)
    emit('success')
    dialogVisible.value = false
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('保存开票信息失败')
      console.error('保存开票信息失败:', error)
    }
  } finally {
    saveLoading.value = false
  }
}

/** 获取开票状态类型 */
const getInvoiceStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    not_invoiced: 'warning',
    invoiced: 'success',
    voided: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取开票状态文本 */
const getInvoiceStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    not_invoiced: '未开票',
    invoiced: '已开票',
    voided: '已作废'
  }
  return statusMap[status] || status
}

/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount?.toFixed(2) || '0.00'
}
</script>

<style scoped lang="scss">
.invoice-maintain-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: white;
    }
  }

  .dialog-content {
    .info-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      background: #fafafa;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-weight: 600;
        color: #303133;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 80px;
          }

          span {
            color: #303133;
          }

          .amount-text {
            color: #f56c6c;
            font-weight: 600;
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>




