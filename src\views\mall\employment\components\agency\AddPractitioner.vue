<!--
  页面名称：新增阿姨
  功能描述：新增阿姨信息，包含基本信息和资质文件上传
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="`为【${agencyName}】新增阿姨`"
    direction="rtl"
    size="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="add-practitioner-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="formData.name" placeholder="请输入阿姨的真实姓名" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入手机号,用于登录" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="formData.idCard" placeholder="请输入18位身份证号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="籍贯" prop="hometown">
              <el-input v-model="formData.hometown" placeholder="例如:四川成都" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 服务信息 -->
      <div class="form-section">
        <h3 class="section-title">服务信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="主要服务类型" prop="serviceType">
              <el-select
                v-model="formData.serviceType"
                placeholder="请选择服务类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in serviceTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="从业年限" prop="experienceYears">
              <el-input v-model="formData.experienceYears" placeholder="请输入数字" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="平台状态" prop="platformStatus">
              <el-select
                v-model="formData.platformStatus"
                placeholder="请选择平台状态"
                style="width: 100%"
              >
                <el-option
                  v-for="item in platformStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评级" prop="rating">
              <el-input-number
                v-model="formData.rating"
                :min="1"
                :max="5"
                :precision="1"
                :step="0.1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 资质文件上传 -->
      <div class="form-section">
        <h3 class="section-title">资质文件上传</h3>

        <!-- 身份证正反面 -->
        <div class="upload-section">
          <div class="upload-label">身份证正反面</div>
          <div class="upload-area">
            <el-upload
              ref="idCardUpload"
              :before-upload="beforeIdCardUpload"
              :http-request="handleIdCardUpload"
              :on-remove="onIdCardRemove"
              :file-list="idCardFiles"
              multiple
              accept=".jpg,.jpeg,.png,.pdf"
            >
              <el-button type="primary" plain>选择文件</el-button>
            </el-upload>
            <div class="upload-tip">支持图片和PDF格式，可多选</div>
          </div>
        </div>

        <!-- 健康证 -->
        <div class="upload-section">
          <div class="upload-label">健康证</div>
          <div class="upload-area">
            <el-upload
              ref="healthCertUpload"
              :before-upload="beforeHealthCertUpload"
              :http-request="handleHealthCertUpload"
              :on-remove="onHealthCertRemove"
              :file-list="healthCertFiles"
              accept=".jpg,.jpeg,.png,.pdf"
            >
              <el-button type="primary" plain>选择文件</el-button>
            </el-upload>
            <div class="upload-tip">支持图片和PDF格式</div>
          </div>
        </div>

        <!-- 专业技能证书 -->
        <div class="upload-section">
          <div class="upload-label">专业技能证书 (可多选)</div>
          <div class="upload-area">
            <el-upload
              ref="skillCertUpload"
              :before-upload="beforeSkillCertUpload"
              :http-request="handleSkillCertUpload"
              :on-remove="onSkillCertRemove"
              :file-list="skillCertFiles"
              multiple
              accept=".jpg,.jpeg,.png,.pdf"
            >
              <el-button type="primary" plain>选择文件</el-button>
            </el-upload>
            <div class="upload-tip">支持图片和PDF格式，可多选</div>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"> 保存 </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, onMounted } from 'vue'
import type { FormInstance, UploadProps } from 'element-plus'
import { ElMessage } from 'element-plus'
import { createPractitioner } from '@/api/mall/employment/practitioner'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { updateFile } from '@/api/infra/file'

const props = defineProps<{
  visible: boolean
  agencyName: string
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 表单引用
const formRef = ref<FormInstance>()

// 提交状态
const submitting = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  phone: '',
  idCard: '',
  hometown: '',
  serviceType: '',
  experienceYears: '',
  platformStatus: 'cooperating',
  rating: 4.5
})

// 下拉选项数据
const serviceTypeOptions = ref<any[]>([])
const platformStatusOptions = ref<any[]>([])

// 文件列表
const idCardFiles = ref<any[]>([])
const healthCertFiles = ref<any[]>([])
const skillCertFiles = ref<any[]>([])

// 上传配置 - 使用自定义上传方法

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入阿姨姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /^\d{17}[\dXx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
  ],
  hometown: [{ required: true, message: '请输入籍贯', trigger: 'blur' }],
  serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
  experienceYears: [
    { required: true, message: '请输入从业年限', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入数字', trigger: 'blur' }
  ],
  platformStatus: [{ required: true, message: '请选择平台状态', trigger: 'change' }],
  rating: [{ required: true, message: '请输入评级', trigger: 'blur' }]
}

/** 加载数据字典选项 */
const loadDictOptions = async () => {
  try {
    console.log('[AddPractitioner] 开始加载数据字典选项')

    // 加载服务类型选项
    console.log('[AddPractitioner] 加载服务类型选项')
    const serviceTypeRes = await getDictDataPage({
      dictType: 'service_type',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    console.log('[AddPractitioner] 服务类型响应:', serviceTypeRes)

    serviceTypeOptions.value = (serviceTypeRes.data?.list || serviceTypeRes.list || []).map(
      (item: any) => ({
        label: item.label,
        value: item.value
      })
    )

    // 如果数据字典为空，使用临时硬编码选项
    if (serviceTypeOptions.value.length === 0) {
      console.log('[AddPractitioner] 服务类型数据字典为空，使用临时选项')
      serviceTypeOptions.value = [
        { label: '月嫂', value: '月嫂' },
        { label: '育儿嫂', value: '育儿嫂' },
        { label: '保洁', value: '保洁' },
        { label: '护工', value: '护工' }
      ]
    }
    console.log('[AddPractitioner] 服务类型选项:', serviceTypeOptions.value)

    // 加载平台状态选项
    console.log('[AddPractitioner] 加载平台状态选项')
    const platformStatusRes = await getDictDataPage({
      dictType: 'platform_status',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    console.log('[AddPractitioner] 平台状态响应:', platformStatusRes)

    platformStatusOptions.value = (
      platformStatusRes.data?.list ||
      platformStatusRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    // 如果数据字典为空，使用临时硬编码选项
    if (platformStatusOptions.value.length === 0) {
      console.log('[AddPractitioner] 平台状态数据字典为空，使用临时选项')
      platformStatusOptions.value = [
        { label: '合作中', value: 'cooperating' },
        { label: '已解约', value: 'terminated' },
        { label: '待审核', value: 'pending' }
      ]
    }
    console.log('[AddPractitioner] 平台状态选项:', platformStatusOptions.value)
  } catch (error) {
    console.error('[AddPractitioner] 加载数据字典失败:', error)

    // 出错时使用临时硬编码选项
    serviceTypeOptions.value = [
      { label: '月嫂', value: '月嫂' },
      { label: '育儿嫂', value: '育儿嫂' },
      { label: '保洁', value: '保洁' },
      { label: '护工', value: '护工' }
    ]

    platformStatusOptions.value = [
      { label: '合作中', value: 'cooperating' },
      { label: '已解约', value: 'terminated' },
      { label: '待审核', value: 'pending' }
    ]
  }
}

// 身份证上传
const beforeIdCardUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
  if (!isValidType) {
    ElMessage.error('只能上传图片或PDF格式文件')
    return false
  }
  return true
}

const handleIdCardUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      const file = options.file
      file.url = response.data
      idCardFiles.value.push(file)
      ElMessage.success('身份证文件上传成功')
    }
  } catch (error) {
    console.error('身份证文件上传失败:', error)
    ElMessage.error('身份证文件上传失败')
  }
}

const onIdCardRemove = (file: any) => {
  const index = idCardFiles.value.findIndex((item) => item.uid === file.uid)
  if (index > -1) {
    idCardFiles.value.splice(index, 1)
  }
}

// 健康证上传
const beforeHealthCertUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
  if (!isValidType) {
    ElMessage.error('只能上传图片或PDF格式文件')
    return false
  }
  return true
}

const handleHealthCertUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      const file = options.file
      file.url = response.data
      healthCertFiles.value.push(file)
      ElMessage.success('健康证文件上传成功')
    }
  } catch (error) {
    console.error('健康证文件上传失败:', error)
    ElMessage.error('健康证文件上传失败')
  }
}

const onHealthCertRemove = (file: any) => {
  const index = healthCertFiles.value.findIndex((item) => item.uid === file.uid)
  if (index > -1) {
    healthCertFiles.value.splice(index, 1)
  }
}

// 技能证书上传
const beforeSkillCertUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
  if (!isValidType) {
    ElMessage.error('只能上传图片或PDF格式文件')
    return false
  }
  return true
}

const handleSkillCertUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      const file = options.file
      file.url = response.data
      skillCertFiles.value.push(file)
      ElMessage.success('技能证书文件上传成功')
    }
  } catch (error) {
    console.error('技能证书文件上传失败:', error)
    ElMessage.error('技能证书文件上传失败')
  }
}

const onSkillCertRemove = (file: any) => {
  const index = skillCertFiles.value.findIndex((item) => item.uid === file.uid)
  if (index > -1) {
    skillCertFiles.value.splice(index, 1)
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  idCardFiles.value = []
  healthCertFiles.value = []
  skillCertFiles.value = []
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构建资质文件列表
    const qualifications = []

    // 身份证文件
    idCardFiles.value.forEach((file) => {
      qualifications.push({
        fileType: 'id_card',
        fileName: file.name,
        fileUrl: file.url,
        fileSize: file.size,
        fileExtension: file.name.split('.').pop(),
        sortOrder: 0,
        status: 1
      })
    })

    // 健康证文件
    healthCertFiles.value.forEach((file) => {
      qualifications.push({
        fileType: 'health_cert',
        fileName: file.name,
        fileUrl: file.url,
        fileSize: file.size,
        fileExtension: file.name.split('.').pop(),
        sortOrder: 0,
        status: 1
      })
    })

    // 技能证书文件
    skillCertFiles.value.forEach((file) => {
      qualifications.push({
        fileType: 'skill_cert',
        fileName: file.name,
        fileUrl: file.url,
        fileSize: file.size,
        fileExtension: file.name.split('.').pop(),
        sortOrder: 0,
        status: 1
      })
    })

    const data = {
      ...formData,
      experienceYears: parseInt(formData.experienceYears),
      qualifications
    }

    await createPractitioner(data)
    emit('success')
    handleClose()
  } catch (error) {
    console.error('新增阿姨失败:', error)
    ElMessage.error('新增阿姨失败')
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(() => {
  loadDictOptions()
})
</script>

<style scoped lang="scss">
.add-practitioner-form {
  .form-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
  }

  .upload-section {
    margin-bottom: 20px;

    .upload-label {
      font-weight: 500;
      margin-bottom: 10px;
      color: #333;
    }

    .upload-area {
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      padding: 20px;
      text-align: center;
      background: #fafafa;

      &:hover {
        border-color: #409eff;
      }

      .upload-tip {
        margin-top: 10px;
        color: #666;
        font-size: 12px;
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
