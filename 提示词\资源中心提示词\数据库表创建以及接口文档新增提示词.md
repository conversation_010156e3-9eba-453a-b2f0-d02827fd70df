


现在需要根据页面内容新增表结构
遵循规范：
1. 每个表需要包含以下几个字段：
`deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
`creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
2. 表名称前缀必须用 publicbiz_

先创建合作伙伴的表结构，严格参照页面字段设计表结构，避免缺失字段导致业务不完整，将可执行的mysql语句写入文件：提示词\资源中心提示词\资源中心相关数据库表结构.md。
前端页面路径如下：
合作伙伴首页：src\views\infra\ResourceCenter\index.vue
合作伙伴编辑和新增页面：src\views\infra\ResourceCenter\TeacherDatabase\components\AddTeacher.vue
表创建过程中请注意操作日志需要单独创建一个表，后续统一师资库、数字资产等其它业务也需要用到操作日志。所以需要加类型字段区分，日志相关页面路径如下：
1. 合作伙伴操作日志页面：src\views\infra\ResourceCenter\components\OptLog.vue
2. 统一师资库操作日志页面：src\views\infra\ResourceCenter\TeacherDatabase\components\TeacherOptLog.vue
3.考题管理操作日志页面路径：src\views\infra\ResourceCenter\QuestionManagement\components\QuestionOptLog.vue
4.场地资源管理操作日志页面路径：src\views\infra\ResourceCenter\SiteManagement\components\SiteOptLog.vue



