<!--
  页面名称：任务管理
  功能描述：展示客户服务与保障工单，支持筛选、分页、处理等操作
-->
<template>
  <div class="task-management">
    <!-- 工单类型标签页 -->
    <el-tabs v-model="activeTaskType" class="task-type-tabs" @tab-change="handleTabChange">
      <el-tab-pane name="all">
        <template #label>
          <span class="tab-label">全部工单</span>
          <span class="tab-badge">{{ taskTypeStats.all || 0 }}</span>
        </template>
      </el-tab-pane>
      <el-tab-pane name="complaint">
        <template #label>
          <span class="tab-label">投诉</span>
          <span class="tab-badge">{{ taskTypeStats.complaint || 0 }}</span>
        </template>
      </el-tab-pane>
      <el-tab-pane name="substitution_request">
        <template #label>
          <span class="tab-label">换人申请</span>
          <span class="tab-badge">{{ taskTypeStats.substitution_request || 0 }}</span>
        </template>
      </el-tab-pane>
      <el-tab-pane name="take_leave">
        <template #label>
          <span class="tab-label">请假/顶岗/调休</span>
          <span class="tab-badge">{{ taskTypeStats.take_leave || 0 }}</span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 筛选栏 -->
    <el-form :inline="true" :model="searchForm" class="search-form" @submit.prevent>
      <el-form-item label="工单状态：">
        <el-select
          v-model="searchForm.workOrderStatus"
          placeholder="全部"
          clearable
          style="width: 120px"
        >
          <el-option label="全部" :value="''" />
          <el-option label="待处理" value="pending" />
          <el-option label="处理中" value="processing" />
          <el-option label="已解决" value="resolved" />
          <el-option label="已关闭" value="closed" />
        </el-select>
      </el-form-item>
      <el-form-item label="紧急程度：">
        <el-select v-model="searchForm.priority" placeholder="全部" clearable style="width: 120px">
          <el-option label="全部" :value="''" />
          <el-option label="紧急" value="urgent" />
          <el-option label="高" value="high" />
          <el-option label="中" value="medium" />
          <el-option label="低" value="low" />
        </el-select>
      </el-form-item>
      <el-form-item label="关联机构：">
        <el-input
          v-model="searchForm.agencyName"
          placeholder="输入机构名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工单列表 -->
    <div class="task-list">
      <div class="list-header">
        <div class="list-cell">工单号</div>
        <div class="list-cell">工单类型</div>
        <div class="list-cell">关联订单/阿姨</div>
        <div class="list-cell">申请方</div>
        <div class="list-cell">关联机构</div>
        <div class="list-cell">紧急度</div>
        <div class="list-cell">工单状态</div>
        <div class="list-cell">创建时间</div>
        <div class="list-cell">处理人</div>
        <div class="list-cell">操作</div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-wrapper">
        <div class="loading-spinner">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
      </div>

      <div v-else-if="tableData.length === 0" class="empty-wrapper">
        <div class="empty-content">
          <el-icon class="empty-icon"><Document /></el-icon>
          <span>暂无工单数据</span>
        </div>
      </div>

      <div v-else class="list-body">
        <div v-for="task in tableData" :key="task.id" class="list-row">
          <div class="list-cell">
            <small>{{ task.workOrderNo }}</small>
          </div>
          <div class="list-cell">
            <el-tag :type="getTaskTypeTag(task.workOrderType)" size="small">
              {{ getTaskTypeText(task.workOrderType) }}
            </el-tag>
          </div>
          <div class="list-cell">
            <strong>{{ task.orderNo }}</strong>
            <br />
            <small v-if="task.auntName">被投诉方: {{ task.auntName }}</small>
          </div>
          <div class="list-cell">{{ task.complainerName || '-' }}</div>
          <div class="list-cell">{{ task.agencyName }}</div>
          <div class="list-cell">
            <el-tag :type="getPriorityTag(task.priority)" size="small">
              {{ getPriorityText(task.priority) }}
            </el-tag>
          </div>
          <div class="list-cell">
            <el-tag :type="getStatusTag(task.workOrderStatus)" size="small">
              {{ getStatusText(task.workOrderStatus) }}
            </el-tag>
          </div>
          <div class="list-cell">{{ formatDate(task.createTime) }}</div>
          <div class="list-cell">{{ task.assigneeName || '-' }}</div>
          <div class="list-cell">
            <div class="operation-buttons">
              <!-- 根据工单状态显示不同的按钮 -->
              <el-button
                v-if="task.workOrderStatus === 'processing' && task.assigneeName"
                size="small"
                type="primary"
                @click="handleInProgress(task)"
              >
                处理中
              </el-button>
              <el-button
                v-if="task.workOrderStatus === 'pending'"
                size="small"
                type="primary"
                @click="acceptOrder(task)"
              >
                接单
              </el-button>
              <el-button size="small" @click="viewTaskDetail(task)">查看</el-button>
              <el-button size="small" @click="reassignTask(task)">转派</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.pageNo"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 工单详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      :title="`工单详情: ${currentTask?.workOrderNo || ''}`"
      direction="rtl"
      size="600px"
      :before-close="closeDetailDrawer"
    >
      <OrderDetail
        v-if="detailDrawerVisible && currentTask && currentTask.workOrderType === 'complaint'"
        :task="currentTask"
        @close="closeDetailDrawer"
      />
      <ReplaceOrderDetail
        v-if="
          detailDrawerVisible &&
          currentTask &&
          (currentTask.workOrderType === 'substitution_request' ||
            currentTask.workOrderType === 'take_leave' ||
            currentTask.workOrderType === 'leave_adjustment')
        "
        :task="currentTask"
        @close="closeDetailDrawer"
      />
    </el-drawer>

    <!-- 转派工单对话框 -->
    <TransferOrder ref="transferOrderRef" @success="handleTransferSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElIcon } from 'element-plus'
import { Loading, Document } from '@element-plus/icons-vue'
import {
  getWorkOrderPage,
  getWorkOrderTypeStats,
  acceptWorkOrder,
  type WorkOrder
} from '@/api/mall/employment/task'
import OrderDetail from './agency/OrderDetail.vue'
import ReplaceOrderDetail from './agency/ReplaceOrderDetail.vue'
import TransferOrder from './agency/TransferOrder.vue'

/** 当前激活的工单类型 */
const activeTaskType = ref('all')

/** 任务类型统计 */
const taskTypeStats = reactive({
  all: 0,
  complaint: 0,
  substitution_request: 0,
  take_leave: 0,
  leave_adjustment: 0
})

/** 搜索表单数据 */
const searchForm = reactive({
  workOrderStatus: '',
  priority: '',
  agencyName: ''
})

/** 表格数据 */
const tableData = ref<WorkOrder[]>([])

/** 分页信息 */
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

/** 加载状态 */
const loading = ref(false)

/** 工单详情抽屉相关 */
const detailDrawerVisible = ref(false)
const currentTask = ref<WorkOrder | null>(null)

/** 转派对话框相关 */
const transferOrderRef = ref()

/** 获取任务类型统计 */
const fetchTaskTypeStats = async () => {
  try {
    const res = await getWorkOrderTypeStats()
    taskTypeStats.all = res.all || 0
    taskTypeStats.complaint = res.complaint || 0
    taskTypeStats.substitution_request = res.substitutionRequest || 0
    taskTypeStats.take_leave = res.takeLeave || 0
    taskTypeStats.leave_adjustment = res.leave_adjustment || 0
  } catch (error) {
    console.error('获取任务类型统计失败:', error)
    ElMessage.error('获取任务类型统计失败')
  }
}

/** 获取工单列表 */
const fetchList = async () => {
  try {
    loading.value = true
    
    // 处理工单类型参数
    let workOrderType = ''
    if (activeTaskType.value === 'take_leave') {
      // 请假/顶岗标签页查询两种类型：take_leave 和 leave_adjustment
      workOrderType = 'take_leave,leave_adjustment'
    } else if (activeTaskType.value !== 'all') {
      workOrderType = activeTaskType.value
    }
    
    const params = {
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      workOrderType: workOrderType,
      ...searchForm
    }

    console.log('查询参数:', params)

    const res: any = await getWorkOrderPage(params)
    // 兼容不同返回形态：res 可能是 {list,total} 或 {data: { list, total }} 或 纯数组
    const rawList = Array.isArray(res)
      ? res
      : res?.list || res?.data?.list || res?.records || res?.data || []
    pagination.total = (res?.total ?? res?.data?.total ?? 0) as number
    if (res?.pageNo) pagination.pageNo = res.pageNo
    if (res?.pageSize) pagination.pageSize = res.pageSize

    tableData.value = (rawList || []).map((item: any) => {
      return {
        id: item.id,
        workOrderNo: item.workOrderNo || item.work_order_no || '-',
        orderNo: item.orderNo || item.order_no || '-',
        workOrderTitle: item.workOrderTitle || item.work_order_title || item.title || '-',
        workOrderType: item.workOrderType || item.work_order_type || 'other',
        priority: item.priority || 'low',
        workOrderStatus: item.workOrderStatus || item.work_order_status || 'pending',
        assigneeId: item.assigneeId || item.assignee_id,
        assigneeName: item.assigneeName || item.assignee_name || '-',
        auntName: item.auntName || item.aunt_name || '-',
        complainerName: item.complainerName || item.complainer_name || '-',
        agencyId: item.agencyId || item.agency_id,
        agencyName: item.agencyName || item.agency_name || '-',
        remark: item.remark || '-',
        createTime: item.createTime || item.create_time || item.createTime || ''
      }
    })
  } catch (error) {
    console.error('获取工单列表失败:', error)
    ElMessage.error('获取工单列表失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 搜索 */
const onSearch = () => {
  pagination.pageNo = 1
  fetchList()
}

/** 重置 */
const onReset = () => {
  Object.assign(searchForm, {
    workOrderStatus: '',
    priority: '',
    agencyName: ''
  })
  pagination.pageNo = 1
  fetchList()
}

/** 查看工单详情 */
const viewTaskDetail = (task: WorkOrder) => {
  currentTask.value = task
  detailDrawerVisible.value = true
}

/** 处理中 */
const handleInProgress = (task: WorkOrder) => {
  console.log('处理中:', task)
}

/** 接单 */
const acceptOrder = async (task: WorkOrder) => {
  try {
    // 这里需要获取当前登录用户信息
    const currentUser = {
      id: 1001, // 临时写死，实际应该从用户状态获取
      name: '当前用户' // 临时写死，实际应该从用户状态获取
    }

    const params = {
      workOrderId: task.id,
      assigneeId: currentUser.id,
      assigneeName: currentUser.name,
      acceptRemark: '已接单，开始处理'
    }

    const res = await acceptWorkOrder(params)
    if (res.data.success) {
      ElMessage.success('接单成功')
      // 刷新列表
      fetchList()
      // 刷新任务类型统计
      fetchTaskTypeStats()
    } else {
      ElMessage.error(res.message || '接单失败')
    }
  } catch (error) {
    console.error('接单失败:', error)
    ElMessage.error('接单失败，请重试')
  }
}

/** 转派 */
const reassignTask = (task: WorkOrder) => {
  console.log('开始转派工单:', task)
  
  // 使用工单编码传递，而不是整个task对象
  const taskInfo = {
    taskNo: task.workOrderNo,
    id: task.id
  }
  
  console.log('传递给转派组件的工单信息:', taskInfo)
  transferOrderRef.value?.open(taskInfo)
}

/** 转派成功处理 */
const handleTransferSuccess = (data: any) => {
  console.log('转派成功:', data)
  // 可以在这里刷新列表或更新当前工单状态
  fetchList()
}

/** 关闭详情抽屉 */
const closeDetailDrawer = () => {
  detailDrawerVisible.value = false
  currentTask.value = null
}

/** 获取工单类型标签 */
const getTaskTypeTag = (type: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    complaint: 'danger',
    substitution_request: 'warning',
    take_leave: 'info',
    leave_adjustment: 'info',
    other: 'info'
  }
  return typeMap[type] || 'info'
}

/** 获取工单类型文本 */
const getTaskTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    complaint: '投诉',
    substitution_request: '换人申请',
    take_leave: '请假/顶岗',
    leave_adjustment: '调休'
  }
  return typeMap[type] || '其他'
}

/** 获取紧急度标签 */
const getPriorityTag = (priority: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const priorityMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    high: 'danger',
    medium: 'warning',
    low: 'info',
    urgent: 'danger'
  }
  return priorityMap[priority] || 'info'
}

/** 获取紧急度文本 */
const getPriorityText = (priority: string) => {
  const priorityMap: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低',
    urgent: '紧急'
  }
  return priorityMap[priority] || '低'
}

/** 获取状态标签 */
const getStatusTag = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info',
    approved: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取状态文本 */
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭',
    approved: '已批准',
    rejected: '已驳回'
  }
  return statusMap[status] || '待处理'
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

/** 分页大小改变 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageNo = 1
  fetchList()
}

/** 当前页改变 */
const handleCurrentChange = (page: number) => {
  pagination.pageNo = page
  fetchList()
}

/** 标签页切换 */
const handleTabChange = () => {
  pagination.pageNo = 1
  fetchList()
}

onMounted(() => {
  fetchTaskTypeStats()
  fetchList()
})
</script>

<style scoped lang="scss">
.task-management {
  .task-type-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .tab-label {
    margin-right: 8px;
  }

  .tab-badge {
    background: #f56c6c;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 500;
    min-width: 16px;
    text-align: center;
    display: inline-block;
  }

  .search-form {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .task-list {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    margin-bottom: 20px;
  }

  .list-header,
  .list-row {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s;
  }

  .list-row:last-child {
    border-bottom: none;
  }

  .list-header {
    background: #f8f9fa;
    font-weight: 600;
    font-size: 14px;
    color: #343a40;
  }

  .list-row:hover {
    background: #f5f7fa;
  }

  .list-cell {
    padding: 0 10px;
    flex: 1;
  }

  .list-cell:first-child {
    flex: 0 0 120px;
  }
  .list-cell:nth-child(2) {
    flex: 0 0 90px;
  }
  .list-cell:nth-child(3) {
    flex: 1 1 15%;
  }
  .list-cell:nth-child(4) {
    flex: 1 1 15%;
  }
  .list-cell:nth-child(5) {
    flex: 1 1 18%;
  }
  .list-cell:nth-child(6) {
    flex: 0 0 80px;
  }
  .list-cell:nth-child(7) {
    flex: 0 0 90px;
  }
  .list-cell:nth-child(8) {
    flex: 1 1 15%;
  }
  .list-cell:nth-child(9) {
    flex: 1 1 12%;
  }
  .list-cell:last-child {
    flex: 0 0 200px;
    text-align: right;
  }

  .loading-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    padding: 40px 0;
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #909399;
    font-size: 14px;

    .el-icon {
      font-size: 24px;
      color: #409eff;
    }
  }

  .empty-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    padding: 40px 0;
  }

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: #909399;
    font-size: 14px;

    .empty-icon {
      font-size: 40px;
      color: #c0c4cc;
    }
  }

  .operation-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;

    .el-button {
      margin: 0;

      &.el-button--primary {
        background: #409eff;
        border-color: #409eff;
        color: white;
      }

      &:not(.el-button--primary) {
        background: white;
        border-color: #d9d9d9;
        color: #666;

        &:hover {
          border-color: #409eff;
          color: #409eff;
        }
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
  }

  // 自定义抽屉标题样式
  :deep(.el-drawer__header) {
    .el-drawer__title {
      font-size: 20px;
      color: #333;
      font-weight: 600;
    }
  }
}
</style>
