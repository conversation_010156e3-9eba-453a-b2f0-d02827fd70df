<!--
  页面名称：对账确认弹窗
  功能描述：对账单确认对账，支持查看原始分成信息、调整分成比例、确认对账等操作
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="对账确认"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="reconciliation-confirm-dialog"
  >
    <div class="dialog-content">
      <!-- 对账单信息 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          <span>对账单信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>对账单号：</label>
            <span>{{ reconciliationData?.statementNo }}</span>
          </div>
          <div class="info-item">
            <label>机构名称：</label>
            <span>{{ reconciliationData?.agencyName }}</span>
          </div>
          <div class="info-item">
            <label>生成时间：</label>
            <span>{{ formatDateTime(reconciliationData?.generationTime) }}</span>
          </div>
          <div class="info-item">
            <label>订单数量：</label>
            <span>{{ reconciliationData?.orderCount }}个订单</span>
          </div>
        </div>
      </div>

      <!-- 原始分成信息 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Money /></el-icon>
          <span>原始分成信息</span>
        </div>
        <div class="split-info">
          <div class="split-item">
            <label>机构分成比例：</label>
            <span class="ratio-text">{{ reconciliationData?.agencyRatio }}%</span>
          </div>
          <div class="split-item">
            <label>平台分成比例：</label>
            <span class="ratio-text">{{ reconciliationData?.platformRatio }}%</span>
          </div>
          <div class="split-item">
            <label>机构分成金额：</label>
            <span class="amount-text agency-amount"
              >¥{{ formatAmount(reconciliationData?.agencyAmount) }}</span
            >
          </div>
          <div class="split-item">
            <label>平台分成金额：</label>
            <span class="amount-text platform-amount"
              >¥{{ formatAmount(reconciliationData?.platformAmount) }}</span
            >
          </div>
        </div>
      </div>

      <!-- 分成调整 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Edit /></el-icon>
          <span>分成调整</span>
        </div>
        <div class="adjustment-section">
          <el-checkbox v-model="enableAdjustment" @change="handleAdjustmentChange">
            启用手工调整
          </el-checkbox>

          <div v-if="enableAdjustment" class="adjustment-inputs">
            <el-form :model="adjustmentForm" label-width="180px">
              <el-form-item label="调整后机构分成金额：">
                <el-input
                  v-model="adjustmentForm.agencyAmount"
                  type="number"
                  placeholder="请输入调整后机构分成金额"
                  :disabled="!enableAdjustment"
                  style="width: 200px"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
              <el-form-item label="调整后平台分成金额：">
                <el-input
                  v-model="adjustmentForm.platformAmount"
                  type="number"
                  placeholder="请输入调整后平台分成金额"
                  :disabled="!enableAdjustment"
                  style="width: 200px"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
              <el-form-item label="调整原因：" required>
                <el-input
                  v-model="adjustmentForm.reason"
                  type="textarea"
                  :rows="3"
                  placeholder="请详细说明调整原因..."
                  :disabled="!enableAdjustment"
                  style="width: 100%"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="confirmLoading">
          确认对账
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Money, Edit } from '@element-plus/icons-vue'
import { confirmReconciliation } from '@/api/settlement/reconciliation'

/** 弹窗显示状态 */
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

/** 确认加载状态 */
const confirmLoading = ref(false)

/** 启用调整状态 */
const enableAdjustment = ref(false)

/** 调整表单数据 */
const adjustmentForm = reactive({
  agencyAmount: '',
  platformAmount: '',
  reason: ''
})

/** 组件属性 */
const props = defineProps<{
  visible: boolean
  reconciliationData: any
}>()

/** 组件事件 */
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

/** 监听对账单数据变化，初始化表单 */
watch(
  () => props.reconciliationData,
  (newData) => {
    if (newData) {
      adjustmentForm.agencyAmount = newData.agencyAmount?.toString() || ''
      adjustmentForm.platformAmount = newData.platformAmount?.toString() || ''
      adjustmentForm.reason = ''
      enableAdjustment.value = false
    }
  },
  { immediate: true }
)

/** 处理调整状态变化 */
const handleAdjustmentChange = (checked: boolean) => {
  if (!checked) {
    // 重置为原始值
    adjustmentForm.agencyAmount = props.reconciliationData?.agencyAmount?.toString() || ''
    adjustmentForm.platformAmount = props.reconciliationData?.platformAmount?.toString() || ''
    adjustmentForm.reason = ''
  }
}

/** 处理取消 */
const handleCancel = () => {
  dialogVisible.value = false
}

/** 处理确认对账 */
const handleConfirm = async () => {
  if (enableAdjustment.value && !adjustmentForm.reason.trim()) {
    ElMessage.warning('请输入调整原因')
    return
  }

  if (enableAdjustment.value) {
    const agencyAmount = parseFloat(adjustmentForm.agencyAmount)
    const platformAmount = parseFloat(adjustmentForm.platformAmount)

    if (isNaN(agencyAmount) || isNaN(platformAmount)) {
      ElMessage.warning('请输入有效的金额')
      return
    }

    const total = agencyAmount + platformAmount
    const originalTotal = props.reconciliationData?.totalAmount || 0

    if (Math.abs(total - originalTotal) > 0.01) {
      ElMessage.warning('调整后金额总和必须等于对账总金额')
      return
    }
  }

  confirmLoading.value = true
  try {
    const params = {
      statementNo: props.reconciliationData?.statementNo,
      enableAdjustment: enableAdjustment.value,
      adjustedAgencyAmount: enableAdjustment.value
        ? parseFloat(adjustmentForm.agencyAmount)
        : undefined,
      adjustedPlatformAmount: enableAdjustment.value
        ? parseFloat(adjustmentForm.platformAmount)
        : undefined,
      adjustmentReason: enableAdjustment.value ? adjustmentForm.reason : undefined
    }

    await confirmReconciliation(params)
    emit('success')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('确认对账失败')
    console.error('确认对账失败:', error)
  } finally {
    confirmLoading.value = false
  }
}

/** 格式化日期时间 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount?.toFixed(2) || '0.00'
}
</script>

<style scoped lang="scss">
.reconciliation-confirm-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: white;
    }
  }

  .dialog-content {
    .info-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      background: #fafafa;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-weight: 600;
        color: #303133;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 80px;
          }

          span {
            color: #303133;
          }
        }
      }

      .split-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;

        .split-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 120px;
          }

          .ratio-text {
            color: #409eff;
            font-weight: 600;
          }

          .amount-text {
            font-weight: 600;

            &.agency-amount {
              color: #67c23a;
            }

            &.platform-amount {
              color: #409eff;
            }
          }
        }
      }

      .adjustment-section {
        .adjustment-inputs {
          margin-top: 16px;
          padding-top: 16px;
          border-top: 1px solid #e4e7ed;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>




