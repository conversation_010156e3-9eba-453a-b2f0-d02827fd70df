<template>
  <div class="communication-detail">
    <!-- 沟通记录 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-comments section-icon"></i>
        <span class="section-title">沟通记录</span>
      </div>
      <div class="section-content">
        <div class="info-grid">
          <div class="info-item">
            <span class="label">记录编号:</span>
            <span class="value">{{ record.recordId || 'COM001' }}</span>
          </div>
          <div class="info-item">
            <span class="label">记录日期:</span>
            <span class="value">{{ record.date }}</span>
          </div>
          <div class="info-item">
            <span class="label">记录人:</span>
            <span class="value">{{ record.recorder }}</span>
          </div>
          <div class="info-item">
            <span class="label">记录时间:</span>
            <span class="value">{{ record.recordTime || `${record.date} 14:30:00` }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 记录内容 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-file-alt section-icon"></i>
        <span class="section-title">记录内容</span>
      </div>
      <div class="section-content">
        <div class="content-item">
          <span class="label">标题:</span>
          <el-input :model-value="record.title" readonly class="readonly-input" />
        </div>
        <div class="content-item">
          <span class="label">沟通纪要:</span>
          <el-input
            :model-value="record.summary"
            type="textarea"
            :rows="6"
            readonly
            class="readonly-input"
          />
        </div>
      </div>
    </div>

    <!-- 参与人员 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-users section-icon"></i>
        <span class="section-title">参与人员</span>
      </div>
      <div class="section-content">
        <div class="participants-info">
          <div class="participant-item">
            <span class="label">参与人员:</span>
            <span class="value">{{ record.participants }}</span>
          </div>
          <div v-if="record.todo" class="participant-item">
            <span class="label">待办事项:</span>
            <span class="value">{{ record.todo }}</span>
          </div>
          <div v-if="record.nextFollowUp" class="participant-item">
            <span class="label">下次跟进:</span>
            <span class="value">{{ record.nextFollowUp }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 附件材料 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-paperclip section-icon"></i>
        <span class="section-title">附件材料</span>
      </div>
      <div class="section-content">
        <div class="attachments-grid">
          <div
            class="attachment-item"
            v-for="attachment in record.attachments"
            :key="attachment.id"
          >
            <div class="attachment-icon">
              <i :class="getFileIcon(attachment.type)" class="file-icon"></i>
            </div>
            <div class="attachment-info">
              <div class="file-name">{{ attachment.name }}</div>
              <div class="file-meta">
                <span class="file-size">{{ attachment.size }}</span>
                <span class="file-date">{{ attachment.date }}</span>
              </div>
            </div>
            <div class="attachment-actions">
              <el-button type="text" size="small" @click="previewFile(attachment)">
                <i class="fas fa-eye"></i> 预览
              </el-button>
              <el-button type="text" size="small" @click="downloadFile(attachment)">
                <i class="fas fa-download"></i> 下载
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 跟进记录 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-history section-icon"></i>
        <span class="section-title">跟进记录</span>
      </div>
      <div class="section-content">
        <div class="follow-up-item" v-for="followUp in record.followUps" :key="followUp.id">
          <div class="follow-up-header">
            <span class="follow-up-title">{{ followUp.title }}</span>
            <span class="follow-up-time">{{ followUp.time }}</span>
          </div>
          <div class="follow-up-description">{{ followUp.description }}</div>
          <div class="follow-up-operator">操作人: {{ followUp.operator }}</div>
        </div>
      </div>
    </div>

    <!-- 备注信息 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-sticky-note section-icon"></i>
        <span class="section-title">备注信息</span>
      </div>
      <div class="section-content">
        <div class="remark-item">
          <div class="remark-subtitle">内部备注</div>
          <div class="remark-content">{{ record.internalRemark }}</div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="detail-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button @click="handleEdit"> <i class="fas fa-edit"></i> 编辑记录 </el-button>
      <el-button type="primary" @click="handleAddFollowUp">
        <i class="fas fa-plus"></i> 添加跟进
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const props = defineProps<{
  record: any
}>()

const emit = defineEmits<{
  close: []
}>()

// 获取文件图标
const getFileIcon = (fileType: string) => {
  const iconMap: Record<string, string> = {
    pdf: 'fas fa-file-pdf',
    xls: 'fas fa-file-excel',
    doc: 'fas fa-file-word',
    mp3: 'fas fa-file-audio',
    jpg: 'fas fa-file-image',
    png: 'fas fa-file-image'
  }
  return iconMap[fileType] || 'fas fa-file'
}

// 预览文件
const previewFile = (file: any) => {
  console.log('预览文件:', file)
}

// 下载文件
const downloadFile = (file: any) => {
  console.log('下载文件:', file)
}

// 关闭
const handleClose = () => {
  emit('close')
}

// 编辑记录
const handleEdit = () => {
  console.log('编辑记录')
}

// 添加跟进
const handleAddFollowUp = () => {
  console.log('添加跟进')
}

// 模拟附件数据
const mockAttachments = [
  {
    id: 1,
    name: '沟通录音.mp3',
    type: 'mp3',
    size: '2.1MB',
    date: '2024-06-10 14:30:00'
  },
  {
    id: 2,
    name: '会议纪要.docx',
    type: 'doc',
    size: '156KB',
    date: '2024-06-10 15:00:00'
  }
]

// 模拟跟进记录
const mockFollowUps = [
  {
    id: 1,
    title: '跟进协调结果',
    time: '2024-06-12 10:00:00',
    description: '已与相关部门协调，将加派3名阿姨到该机构，预计下周到位。',
    operator: '王经理'
  }
]

// 模拟内部备注
const mockInternalRemark = '该机构合作态度良好，建议继续保持良好关系，可考虑给予更多优质订单分配。'

// 合并数据
const record = computed(() => ({
  ...props.record,
  attachments: mockAttachments,
  followUps: mockFollowUps,
  internalRemark: mockInternalRemark
}))
</script>

<style scoped lang="scss">
.communication-detail {
  padding: 20px;
  height: 100%;
  overflow-y: auto;

  .detail-section {
    margin-bottom: 30px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e9ecef;

      .section-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #409eff;
      }

      .section-title {
        font-weight: 500;
        color: #343a40;
        font-size: 16px;
      }
    }

    .section-content {
      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;

        .info-item {
          display: flex;
          align-items: center;

          .label {
            color: #666;
            min-width: 100px;
            margin-right: 10px;
            font-size: 14px;
          }

          .value {
            color: #333;
            font-size: 14px;
          }
        }
      }

      .content-item {
        margin-bottom: 15px;

        .label {
          display: block;
          color: #666;
          margin-bottom: 5px;
          font-size: 14px;
        }

        .readonly-input {
          .el-input__inner {
            background: #f8f9fa;
            color: #666;
          }
        }
      }

      .participants-info {
        .participant-item {
          display: flex;
          margin-bottom: 10px;
          font-size: 14px;

          .label {
            color: #666;
            min-width: 100px;
            margin-right: 10px;
          }

          .value {
            color: #333;
            flex: 1;
          }
        }
      }

      .attachments-grid {
        .attachment-item {
          display: flex;
          align-items: center;
          padding: 12px;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          margin-bottom: 10px;

          .attachment-icon {
            margin-right: 12px;

            .file-icon {
              font-size: 24px;
              color: #666;
            }
          }

          .attachment-info {
            flex: 1;

            .file-name {
              font-weight: 500;
              color: #333;
              margin-bottom: 4px;
            }

            .file-meta {
              font-size: 12px;
              color: #999;

              .file-size {
                margin-right: 10px;
              }
            }
          }

          .attachment-actions {
            display: flex;
            gap: 8px;

            .el-button {
              padding: 4px 8px;
              font-size: 12px;
            }
          }
        }
      }

      .follow-up-item {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 10px;

        .follow-up-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .follow-up-title {
            font-weight: 500;
            color: #333;
          }

          .follow-up-time {
            font-size: 12px;
            color: #999;
          }
        }

        .follow-up-description {
          color: #666;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .follow-up-operator {
          font-size: 12px;
          color: #999;
        }
      }

      .remark-item {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;

        .remark-subtitle {
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
        }

        .remark-content {
          color: #666;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .detail-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .el-button {
      padding: 8px 16px;

      i {
        margin-right: 4px;
      }
    }
  }
}
</style>
