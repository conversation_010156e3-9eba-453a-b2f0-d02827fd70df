---
description: 
globs: 
alwaysApply: false
---
最新技术栈**：
使用 Vue3、Vite4 等前端前沿技术开发
应用程序级 JavaScript 的语言

# 项目目录结构说明

```
├── build/               # 构建相关配置
├── public/              # 公共资源目录
├── src/                 # 源码目录
│   ├── api/             # 所有后端接口请求
│   ├── assets/          # 静态资源（图片、字体等）
│   ├── components/      # 公共组件
│   ├── config/          # 配置文件
│   ├── directives/      # 自定义指令
│   ├── hooks/           # 通用 hooks
│   ├── layout/          # 布局相关
│   ├── locales/         # 国际化资源
│   ├── plugins/         # 插件（如element-plus、i18n等）
│   ├── router/          # 路由配置
│   ├── store/           # 状态管理（Pinia）
│   ├── styles/          # 全局样式
│   ├── types/           # TypeScript 类型定义
│   ├── utils/           # 工具函数
│   ├── views/           # 业务页面
│   ├── App.vue          # 根组件
│   └── main.ts          # 入口文件
├── index.html           # 主页面
├── package.json         # 项目依赖及脚本
├── tsconfig.json        # TypeScript 配置
├── vite.config.ts       # Vite 配置
└── ...
```

# 技术栈
- **Vue3**：采用 Composition API，支持 <script setup> 语法糖
- **Vite4**：极速开发与构建工具
- **TypeScript**：强类型开发
- **Element Plus**：UI 组件库
- **Pinia**：新一代状态管理
- **Vue Router 4**：路由管理
- **vue-i18n**：国际化
- **UnoCSS**：原子化 CSS
- 其他：iconify、wangeditor、vueuse 等

# Vue3/Vite4 语法规范与最佳实践

## 1. 组件开发
- 推荐使用 `<script setup lang="ts">` 语法糖，简洁高效
- 组件命名采用大驼峰（如：UserSelectForm.vue）
- 通过 `defineProps`、`defineEmits` 定义 props 和事件
- 使用 `ref`、`reactive` 管理响应式数据
- 通过 `defineExpose` 暴露方法给父组件

**示例：**
```vue
<script setup lang="ts">
import { ref, reactive } from 'vue'
const props = defineProps<{ id: number }>()
const emit = defineEmits(['success'])
const formData = reactive({ name: '', age: 0 })
const submit = () => emit('success', formData)
defineExpose({ submit })
</script>
```

## 2. 路由管理
- 使用 `vue-router@4`，推荐使用 `createWebHistory`
- 路由配置集中在 `src/router/` 目录
- 动态路由、权限路由建议分模块管理

**示例：**
```ts
import { createRouter, createWebHistory } from 'vue-router'
const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_PATH),
  routes: [...],
})
export default router
```

## 3. 状态管理
- 使用 Pinia，推荐模块化拆分 store
- 支持持久化插件 pinia-plugin-persistedstate

**示例：**
```ts
import { defineStore } from 'pinia'
export const useUserStore = defineStore('user', {
  state: () => ({ token: '' }),
  actions: { setToken(token: string) { this.token = token } }
})
```

## 4. 表单与校验
- 推荐使用 Element Plus 表单组件
- 表单数据用 `ref` 或 `reactive` 管理
- 校验规则用 `reactive` 定义
- 复杂表单可用自定义 Form 组件（如 src/components/Form）

**示例：**
```vue
<el-form :model="formData" :rules="formRules" ref="formRef">
  <el-form-item label="姓名" prop="name">
    <el-input v-model="formData.name" />
  </el-form-item>
</el-form>
<script setup lang="ts">
import { ref, reactive } from 'vue'
const formData = reactive({ name: '' })
const formRules = reactive({ name: [{ required: true, message: '必填', trigger: 'blur' }] })
const formRef = ref()
</script>
```

## 5. 国际化
- 使用 vue-i18n，所有文案建议通过 $t 访问
- 语言包放在 `src/locales/` 目录

## 6. 样式
- 推荐使用 UnoCSS 原子类
- 全局样式放在 `src/styles/`，支持 SCSS

# 其他规范
- 统一使用 TypeScript
- 目录、文件、变量命名清晰、语义化
- 组件/页面拆分合理，避免大文件
- 公共逻辑抽离为 hooks 或 utils

# 参考
- [Vue3 官方文档](mdc:https:/staging-cn.vuejs.org)
- [Vite 官方文档](mdc:https:/cn.vitejs.dev)
- [Element Plus](mdc:https:/element-plus.org/zh-CN)
- [Pinia](mdc:https:/pinia.vuejs.org)
- [UnoCSS](mdc:https:/uno.antfu.me)