# 个人培训与认证订单模块测试指南

## 测试环境准备

1. 确保项目依赖已安装：`npm install`
2. 启动开发服务器：`npm run dev`
3. 访问个人培训与认证订单页面

## 功能测试清单

### 1. 主页面功能测试

#### 1.1 统计卡片显示

- [ ] 总订单数显示正确（18）
- [ ] 待处理订单显示正确（0）
- [ ] 本月订单金额显示正确（¥2,135,220）
- [ ] 订单完成率显示正确（92.5%）

#### 1.2 搜索筛选功能

- [ ] 订单状态下拉选择正常
- [ ] 支付状态下拉选择正常
- [ ] 类型下拉选择正常
- [ ] 关键词搜索输入正常
- [ ] 搜索按钮点击响应正常
- [ ] 重置按钮清空所有筛选条件

#### 1.3 数据表格显示

- [ ] 表格数据正确显示
- [ ] 订单号可点击查看详情
- [ ] 状态标签颜色正确
- [ ] 分页功能正常
- [ ] 表格加载状态显示

#### 1.4 操作按钮功能

- [ ] 新建个人培训订单按钮可点击
- [ ] 查看按钮打开详情抽屉
- [ ] 编辑按钮打开编辑抽屉
- [ ] 删除按钮弹出确认对话框
- [ ] 操作日志按钮打开日志抽屉

### 2. 新增/编辑订单功能测试

#### 2.1 新增订单

- [ ] 点击新建按钮打开抽屉
- [ ] 抽屉标题显示"新建个人培训订单"
- [ ] 所有表单字段正常显示
- [ ] 必填字段验证正常
- [ ] 表单提交成功
- [ ] 抽屉关闭后列表刷新

#### 2.2 编辑订单

- [ ] 点击编辑按钮打开抽屉
- [ ] 抽屉标题显示"编辑个人培训订单"
- [ ] 表单数据正确回显
- [ ] 修改数据后保存成功
- [ ] 抽屉关闭后列表刷新

#### 2.3 表单验证

- [ ] 学员姓名为空时提示错误
- [ ] 课程项目未选择时提示错误
- [ ] 订单来源未选择时提示错误
- [ ] 订单金额格式错误时提示错误
- [ ] 支付状态未选择时提示错误
- [ ] 学习状态未选择时提示错误

#### 2.4 模块化设计

- [ ] 订单信息模块有浅色背景
- [ ] 合同管理模块有浅色背景
- [ ] 模块之间有清晰分隔

### 3. 查看订单详情功能测试

#### 3.1 基本信息显示

- [ ] 课程标题横幅显示正确
- [ ] 订单号显示正确
- [ ] 学员姓名显示正确
- [ ] 课程名称显示正确
- [ ] 订单金额显示正确
- [ ] 各种状态标签显示正确

#### 3.2 收款信息模块

- [ ] 收款信息模块有绿色边框
- [ ] 收款金额显示正确
- [ ] 收款方式显示正确
- [ ] 收款日期显示正确

#### 3.3 电子协议模块

- [ ] 协议名称显示正确
- [ ] 协议状态标签显示正确
- [ ] 发起签约按钮可点击

#### 3.4 审批流程模块

- [ ] 模块标题显示正确
- [ ] 无审批记录时显示提示

#### 3.5 操作日志模块

- [ ] 操作日志列表显示正确
- [ ] 时间线样式正确
- [ ] 查看完整日志按钮可点击

### 4. 操作日志功能测试

#### 4.1 日志显示

- [ ] 操作日志抽屉正常打开
- [ ] 日志条目按时间顺序显示
- [ ] 操作人信息显示正确
- [ ] 操作时间显示正确
- [ ] 操作内容显示正确

#### 4.2 状态变更显示

- [ ] 状态变更信息正确显示
- [ ] 变更前后状态颜色区分
- [ ] 箭头符号显示正确

#### 4.3 操作标签

- [ ] 不同类型操作有不同颜色标签
- [ ] 标签样式正确

### 5. 交互功能测试

#### 5.1 抽屉交互

- [ ] 抽屉从右侧滑出
- [ ] 抽屉大小合适
- [ ] 关闭按钮可点击
- [ ] 点击遮罩层可关闭
- [ ] ESC键可关闭

#### 5.2 表单交互

- [ ] 输入框获得焦点时样式正确
- [ ] 下拉选择正常展开
- [ ] 单选按钮选择正常
- [ ] 文件上传功能正常
- [ ] 日期选择器正常

#### 5.3 按钮交互

- [ ] 按钮hover效果正常
- [ ] 按钮点击反馈正常
- [ ] 加载状态显示正确

### 6. 响应式设计测试

#### 6.1 不同屏幕尺寸

- [ ] 大屏幕显示正常
- [ ] 中等屏幕显示正常
- [ ] 小屏幕显示正常
- [ ] 移动端显示正常

#### 6.2 滚动功能

- [ ] 内容超出时滚动正常
- [ ] 滚动条样式正确

## 测试数据

### 模拟订单数据

```javascript
{
  id: 1,
  orderNumber: 'PT202406001',
  studentName: '王小明',
  orderType: 'training',
  courseName: '项目管理PMP认证课程',
  orderSource: '线上小程序',
  orderAmount: '4,500',
  orderStatus: 'in_progress',
  paymentStatus: 'paid',
  learningStatus: 'learning',
  registrationTime: '2024-06-10'
}
```

### 测试用例

1. **新增订单测试**: 填写完整信息，验证提交成功
2. **编辑订单测试**: 修改部分信息，验证保存成功
3. **删除订单测试**: 点击删除，验证确认对话框和删除成功
4. **查看详情测试**: 点击查看，验证详情信息正确
5. **操作日志测试**: 点击操作日志，验证日志信息正确

## 常见问题排查

### 1. 抽屉不显示

- 检查visible属性是否正确绑定
- 检查组件是否正确导入
- 检查CSS样式是否正确

### 2. 表单验证不生效

- 检查rules配置是否正确
- 检查formRef是否正确绑定
- 检查表单字段prop是否正确

### 3. 数据不显示

- 检查数据源是否正确
- 检查模板语法是否正确
- 检查计算属性是否正确

### 4. 样式不正确

- 检查SCSS文件是否正确
- 检查CSS类名是否正确
- 检查Element Plus主题是否正确

## 性能测试

### 1. 加载性能

- 页面首次加载时间 < 2秒
- 抽屉打开时间 < 500ms
- 表格数据加载时间 < 1秒

### 2. 交互性能

- 按钮点击响应时间 < 100ms
- 表单验证响应时间 < 200ms
- 滚动流畅度正常

### 3. 内存使用

- 长时间使用无内存泄漏
- 组件销毁时正确清理资源

## 测试报告模板

```
测试日期: _____________
测试人员: _____________
测试环境: _____________

功能测试结果:
□ 通过  □ 失败  □ 部分通过

问题记录:
1. ________________
2. ________________
3. ________________

建议改进:
1. ________________
2. ________________
3. ________________
```

