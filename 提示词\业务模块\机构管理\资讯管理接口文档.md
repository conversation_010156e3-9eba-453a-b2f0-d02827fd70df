# 资讯管理模块API接口文档

## 1. 接口概述

### 1.1 基础信息
- **基础路径**: `/publicbiz/news`
- **数据格式**: `application/json`
- **字符编码**: `UTF-8`

### 1.2 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 1.3 响应状态码说明
- `200`: 操作成功
- `400`: 参数验证错误
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器错误

## 2. 接口列表

### 2.1 分页查询资讯列表

**接口地址**: `POST /publicbiz/news/page`

**功能说明**: 分页查询资讯列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 是 | 页码，从1开始 |
| size | Integer | 是 | 每页大小，最大100 |
| newsTitle | String | 否 | 资讯标题，支持模糊查询 |
| categoryId | Long | 否 | 分类ID |
| status | String | 否 | 状态：draft-草稿/published-已发布/offline-已下架 |
| contentSource | String | 否 | 内容来源：manual-手动编写/material-关联素材库 |
| startTime | String | 否 | 创建时间开始，格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | 否 | 创建时间结束，格式：yyyy-MM-dd HH:mm:ss |

**请求示例**:
```json
{
  "page": 1,
  "size": 10,
  "newsTitle": "行业动态",
  "categoryId": 1,
  "status": "published",
  "contentSource": "manual"
}
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | Long | 总记录数 |
| data | Array | 资讯列表 |
| data[].id | Long | 资讯ID |
| data[].newsTitle | String | 资讯标题 |
| data[].newsSummary | String | 资讯摘要 |
| data[].categoryId | Long | 分类ID |
| data[].categoryName | String | 分类名称 |
| data[].coverImageUrl | String | 封面图片URL |
| data[].materialId | Long | 关联素材文章ID |
| data[].author | String | 作者 |
| data[].publishTime | String | 发布时间 |
| data[].status | String | 状态 |
| data[].viewCount | Integer | 浏览次数 |
| data[].likeCount | Integer | 点赞次数 |
| data[].shareCount | Integer | 分享次数 |
| data[].commentCount | Long | 评论数 |
| data[].sort | Integer | 排序值 |
| data[].createTime | String | 创建时间 |
| data[].updateTime | String | 更新时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "newsTitle": "2024年家政服务行业发展趋势分析",
      "newsSummary": "随着人口老龄化加剧和双职工家庭增多，家政服务需求持续增长...",
      "categoryId": 1,
      "categoryName": "行业动态",
      "coverImageUrl": "https://example.com/images/news1.jpg",
      "materialId": null,
      "author": "张专家",
      "publishTime": "2024-01-15 10:00:00",
      "status": "published",
      "viewCount": 1250,
      "likeCount": 89,
      "shareCount": 23,
      "commentCount": 15,
        "sort": 1,
        "createTime": "2024-01-15 09:30:00",
        "updateTime": "2024-01-15 10:00:00"
    }
  ],
  "total": 25,
  "timestamp": 1640995200000
}
```

### 2.2 创建资讯

**接口地址**: `POST /publicbiz/news/create`

**功能说明**: 创建新的资讯文章

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| newsTitle | String | 是 | 资讯标题，最大200字符 |
| newsSummary | String | 是 | 资讯摘要 |
| newsContent | String | 是 | 资讯内容 |
| categoryId | Long | 是 | 分类ID |
| coverImageUrl | String | 是 | 封面图片URL |
| materialId | Long | 否 | 关联素材文章ID |
| author | String | 是 | 作者，最大50字符 |
| publishTime | String | 否 | 发布时间，格式：yyyy-MM-dd HH:mm:ss |
| status | String | 是 | 状态：draft-草稿/published-已发布 |
| sort | Integer | 否 | 排序值，数字越小越靠前 |

**请求示例**:
```json
{
  "newsTitle": "家政服务标准化建设指南",
  "newsSummary": "本文详细介绍了家政服务标准化的具体实施方法和注意事项...",
  "newsContent": "<p>家政服务标准化是提升服务质量的重要保障...</p>",
  "categoryId": 2,
  "coverImageUrl": "https://example.com/images/guide.jpg",
  "author": "李专家",
  "status": "draft",
  "sort": 5
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 26,
    "newsTitle": "家政服务标准化建设指南",
    "createTime": "2024-01-20 14:30:00"
  },
  "timestamp": 1640995200000
}
```

### 2.3 更新资讯

**接口地址**: `POST /publicbiz/news/update`

**功能说明**: 更新现有资讯信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 资讯ID |
| newsTitle | String | 是 | 资讯标题，最大200字符 |
| newsSummary | String | 是 | 资讯摘要 |
| newsContent | String | 是 | 资讯内容 |
| categoryId | Long | 是 | 分类ID |
| coverImageUrl | String | 是 | 封面图片URL |
| materialId | Long | 否 | 关联素材文章ID |
| author | String | 是 | 作者，最大50字符 |
| publishTime | String | 否 | 发布时间，格式：yyyy-MM-dd HH:mm:ss |
| status | String | 是 | 状态：draft-草稿/published-已发布 |
| sort | Integer | 否 | 排序值，数字越小越靠前 |

**请求示例**:
```json
{
  "id": 26,
  "newsTitle": "家政服务标准化建设指南（修订版）",
  "newsSummary": "本文详细介绍了家政服务标准化的具体实施方法和注意事项，包含最新政策解读...",
  "newsContent": "<p>家政服务标准化是提升服务质量的重要保障，本文结合最新政策...</p>",
  "categoryId": 2,
  "coverImageUrl": "https://example.com/images/guide_v2.jpg",
  "author": "李专家",
  "status": "published",
  "sort": 3
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 26,
    "updateTime": "2024-01-20 15:45:00"
  },
  "timestamp": 1640995200000
}
```

### 2.4 删除资讯

**接口地址**: `POST /publicbiz/news/delete`

**功能说明**: 删除指定资讯（逻辑删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 资讯ID |

**请求示例**:
```json
{
  "id": 26
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null,
  "timestamp": 1640995200000
}
```

### 2.5 更新资讯状态

**接口地址**: `POST /publicbiz/news/updateStatus`

**功能说明**: 更新资讯的发布状态（发布/下架）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 资讯ID |
| status | String | 是 | 目标状态：published-已发布/offline-已下架 |

**请求示例**:
```json
{
  "id": 26,
  "status": "published"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": {
    "id": 26,
    "status": "published",
    "updateTime": "2024-01-20 16:00:00"
  },
  "timestamp": 1640995200000
}
```

### 2.6 获取资讯详情

**接口地址**: `GET /publicbiz/news/detail/{id}`

**功能说明**: 根据ID获取资讯详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 资讯ID |

**请求示例**:
```
GET /publicbiz/news/detail/26
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 资讯ID |
| tenantId | Long | 租户ID |
| creator | String | 创建人 |
| createTime | String | 创建时间 |
| updater | String | 更新人 |
| updateTime | String | 更新时间 |
| newsTitle | String | 资讯标题 |
| newsSummary | String | 资讯摘要 |
| newsContent | String | 资讯内容 |
| categoryId | Long | 分类ID |
| categoryName | String | 分类名称 |
| coverImageUrl | String | 封面图片URL |
| materialId | Long | 关联素材文章ID |
| author | String | 作者 |
| publishTime | String | 发布时间 |
| status | String | 状态 |
| viewCount | Integer | 浏览次数 |
| likeCount | Integer | 点赞次数 |
| shareCount | Integer | 分享次数 |
| commentCount | Long | 评论数 |
| sort | Integer | 排序值 |

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 26,
    "tenantId": 1,
    "creator": "admin",
    "createTime": "2024-01-20 14:30:00",
    "updater": "admin",
    "updateTime": "2024-01-20 16:00:00",
    "newsTitle": "家政服务标准化建设指南（修订版）",
    "newsSummary": "本文详细介绍了家政服务标准化的具体实施方法和注意事项，包含最新政策解读...",
    "newsContent": "<p>家政服务标准化是提升服务质量的重要保障，本文结合最新政策...</p>",
    "categoryId": 2,
    "categoryName": "政策解读",
    "coverImageUrl": "https://example.com/images/guide_v2.jpg",
    "materialId": null,
    "author": "李专家",
    "publishTime": "2024-01-20 16:00:00",
    "status": "published",
    "viewCount": 0,
    "likeCount": 0,
    "shareCount": 0,
    "commentCount": 0,
    "sort": 3
  },
  "timestamp": 1640995200000
}
```





## 3. 数据字典

### 3.1 资讯状态枚举

| 值 | 说明 |
|----|------|
| draft | 草稿 |
| published | 已发布 |
| offline | 已下架 |

### 3.2 内容来源枚举

| 值 | 说明 |
|----|------|
| manual | 手动编写 |
| material | 关联素材库 |

### 3.3 资讯分类枚举

| 值 | 说明 |
|----|------|
| industry | 行业动态 |
| policy | 政策解读 |
| guide | 服务指南 |
| activity | 活动通知 |

## 4. 错误码说明

### 4.1 通用错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数验证错误 | 检查请求参数格式和必填项 |
| 403 | 权限不足 | 确认用户具有相应操作权限 |
| 404 | 资源不存在 | 检查资讯ID是否正确 |
| 500 | 服务器错误 | 联系技术支持 |

### 4.2 业务错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1001 | 资讯标题已存在 | 修改资讯标题 |
| 1002 | 分类不存在 | 检查分类ID是否正确 |
| 1003 | 素材文章不存在 | 检查素材文章ID是否正确 |
| 1004 | 资讯状态不允许操作 | 检查当前状态是否允许该操作 |
| 1005 | 封面图片格式不支持 | 上传支持的图片格式 |

## 5. 注意事项

1. **权限控制**: 所有接口都需要进行权限验证，确保用户具有相应操作权限
2. **数据验证**: 前端和后端都需要进行数据验证，确保数据完整性和正确性
3. **图片上传**: 封面图片建议尺寸为750x400px，支持jpg、png格式，大小不超过2MB
4. **内容安全**: 资讯内容需要进行XSS防护和敏感词过滤
5. **性能优化**: 分页查询建议使用数据库索引优化，避免全表扫描
6. **日志记录**: 重要操作需要记录操作日志，便于审计和问题排查

## 6. 接口调用示例

### 6.1 完整业务流程示例

```javascript
// 1. 获取资讯列表
const getNewsList = async () => {
  const response = await fetch('/publicbiz/news/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
      page: 1,
      size: 10,
      status: 'published'
    })
  });
  return await response.json();
};

// 2. 创建资讯
const createNews = async (newsData) => {
  const response = await fetch('/publicbiz/news/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify(newsData)
  });
  return await response.json();
};

// 3. 发布资讯
const publishNews = async (id) => {
  const response = await fetch('/publicbiz/news/updateStatus', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
      id: id,
      status: 'published'
    })
  });
  return await response.json();
};
```

## 7. 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2024-01-20 | 初始版本，包含基础CRUD接口 |
| 1.1.0 | 2024-01-25 | 新增状态管理接口 |
| 1.2.0 | 2024-02-01 | 新增分类管理接口 |
| 1.3.0 | 2024-02-20 | 简化接口设计，移除批量操作和浏览次数统计接口 |
| 1.4.0 | 2024-02-25 | 移除分类管理接口，进一步简化接口设计 | 