# 就业服务模块数据库设计文档

## 概述

本文档描述了就业服务模块的数据库表结构设计，包括服务套餐管理和阿姨管理两个主要功能模块。

## 数据库表结构

### 1. 服务套餐管理相关表

#### 1.1 服务套餐主表 (publicbiz_service_package)

存储服务套餐的基本信息，包括套餐名称、价格、服务内容等。

**主要字段说明：**

- `name`: 套餐名称
- `category`: 服务分类（日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理）
- `price`: 套餐价格
- `original_price`: 原价
- `unit`: 价格单位（次/项/天/月）
- `service_duration`: 服务时长
- `package_type`: 套餐类型（long-term-长周期套餐/count-card-次数次卡套餐）
- `status`: 状态（active-已上架/pending-待上架/deleted-回收站）

**使用场景：**

- 创建和管理服务套餐
- 套餐价格和内容配置
- 套餐状态管理

#### 1.2 服务套餐轮播图表 (publicbiz_package_carousel)

存储套餐的轮播图片信息。

**主要字段说明：**

- `package_id`: 关联的套餐ID
- `image_url`: 轮播图URL
- `sort_order`: 排序顺序
- `status`: 状态（1-启用，0-禁用）

**使用场景：**

- 套餐详情页轮播图展示
- 图片排序管理

#### 1.3 服务套餐特色标签表 (publicbiz_package_feature)

存储套餐的特色标签信息。

**主要字段说明：**

- `package_id`: 关联的套餐ID
- `feature_name`: 特色标签名称
- `sort_order`: 排序顺序

**使用场景：**

- 套餐特色功能展示
- 标签排序管理

### 2. 阿姨管理相关表

#### 2.1 阿姨基本信息表 (publicbiz_practitioner)

存储阿姨的基本信息，包括个人资料、服务信息、评级等。

**主要字段说明：**

- `name`: 阿姨姓名
- `phone`: 手机号（用于登录）
- `id_card`: 身份证号
- `hometown`: 籍贯
- `service_type`: 主要服务类型（月嫂/育儿嫂/保洁/护工）
- `experience_years`: 从业年限
- `platform_status`: 平台状态（cooperating-合作中/terminated-已解约）
- `rating`: 评级（1.0-5.0）
- `agency_id`: 所属机构ID
- `status`: 状态（active-正常/inactive-停用/pending-待审核）
- `current_status`: 当前状态（服务中/待岗/休假中）
- `total_orders`: 累计服务单数
- `total_income`: 累计收入
- `customer_satisfaction`: 客户满意度评分

**使用场景：**

- 阿姨信息管理
- 阿姨状态跟踪
- 服务统计和评级

#### 2.2 阿姨资质文件表 (publicbiz_practitioner_qualification)

存储阿姨的各类资质文件，如身份证、健康证、技能证书等。

**主要字段说明：**

- `practitioner_id`: 阿姨ID
- `file_type`: 文件类型（id_card-身份证/health_cert-健康证/skill_cert-专业技能证书/other-其他附件）
- `file_name`: 文件名
- `file_url`: 文件URL
- `file_size`: 文件大小
- `file_extension`: 文件扩展名
- `sort_order`: 排序顺序
- `status`: 状态（1-有效，0-无效）

**使用场景：**

- 阿姨资质文件上传和管理
- 文件分类展示
- 文件有效性验证

#### 2.3 阿姨服务记录表 (publicbiz_practitioner_service_record)

记录阿姨的服务历史，包括订单信息、服务评价等。

**主要字段说明：**

- `practitioner_id`: 阿姨ID
- `order_id`: 订单ID
- `customer_id`: 客户ID
- `customer_name`: 客户姓名
- `service_type`: 服务类型
- `service_start_time`: 服务开始时间
- `service_end_time`: 服务结束时间
- `service_duration`: 服务时长
- `service_address`: 服务地址
- `service_amount`: 服务金额
- `practitioner_income`: 阿姨收入
- `platform_income`: 平台收入
- `order_status`: 订单状态
- `customer_rating`: 客户评分
- `customer_comment`: 客户评价

**使用场景：**

- 服务历史记录
- 收入统计
- 客户评价管理

#### 2.4 阿姨评级记录表 (publicbiz_practitioner_rating_record)

记录阿姨评级的变化历史，支持多种评级来源。

**主要字段说明：**

- `practitioner_id`: 阿姨ID
- `rating_type`: 评级类型（customer-客户评价/platform-平台评级/system-系统评级）
- `old_rating`: 原评级
- `new_rating`: 新评级
- `rating_change`: 评级变化
- `rating_reason`: 评级原因
- `evaluator_id`: 评价人ID
- `evaluator_name`: 评价人姓名
- `evaluator_type`: 评价人类型（customer-客户/admin-管理员/system-系统）
- `related_order_id`: 关联订单ID

**使用场景：**

- 评级历史追踪
- 评级变化分析
- 多维度评级管理

## 表关系说明

### 关联关系

1. `publicbiz_package_carousel.package_id` → `publicbiz_service_package.id`
2. `publicbiz_package_feature.package_id` → `publicbiz_service_package.id`
3. `publicbiz_practitioner_qualification.practitioner_id` → `publicbiz_practitioner.id`
4. `publicbiz_practitioner_service_record.practitioner_id` → `publicbiz_practitioner.id`
5. `publicbiz_practitioner_rating_record.practitioner_id` → `publicbiz_practitioner.id`

**注意：** 为了提高数据库性能，避免外键约束带来的锁表问题，本设计采用逻辑关联而非物理外键约束。应用层需要确保数据一致性。

### 业务关系

- 一个套餐可以有多个轮播图和特色标签
- 一个阿姨可以有多个资质文件
- 一个阿姨可以有多个服务记录和评级记录

## 索引设计

### 主要索引

- 租户ID索引：支持多租户数据隔离
- 状态索引：支持状态筛选
- 时间索引：支持时间范围查询
- 业务字段索引：支持业务查询需求

### 唯一索引

- 手机号唯一索引：确保阿姨手机号唯一性
- 身份证号唯一索引：确保身份证号唯一性

## 外键约束说明

### 设计原则

为了提高数据库性能，避免外键约束带来的锁表问题，本设计采用逻辑关联而非物理外键约束。

### 优势

1. **性能提升**：避免外键约束带来的锁表问题
2. **灵活性**：支持更灵活的数据操作
3. **扩展性**：便于分库分表和数据迁移

### 注意事项

1. **数据一致性**：应用层需要确保数据一致性
2. **级联删除**：删除主表数据时，需要手动处理关联数据
3. **数据验证**：在应用层进行关联数据的有效性验证

### 建议实现

```sql
-- 查询时使用JOIN确保数据完整性
SELECT p.*, pq.file_name
FROM publicbiz_practitioner p
LEFT JOIN publicbiz_practitioner_qualification pq ON p.id = pq.practitioner_id
WHERE p.id = ? AND pq.deleted = 0;

-- 删除时检查关联数据
DELETE FROM publicbiz_practitioner_qualification WHERE practitioner_id = ?;
DELETE FROM publicbiz_practitioner WHERE id = ?;
```

## 数据字典

### 服务分类 (service_category)

- 日常保洁
- 深度保洁
- 家电清洗
- 专项服务
- 月嫂服务
- 收纳整理

### 服务类型 (service_type)

- 月嫂
- 育儿嫂
- 保洁
- 护工

### 平台状态 (platform_status)

- cooperating: 合作中
- terminated: 已解约

### 阿姨状态 (practitioner_status)

- active: 正常
- inactive: 停用
- pending: 待审核

### 文件类型 (file_type)

- id_card: 身份证
- health_cert: 健康证
- skill_cert: 专业技能证书
- other: 其他附件

### 评级类型 (rating_type)

- customer: 客户评价
- platform: 平台评级
- system: 系统评级

## 使用建议

### 1. 数据查询优化

- 使用复合索引优化多条件查询
- 合理使用分页查询避免大数据量问题
- 定期清理无效数据

### 2. 数据安全

- 敏感信息（如身份证号）建议加密存储
- 文件URL建议使用签名验证
- 定期备份重要数据

### 3. 性能优化

- 大表建议分表分库
- 历史数据建议归档
- 定期更新统计信息

## 扩展建议

### 1. 功能扩展

- 可考虑增加阿姨技能标签表
- 可考虑增加客户评价标签表
- 可考虑增加服务区域表

### 2. 性能扩展

- 可考虑增加缓存表
- 可考虑增加统计汇总表
- 可考虑增加日志表

### 3. 安全扩展

- 可考虑增加操作日志表
- 可考虑增加数据变更记录表
- 可考虑增加权限控制表
