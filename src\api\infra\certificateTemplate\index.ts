import request from '@/config/axios'

// 证书模板字段类型定义（详情查询返回）
export interface CertificateField {
  id?: number // 数据库主键，Long类型（详情查询时返回）
  fieldId: string // 字段唯一标识，如"field_1"
  fieldType: string // 对应API文档中的fieldType
  fieldLabel: string // 对应API文档中的fieldLabel
  positionX: number // 对应API文档中的positionX
  positionY: number // 对应API文档中的positionY
  fontSize: number
  fontColor: string // 对应API文档中的fontColor
  fontFamily: string
  sortOrder: number
  // 前端使用的字段（不传给后端）
  type?: string // 兼容前端现有代码
  label?: string // 兼容前端现有代码
  x?: number // 兼容前端现有代码
  y?: number // 兼容前端现有代码
  color?: string // 兼容前端现有代码
  selected?: boolean // 前端选中状态
}

// 新增/更新证书模板时的字段类型（不包含id）
export interface CertificateFieldForApi {
  fieldId: string // 字段唯一标识，如"field_1"
  fieldType: string
  fieldLabel: string
  positionX: number
  positionY: number
  fontSize: number
  fontColor: string
  fontFamily: string
  sortOrder: number
}

// 证书模板数据结构
export interface CertificateTemplateVO {
  id?: number
  name: string
  type: string
  description?: string
  background?: string
  backgroundUrl?: string
  course?: string
  courseName?: string
  status: string
  htmlContent?: string
  previewUrl?: string
  fields?: CertificateField[]
  creator?: string
  creatorName?: string
  createTime?: string
  updater?: string
  updateTime?: string
}

// 分页查询参数
export interface CertificateTemplatePageParams {
  page?: number
  size?: number
  type?: string
  status?: string
  keyword?: string
}

// 分页查询结果
export interface CertificateTemplatePageResult {
  total: number
  list: CertificateTemplateVO[]
}

// 新增证书模板参数
export interface CreateCertificateTemplateParams {
  name: string
  type: string
  description?: string
  background?: string
  backgroundUrl?: string
  course?: string
  courseName?: string
  status: string
  htmlContent?: string
  fields?: CertificateFieldForApi[] // 使用不包含id的字段类型
}

// 更新证书模板参数
export interface UpdateCertificateTemplateParams extends CreateCertificateTemplateParams {
  id: number
}

// 证书模板统计结果
export interface CertificateTemplateStatisticsResult {
  total: number
  activeCount: number
  inactiveCount: number
  draftCount: number
}

// 证书模板列表项
export interface CertificateTemplateListItem {
  id: number
  name: string
  type: string
  status: string
}

// 证书模板API
export const CertificateTemplateApi = {
  // 分页查询证书模板
  getPage: async (
    params: CertificateTemplatePageParams
  ): Promise<CertificateTemplatePageResult> => {
    return await request.get({ url: '/publicbiz/certificate/template/page', params })
  },

  // 获取证书模板详情
  getDetail: async (id: number): Promise<CertificateTemplateVO> => {
    return await request.get({ url: '/publicbiz/certificate/template/detail', params: { id } })
  },

  // 新增证书模板
  create: async (data: CreateCertificateTemplateParams): Promise<{ id: number }> => {
    return await request.post({ url: '/publicbiz/certificate/template/create', data })
  },

  // 更新证书模板
  update: async (data: UpdateCertificateTemplateParams): Promise<void> => {
    return await request.post({ url: '/publicbiz/certificate/template/update', data })
  },

  // 删除证书模板
  delete: async (id: number): Promise<void> => {
    return await request.post({ url: '/publicbiz/certificate/template/delete', data: { id } })
  },

  // 更新证书模板状态
  updateStatus: async (id: number, status: string): Promise<void> => {
    return await request.post({
      url: `/publicbiz/certificate/template/update-status?id=${id}&status=${status}`
    })
  },

  // 获取证书模板统计数据
  getStatistics: async (): Promise<CertificateTemplateStatisticsResult> => {
    return await request.get({ url: '/publicbiz/certificate/template/statistics' })
  },

  // 获取证书模板列表（不分页）
  getList: async (status?: string): Promise<CertificateTemplateListItem[]> => {
    const params = status ? { status } : {}
    return await request.get({ url: '/publicbiz/certificate/template/list', params })
  }
}

// 字段类型映射（前端到后端API）
export const mapFieldToApi = (field: any): CertificateFieldForApi => {
  return {
    fieldId: field.id, // 前端的id对应后端的fieldId
    fieldType: field.type || field.fieldType,
    fieldLabel: field.label || field.fieldLabel,
    positionX: field.x !== undefined ? field.x : field.positionX,
    positionY: field.y !== undefined ? field.y : field.positionY,
    fontSize: field.fontSize,
    fontColor: field.color || field.fontColor,
    fontFamily: field.fontFamily,
    sortOrder: field.sortOrder || 1
  }
}

// 字段类型映射（后端到前端）
export const mapFieldFromApi = (field: CertificateField): any => {
  return {
    id: field.fieldId, // 使用fieldId作为前端的id
    type: field.fieldType,
    label: field.fieldLabel,
    x: field.positionX,
    y: field.positionY,
    fontSize: field.fontSize,
    color: field.fontColor,
    fontFamily: field.fontFamily,
    sortOrder: field.sortOrder,
    selected: false,
    // 保留API字段
    fieldType: field.fieldType,
    fieldLabel: field.fieldLabel,
    positionX: field.positionX,
    positionY: field.positionY,
    fontColor: field.fontColor,
    fieldId: field.fieldId,
    dbId: field.id // 保留数据库主键ID
  }
}

// 占位符映射配置
export const placeholderMap: Record<string, string> = {
  name: 'studentName',
  code: 'certificateCode',
  id: 'studentId',
  date: 'issueDate',
  qrcode: 'qrcodeUrl'
}

// 默认占位符数据
export const defaultPlaceholderData: Record<string, string> = {
  studentName: '张三',
  certificateCode: 'CERT-2024-001',
  studentId: '123456789012345678',
  issueDate: '2024年8月1日',
  qrcodeUrl: 'https://example.com/qrcode/cert-2024-001.png'
}
