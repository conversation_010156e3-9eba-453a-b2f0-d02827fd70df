import request from '@/config/axios'

// 数字资产课程相关接口类型定义
export interface DigitalAssetCourse {
  id?: number
  name: string
  teachType: string
  coverUrl?: string
  category: string
  status: string
  teacherId?: number
  teacherName?: string
  businessModule?: string
  merchant?: number
  merchantName?: string
  description?: string
  // 线下授课专用字段
  location?: string
  schedule?: string
  totalSeats?: number
  enrolledCount?: number
  // 线上授课专用字段
  totalDuration?: number
}

// 新增课程请求参数
export interface AddCourseParams {
  name: string
  teachType: string
  coverUrl?: string
  category: string
  status: string
  teacherId?: number
  teacherName?: string
  businessModule?: string
  merchant?: number
  merchantName?: string
  description?: string
  location?: string
  schedule?: string
  totalSeats?: number
  enrolledCount?: number
  totalDuration?: number
}

// 新增课程响应结果
export interface AddCourseResult {
  id: number
}

// 分页查询参数
export interface CoursePageParams {
  pageNo: number
  pageSize: number
  category?: string
  status?: string
  teachType?: string
  businessModule?: string
  merchant?: number
  keyword?: string
}

// 分页查询结果
export interface CoursePageResult {
  total: number
  list: DigitalAssetCourse[]
}

// 查询课程列表参数（不分页）
export interface CourseListParams {
  category?: string
  status?: string
  teachType?: string
  businessModule?: string
  merchant?: number
  keyword?: string
}

// 课程统计概览结果
export interface CourseStatisticsResult {
  totalCount: number
  onlineCount: number
  offlineCount: number
  publishedCount: number
}

// 课程章节相关接口类型定义
export interface CourseChapter {
  id?: number
  courseId: number
  title: string
  sortOrder?: number
  createTime?: string
  lessons?: CourseLesson[]
}

// 课程课时相关接口类型定义
export interface CourseLesson {
  id?: number
  courseId?: number
  chapterId: number
  title: string
  lessonType: string
  isFree?: boolean
  materialId?: string
  materialName?: string
  materialFileUrl?: string
  sortOrder?: number
  createTime?: string
}

// 新增章节请求参数
export interface AddChapterParams {
  courseId: number
  title: string
  sortOrder?: number
}

// 新增章节响应结果
export interface AddChapterResult {
  id: number
}

// 更新章节请求参数
export interface UpdateChapterParams {
  id: number
  courseId: number
  title: string
  sortOrder?: number
}

// 新增课时请求参数
export interface AddLessonParams {
  courseId: number
  chapterId: number
  title: string
  lessonType: string
  isFree?: boolean
  materialId?: string
  materialName?: string
  materialFileUrl?: string
  sortOrder?: number
}

// 新增课时响应结果
export interface AddLessonResult {
  id: number
}

// 更新课时请求参数
export interface UpdateLessonParams {
  id: number
  courseId: number
  chapterId: number
  title: string
  lessonType: string
  isFree?: boolean
  materialId?: string
  materialName?: string
  materialFileUrl?: string
  sortOrder?: number
}

// 章节列表响应结果
// 注意：实际后端返回格式为 {code: 0, data: CourseChapter[], msg: ""}
// request 封装会提取 data 部分，所以这里直接定义为数组类型
export type ChapterListResult = CourseChapter[]

// 课程附件相关接口类型定义
export interface CourseAttachment {
  id?: number
  courseId: number
  attachmentName: string
  attachmentType: string
  fileUrl: string
  fileSize?: number
  createTime?: string
}

// 新增附件请求参数
export interface AddAttachmentParams {
  courseId: number
  attachmentName: string
  attachmentType: string
  fileUrl: string
  fileSize?: number
}

// 新增附件响应结果
export interface AddAttachmentResult {
  id: number
}

// 附件列表响应结果
export interface AttachmentListResult {
  list: CourseAttachment[]
}

export const DigitalAssetApi = {
  // 新增课程
  addCourse: async (data: AddCourseParams): Promise<AddCourseResult> => {
    return await request.post({ url: '/publicbiz/digital-asset/course/create', data })
  },

  // 查询课程列表（不分页）
  getCourseList: async (params?: CourseListParams): Promise<DigitalAssetCourse[]> => {
    return await request.get({ url: '/publicbiz/digital-asset/course/list', params })
  },

  // 分页查询课程列表
  getCoursePage: async (params: CoursePageParams): Promise<CoursePageResult> => {
    return await request.get({ url: '/publicbiz/digital-asset/course/page', params })
  },

  // 获取课程详情
  getCourseDetail: async (id: number): Promise<DigitalAssetCourse> => {
    return await request.get({ url: `/publicbiz/digital-asset/course/get/${id}` })
  },

  // 更新课程信息
  updateCourse: async (data: DigitalAssetCourse): Promise<void> => {
    return await request.put({ url: '/publicbiz/digital-asset/course/update', data })
  },

  // 课程状态管理（上架/下架）
  updateCourseStatus: async (id: number, status: string): Promise<void> => {
    return await request.put({
      url: `/publicbiz/digital-asset/course/status/${id}`,
      data: { status }
    })
  },

  // 获取课程统计概览
  getCourseStatistics: async (): Promise<CourseStatisticsResult> => {
    return await request.get({ url: '/publicbiz/digital-asset/course/statistics/overview' })
  },

  // ==================== 章节管理接口 ====================

  // 获取课程章节列表（包含课时信息）
  getChapterList: async (courseId: number): Promise<ChapterListResult> => {
    return await request.get({ url: `/publicbiz/digital-asset/course/chapter/list/${courseId}` })
  },

  // 新增课程章节
  addChapter: async (data: AddChapterParams): Promise<AddChapterResult> => {
    return await request.post({ url: '/publicbiz/digital-asset/course/chapter/add', data })
  },

  // 更新课程章节
  updateChapter: async (data: UpdateChapterParams): Promise<void> => {
    return await request.put({ url: '/publicbiz/digital-asset/course/chapter/update', data })
  },

  // 删除课程章节
  deleteChapter: async (id: number): Promise<void> => {
    return await request.delete({ url: `/publicbiz/digital-asset/course/chapter/delete/${id}` })
  },

  // ==================== 课时管理接口 ====================

  // 新增课程课时
  addLesson: async (data: AddLessonParams): Promise<AddLessonResult> => {
    return await request.post({ url: '/publicbiz/digital-asset/course/lesson/add', data })
  },

  // 更新课程课时
  updateLesson: async (data: UpdateLessonParams): Promise<void> => {
    return await request.put({ url: '/publicbiz/digital-asset/course/lesson/update', data })
  },

  // 删除课程课时
  deleteLesson: async (id: number): Promise<void> => {
    return await request.delete({ url: `/publicbiz/digital-asset/course/lesson/delete/${id}` })
  },

  // ==================== 附件管理接口 ====================

  // 获取课程附件列表
  getAttachmentList: async (courseId: number): Promise<AttachmentListResult> => {
    return await request.get({ url: `/publicbiz/digital-asset/course/attachment/list/${courseId}` })
  },

  // 新增课程附件
  addAttachment: async (data: AddAttachmentParams): Promise<AddAttachmentResult> => {
    return await request.post({ url: '/publicbiz/digital-asset/course/attachment/add', data })
  },

  // 删除课程附件
  deleteAttachment: async (id: number): Promise<void> => {
    return await request.delete({ url: `/publicbiz/digital-asset/course/attachment/remove/${id}` })
  }
}
