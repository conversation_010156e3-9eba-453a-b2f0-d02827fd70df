<!--
  页面名称：批量重指派弹窗
  功能描述：选择阿姨进行批量重指派
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择阿姨"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="reassign-content">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索阿姨姓名..."
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 阿姨列表 -->
      <div class="auntie-list">
        <div
          v-for="auntie in filteredAunties"
          :key="auntie.id"
          class="auntie-card"
          :class="{ selected: selectedAuntie?.id === auntie.id }"
          @click="selectAuntie(auntie)"
        >
          <div class="auntie-avatar">
            <div class="avatar-placeholder">{{ auntie.name.charAt(0) }}</div>
          </div>
          <div class="auntie-info">
            <div class="auntie-name">{{ auntie.name }}</div>
            <div class="auntie-details"
              >{{ auntie.age }}岁·{{ auntie.experience }}年经验·评分{{ auntie.rating }}</div
            >
            <div class="auntie-tags">
              <el-tag
                v-for="tag in auntie.tags"
                :key="tag"
                size="small"
                type="primary"
                plain
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ tag }}
              </el-tag>
            </div>
            <div class="auntie-orders">已完成{{ auntie.completedOrders }}单</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!selectedAuntie">
          确认选择
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  selectedTasks: any[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [auntie: any, tasks: any[]]
}>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const searchKeyword = ref('')
const selectedAuntie = ref<any>(null)

// 模拟阿姨数据
const aunties = ref([
  {
    id: '1',
    name: '张阿姨',
    age: 45,
    experience: 8,
    rating: 4.8,
    tags: ['月嫂', '育儿嫂', '家务保姆'],
    completedOrders: 156
  },
  {
    id: '2',
    name: '李阿姨',
    age: 42,
    experience: 6,
    rating: 4.7,
    tags: ['月嫂', '育儿嫂'],
    completedOrders: 128
  },
  {
    id: '3',
    name: '王阿姨',
    age: 48,
    experience: 10,
    rating: 4.9,
    tags: ['月嫂', '育儿嫂', '家务保姆', '护工'],
    completedOrders: 203
  },
  {
    id: '4',
    name: '赵阿姨',
    age: 40,
    experience: 5,
    rating: 4.6,
    tags: ['育儿嫂', '家务保姆'],
    completedOrders: 89
  }
])

// 过滤阿姨列表
const filteredAunties = computed(() => {
  if (!searchKeyword.value) return aunties.value

  return aunties.value.filter((auntie) => auntie.name.includes(searchKeyword.value))
})

const selectAuntie = (auntie: any) => {
  selectedAuntie.value = auntie
}

const handleClose = () => {
  selectedAuntie.value = null
  searchKeyword.value = ''
  emit('update:visible', false)
}

const handleConfirm = async () => {
  if (!selectedAuntie.value) {
    ElMessage.warning('请选择阿姨')
    return
  }

  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    emit('success', selectedAuntie.value, props.selectedTasks)
    ElMessage.success('批量重指派成功')
    handleClose()
  } catch (error) {
    console.error('批量重指派失败:', error)
    ElMessage.error('批量重指派失败')
  }
}
</script>

<style scoped lang="scss">
.reassign-content {
  .search-bar {
    margin-bottom: 20px;
    text-align: center;
  }

  .auntie-list {
    max-height: 400px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
  }

  .auntie-card {
    display: flex;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }

    &.selected {
      border-color: #409eff;
      background-color: #f0f9ff;
    }

    .auntie-avatar {
      margin-right: 12px;

      .avatar-placeholder {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: bold;
      }
    }

    .auntie-info {
      flex: 1;

      .auntie-name {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
      }

      .auntie-details {
        font-size: 12px;
        color: #909399;
        margin-bottom: 8px;
      }

      .auntie-tags {
        margin-bottom: 8px;
      }

      .auntie-orders {
        font-size: 12px;
        color: #67c23a;
        font-weight: 500;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
