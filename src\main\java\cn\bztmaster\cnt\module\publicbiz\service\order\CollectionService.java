package cn.bztmaster.cnt.module.publicbiz.service.order;

import cn.bztmaster.cnt.module.publicbiz.dto.order.CollectionInfoDTO;
import cn.bztmaster.cnt.module.publicbiz.dto.order.CollectionResultDTO;

/**
 * 收款服务接口
 * 提供订单收款相关的业务操作
 */
public interface CollectionService {

    /**
     * 确认收款
     * 确认订单的收款信息，更新订单状态
     *
     * @param collectionInfo 收款信息
     * @return 收款结果
     */
    CollectionResultDTO confirmCollection(CollectionInfoDTO collectionInfo);

    /**
     * 更新收款信息
     * 更新已收款订单的收款信息
     *
     * @param collectionInfo 收款信息
     * @return 更新结果
     */
    CollectionResultDTO updateCollection(CollectionInfoDTO collectionInfo);

    /**
     * 查询收款信息
     * 查询订单的收款信息详情
     *
     * @param orderId 订单ID
     * @return 收款信息结果
     */
    CollectionResultDTO getCollectionInfo(Long orderId);

    /**
     * 验证收款信息
     * 验证收款信息的合理性
     *
     * @param collectionInfo 收款信息
     * @return 验证结果
     */
    boolean validateCollectionInfo(CollectionInfoDTO collectionInfo);
}

