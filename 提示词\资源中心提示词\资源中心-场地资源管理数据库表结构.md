# 资源中心 - 场地资源管理数据库表结构

## 概述

本文档包含基于 `src/views/infra/ResourceCenter/SiteManagement` 目录下 Vue 页面文件分析生成的 MySQL 数据表建表 SQL 脚本。

## 分析来源

- **SiteManagement.vue**: 场地列表管理主页面
- **AddSite.vue**: 新增/编辑场地弹窗
- **SiteAppointment.vue**: 场地预约弹窗
- **SchedulingBoard.vue**: 排期看板组件

## 数据表设计

### 1. 场地管理表 (publicbiz_site_management)

```sql
-- 场地管理表
CREATE TABLE `publicbiz_site_management` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',

  -- 基本信息
  `name` VARCHAR(30) NOT NULL COMMENT '场地名称',
  `campus` VARCHAR(50) NOT NULL COMMENT '所属校区/位置',
  `campus_name` VARCHAR(50) NOT NULL COMMENT '所属校区名称',
  `type` VARCHAR(20) NOT NULL COMMENT '场地类型：培训教室、多功能厅、会议室、实训室、考试场地、研讨室',
  `location` VARCHAR(30) NOT NULL COMMENT '具体位置，如：A座1楼101室',

  -- 座位配置
  `seat_total` INT NOT NULL DEFAULT 0 COMMENT '总座位数（自动计算）',
  `seat_types` JSON COMMENT '座位类型配置，格式：[{"name":"普通座","count":50,"remark":"备注"}]',
  `seat_detail` VARCHAR(100) COMMENT '座位详情描述，如：普通座:50座',

  -- 设备和状态
  `equipment` VARCHAR(200) COMMENT '设备配置描述',
  `status` VARCHAR(10) NOT NULL DEFAULT '可用' COMMENT '状态：可用、已预约、维护中、停用',
  `description` VARCHAR(200) COMMENT '场地描述和适用场景',

  -- 负责人信息
  `manager` VARCHAR(10) NOT NULL COMMENT '负责人姓名',
  `manager_phone` VARCHAR(20) NOT NULL COMMENT '负责人联系电话',

  -- 公共字段
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 索引
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_campus` (`campus`),
  INDEX `idx_type` (`type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_manager` (`manager`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='场地管理表';
```

### 2. 场地预约表 (publicbiz_site_appointment)

```sql
-- 场地预约表
CREATE TABLE `publicbiz_site_appointment` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',

  -- 关联信息
  `site_id` BIGINT NOT NULL COMMENT '场地ID，关联场地管理表',
  `site_name` VARCHAR(30) NOT NULL COMMENT '场地名称（冗余字段）',

  -- 活动信息
  `activity_name` VARCHAR(30) NOT NULL COMMENT '活动名称',
  `activity_type` VARCHAR(20) NOT NULL COMMENT '活动类型：培训、考试、会议、讲座、其他',

  -- 时间安排
  `start_date` DATE NOT NULL COMMENT '开始日期',
  `end_date` DATE NOT NULL COMMENT '结束日期',
  `start_time` TIME NOT NULL COMMENT '开始时间',
  `end_time` TIME NOT NULL COMMENT '结束时间',
  `time_range` VARCHAR(20) COMMENT '时间段描述，如：09:00-17:00',

  -- 参与人员
  `people_count` INT NOT NULL COMMENT '预计人数',
  `contact_name` VARCHAR(10) NOT NULL COMMENT '联系人姓名',
  `contact_phone` VARCHAR(20) NOT NULL COMMENT '联系电话',

  -- 状态和备注
  `status` VARCHAR(10) NOT NULL DEFAULT '已确认' COMMENT '预约状态：已确认、待确认、已取消',
  `remark` VARCHAR(200) COMMENT '备注信息',

  -- 公共字段
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 索引
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_site_id` (`site_id`),
  INDEX `idx_start_date` (`start_date`),
  INDEX `idx_end_date` (`end_date`),
  INDEX `idx_activity_type` (`activity_type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_contact_name` (`contact_name`),
  INDEX `idx_create_time` (`create_time`),

  -- 外键约束
  FOREIGN KEY (`site_id`) REFERENCES `publicbiz_site_management`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='场地预约表';
```

## 字段映射说明

### 场地管理表字段来源

| 字段名        | 来源文件    | 对应表单元素/数据模型                |
| ------------- | ----------- | ------------------------------------ |
| name          | AddSite.vue | el-input v-model="form.name"         |
| campus        | AddSite.vue | el-select v-model="form.campus"      |
| campus_name   | AddSite.vue | el-select v-model="form.campus"      |
| type          | AddSite.vue | el-select v-model="form.type"        |
| location      | AddSite.vue | el-input v-model="form.location"     |
| seat_total    | AddSite.vue | computed seatTotal                   |
| seat_types    | AddSite.vue | form.seatTypes 数组                  |
| equipment     | AddSite.vue | el-input v-model="form.equipment"    |
| status        | AddSite.vue | el-select v-model="form.status"      |
| description   | AddSite.vue | el-input v-model="form.desc"         |
| manager       | AddSite.vue | el-input v-model="form.manager"      |
| manager_phone | AddSite.vue | el-input v-model="form.managerPhone" |

### 场地预约表字段来源

| 字段名        | 来源文件            | 对应表单元素/数据模型                   |
| ------------- | ------------------- | --------------------------------------- |
| site_id       | SiteAppointment.vue | props.siteInfo.id                       |
| activity_name | SiteAppointment.vue | el-input v-model="form.activityName"    |
| activity_type | SiteAppointment.vue | el-select v-model="form.activityType"   |
| start_date    | SiteAppointment.vue | el-date-picker v-model="form.startDate" |
| end_date      | SiteAppointment.vue | el-date-picker v-model="form.endDate"   |
| start_time    | SiteAppointment.vue | el-time-picker v-model="form.startTime" |
| end_time      | SiteAppointment.vue | el-time-picker v-model="form.endTime"   |
| people_count  | SiteAppointment.vue | el-input v-model="form.peopleCount"     |
| contact_name  | SiteAppointment.vue | el-input v-model="form.contactName"     |
| contact_phone | SiteAppointment.vue | el-input v-model="form.contactPhone"    |
| status        | SiteAppointment.vue | el-select v-model="form.status"         |
| remark        | SiteAppointment.vue | el-input v-model="form.remark"          |

## 枚举值说明

### 场地类型 (type)

- 培训教室
- 多功能厅
- 会议室
- 实训室
- 考试场地
- 研讨室

### 场地状态 (status)

- 可用
- 已预约
- 维护中
- 停用

### 活动类型 (activity_type)

- 培训
- 考试
- 会议
- 讲座
- 其他

### 预约状态 (appointment status)

- 已确认
- 待确认
- 已取消

## 使用说明

1. 所有表都使用 `utf8mb4` 字符集和 `utf8mb4_unicode_ci` 排序规则
2. 符合 MySQL 5.7/8.0 语法标准
3. 包含完整的索引设计，优化查询性能
4. 使用外键约束保证数据完整性（RESTRICT 策略防止误删）
5. JSON 字段用于存储复杂的座位配置
6. 所有时间字段都有默认值和自动更新机制

## 注意事项

- 座位配置使用 JSON 格式存储，便于扩展和查询
- 预约表支持跨日期的活动安排
- 外键约束使用 RESTRICT 策略，防止误删除已有预约的场地
- 所有表都包含软删除字段，支持数据恢复
