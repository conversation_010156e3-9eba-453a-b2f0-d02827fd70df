# 高校实践订单下拉数据接口文档

## 接口概述

该接口用于获取高校实践订单创建/编辑时需要的下拉选项数据，包括商机列表、线索列表、客户列表和负责人列表。

## 接口信息

- **接口名称**: 获取订单下拉数据
- **接口路径**: `/admin-api/publicbiz/order/dropdown-data`
- **请求方法**: `POST`
- **接口描述**: 根据订单类型获取对应的商机、线索、客户、负责人等下拉选项数据

## 请求参数

### 请求头

```
Content-Type: application/json
Authorization: Bearer {token}
```

### 请求体 (JSON)

```json
{
  "orderType": "practice",
  "businessLine": "高校实践"
}
```

### 参数说明

| 参数名       | 类型   | 必填 | 说明     | 示例值     |
| ------------ | ------ | ---- | -------- | ---------- |
| orderType    | String | 是   | 订单类型 | "practice" |
| businessLine | String | 否   | 业务线   | "高校实践" |

### orderType 可选值

- `practice`: 高校实践
- `training`: 企业培训
- `domestic`: 家政服务
- `certification`: 考试认证

## 响应参数

### 响应格式

```json
{
  "code": 0,
  "data": {
    "businessOptions": [...],
    "leadOptions": [...],
    "customerOptions": [...],
    "managerOptions": [...]
  },
  "msg": "操作成功"
}
```

### data 字段说明

| 字段名          | 类型  | 说明           |
| --------------- | ----- | -------------- |
| businessOptions | Array | 商机选项列表   |
| leadOptions     | Array | 线索选项列表   |
| customerOptions | Array | 客户选项列表   |
| managerOptions  | Array | 负责人选项列表 |

## 完整响应示例

### 成功响应示例

```json
{
  "code": 0,
  "data": {
    "businessOptions": [
      {
        "id": 1,
        "name": "YY大学春季实习项目商机",
        "customerName": "YY大学",
        "businessType": "高校",
        "totalPrice": 500000.0,
        "businessStage": "方案报价",
        "ownerUserName": "张三"
      }
    ],
    "leadOptions": [
      {
        "id": 1,
        "leadId": "LX202406001",
        "customerName": "李四",
        "customerPhone": "***********",
        "businessModule": "高校业务",
        "leadSource": "官网注册",
        "leadStatus": "未处理"
      }
    ],
    "customerOptions": [
      {
        "id": 1,
        "customerName": "YY大学",
        "customerPhone": "028-12345678"
      }
    ],
    "managerOptions": [
      {
        "id": 1,
        "managerName": "张三",
        "managerPhone": "***********"
      }
    ]
  },
  "msg": "操作成功"
}
```

## 业务逻辑说明

### 数据筛选规则

1. **商机筛选**: 根据订单类型映射到对应的业务类型

   - `practice` → `高校`
   - `training` → `培训`
   - `domestic` → `家政`
   - `certification` → `认证`

2. **线索筛选**: 根据订单类型映射到对应的业务模块
   - `practice` → `1` (高校业务)
   - `training` → `3` (培训业务)
   - `domestic` → `2` (家政业务)
   - `certification` → `4` (认证业务)

### 数据来源

- **商机数据**: 来自 `publicbiz_business` 表
- **线索数据**: 来自 `publicbiz_lead_info` 表
- **客户数据**: 从商机表中提取唯一客户信息
- **负责人数据**: 从商机表中提取唯一负责人信息

## 使用场景

### 1. 订单创建页面

- 选择关联商机
- 选择关联线索
- 选择客户
- 选择负责人

### 2. 订单编辑页面

- 修改关联商机
- 修改关联线索
- 修改客户信息
- 修改负责人信息

## 注意事项

1. **权限控制**: 需要 `publicbiz:university-practice-order:query` 权限
2. **数据过滤**: 只返回未删除的有效数据
3. **排序规则**: 按ID倒序排列，最新数据在前
4. **数据去重**: 客户和负责人列表会自动去重

## 错误码说明

| 错误码 | 说明           |
| ------ | -------------- |
| 0      | 成功           |
| 400    | 请求参数错误   |
| 401    | 未授权/未登录  |
| 403    | 无权限访问     |
| 500    | 服务器内部错误 |

## 接口调用示例

### cURL 示例

```bash
curl -X POST "http://localhost:48080/admin-api/publicbiz/order/dropdown-data" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token-here" \
  -d '{
    "orderType": "practice",
    "businessLine": "高校实践"
  }'
```

---

_文档生成时间: 2024年12月_ _接口版本: v1.0_
