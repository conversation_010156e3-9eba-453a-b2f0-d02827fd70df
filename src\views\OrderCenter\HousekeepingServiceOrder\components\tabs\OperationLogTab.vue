<!--
  页面名称：操作日志标签页
  功能描述：展示家政服务订单的操作日志
-->
<template>
  <div class="operation-log-tab">
    <div class="header-actions">
      <el-button type="primary" size="small" @click="handleViewFullLog">
        <el-icon><List /></el-icon>
        查看完整日志
      </el-button>
    </div>

    <div class="log-content">
      <div class="log-list">
        <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
          <div class="log-dot"></div>
          <div class="log-content">
            <div class="log-time">{{ log.time }}</div>
            <div class="log-text">{{ log.content }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Refresh, List } from '@element-plus/icons-vue'

interface Props {
  orderDetail: any
}

defineProps<Props>()

// 操作日志数据
const operationLogs = ref([
  {
    time: '2024/5/28 15:30:00',
    content: '李小明(客服)客户通过电话预约月嫂服务'
  },
  {
    time: '2024/6/1 08:45:00',
    content: '李小明(客服)服务人员已派单,开始服务'
  },
  {
    time: '2024/6/10 08:30:00',
    content: '王小红(运营)张阿姨因家中有急事请假,安排王阿姨顶岗'
  }
])

const handleViewFullLog = () => {
  console.log('查看完整日志')
}
</script>

<style scoped lang="scss">
.operation-log-tab {
  .header-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 20px;
  }

  .log-content {
    .log-list {
      .log-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        position: relative;

        &:not(:last-child)::after {
          content: '';
          position: absolute;
          left: 6px;
          top: 20px;
          bottom: -20px;
          width: 2px;
          background: #e4e7ed;
        }

        .log-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #409eff;
          margin-right: 12px;
          margin-top: 4px;
          flex-shrink: 0;
        }

        .log-content {
          flex: 1;

          .log-time {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }

          .log-text {
            font-size: 14px;
            color: #303133;
            line-height: 1.5;
          }
        }
      }
    }
  }
}
</style>
