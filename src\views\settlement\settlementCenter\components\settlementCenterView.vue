<!--
  页面名称：对账单详情查看
  功能描述：展示对账单详细信息，包含对账单信息、机构信息、金额信息、包含订单等
-->
<template>
  <el-drawer
    v-model="drawerVisible"
    title="对账单详情"
    size="600px"
    :with-header="true"
    direction="rtl"
    class="settlement-detail-drawer"
  >
    <div class="drawer-content">
      <!-- 对账单信息 -->
      <div class="detail-section">
        <div class="section-header">
          <el-icon><Document /></el-icon>
          <span>对账单信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>对账单号：</label>
            <span class="value">{{ reconciliationData?.statementNo }}</span>
          </div>
          <div class="info-item">
            <label>生成时间：</label>
            <span class="value">{{ formatDateTime(reconciliationData?.generationTime) }}</span>
          </div>
          <div class="info-item">
            <label>确认时间：</label>
            <span class="value">{{
              formatDateTime(reconciliationData?.confirmationTime) || '未确认'
            }}</span>
          </div>
          <div class="info-item">
            <label>对账状态：</label>
            <el-tag :type="getStatusType(reconciliationData?.status)" size="small">
              {{ getStatusText(reconciliationData?.status) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 机构信息 -->
      <div class="detail-section">
        <div class="section-header">
          <el-icon><OfficeBuilding /></el-icon>
          <span>机构信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>机构名称：</label>
            <span class="value">{{ reconciliationData?.agencyName }}</span>
          </div>
          <div class="info-item">
            <label>联系人：</label>
            <span class="value">{{ reconciliationData?.agencyContact || '张经理' }}</span>
          </div>
          <div class="info-item">
            <label>联系电话：</label>
            <span class="value">{{ reconciliationData?.agencyPhone || '010-12345678' }}</span>
          </div>
          <div class="info-item">
            <label>订单数量：</label>
            <span class="value">{{ reconciliationData?.orderCount || 5 }}个订单</span>
          </div>
        </div>
      </div>

      <!-- 金额信息 -->
      <div class="detail-section">
        <div class="section-header">
          <el-icon><Money /></el-icon>
          <span>金额信息</span>
        </div>
        <div class="amount-info">
          <div class="amount-item total">
            <label>对账总金额：</label>
            <span class="amount-value total-amount"
              >¥{{ formatAmount(reconciliationData?.totalAmount) }}</span
            >
          </div>
          <div class="amount-item">
            <label>机构分成：</label>
            <span class="amount-value agency-amount"
              >¥{{ formatAmount(reconciliationData?.agencyAmount) }}</span
            >
          </div>
          <div class="amount-item">
            <label>平台分成：</label>
            <span class="amount-value platform-amount"
              >¥{{ formatAmount(reconciliationData?.platformAmount) }}</span
            >
          </div>
          <div class="amount-item">
            <label>分成比例：</label>
            <span class="ratio-value">
              机构{{ reconciliationData?.agencyRatio || 80 }}% : 平台{{
                reconciliationData?.platformRatio || 20
              }}%
            </span>
          </div>
        </div>
      </div>

      <!-- 调整说明 -->
      <div v-if="reconciliationData?.adjustmentReason" class="detail-section">
        <div class="section-header">
          <el-icon><Edit /></el-icon>
          <span>调整说明</span>
        </div>
        <div class="adjustment-info">
          <div class="adjustment-item">
            <label>调整原因：</label>
            <span class="value">{{ reconciliationData.adjustmentReason }}</span>
          </div>
        </div>
      </div>

      <!-- 包含订单 -->
      <div class="detail-section">
        <div class="section-header">
          <el-icon><List /></el-icon>
          <span>包含订单</span>
        </div>
        <div class="order-list">
          <div
            v-for="orderNo in getOrderList()"
            :key="orderNo"
            class="order-item"
            @click="handleOrderClick(orderNo)"
          >
            {{ orderNo }}
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出详情</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Document, OfficeBuilding, Money, Edit, List } from '@element-plus/icons-vue'
import { exportReconciliationDetail } from '@/api/settlement/reconciliation'
import { ElMessage } from 'element-plus'

/** 抽屉显示状态 */
const drawerVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

/** 组件属性 */
const props = defineProps<{
  visible: boolean
  reconciliationData: any
}>()

/** 组件事件 */
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

/** 处理关闭 */
const handleClose = () => {
  drawerVisible.value = false
}

/** 处理导出 */
const handleExport = async () => {
  try {
    await exportReconciliationDetail(props.reconciliationData?.statementNo)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出失败:', error)
  }
}

/** 处理订单点击 */
const handleOrderClick = (orderNo: string) => {
  // 这里可以跳转到订单详情页面
  console.log('点击订单:', orderNo)
}

/** 获取订单列表 */
const getOrderList = () => {
  if (props.reconciliationData?.orderList) {
    try {
      return JSON.parse(props.reconciliationData.orderList)
    } catch {
      return []
    }
  }
  // 模拟数据
  return ['DD20240815001', 'DD20240814002', 'DD20240813003', 'DD20240812004', 'DD20240811005']
}

/** 获取状态类型 */
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    confirmed: 'success',
    paid: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取状态文本 */
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待对账确认',
    confirmed: '已确认',
    paid: '已支付',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 格式化日期时间 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount?.toFixed(2) || '0.00'
}
</script>

<style scoped lang="scss">
.settlement-detail-drawer {
  :deep(.el-drawer__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 0;
    padding: 20px;

    .el-drawer__title {
      color: white;
      font-weight: 600;
    }

    .el-drawer__close-btn {
      color: white;
    }
  }

  .drawer-content {
    padding: 20px;
    height: calc(100% - 120px);
    overflow-y: auto;

    .detail-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      background: #fafafa;

      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-weight: 600;
        color: #303133;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 80px;
          }

          .value {
            color: #303133;
            font-weight: 500;
          }
        }
      }

      .amount-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .amount-item {
          display: flex;
          align-items: center;

          &.total {
            grid-column: 1 / -1;
            justify-content: center;
            padding: 12px;
            background: #fff2f0;
            border-radius: 6px;
            border: 1px solid #ffccc7;
          }

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 100px;
          }

          .amount-value {
            font-weight: 600;
            font-size: 16px;

            &.total-amount {
              color: #f56c6c;
              font-size: 18px;
            }

            &.agency-amount {
              color: #67c23a;
            }

            &.platform-amount {
              color: #409eff;
            }
          }

          .ratio-value {
            color: #409eff;
            font-weight: 500;
          }
        }
      }

      .adjustment-info {
        .adjustment-item {
          display: flex;
          align-items: flex-start;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 80px;
            margin-top: 4px;
          }

          .value {
            color: #303133;
            line-height: 1.5;
          }
        }
      }

      .order-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .order-item {
          padding: 6px 12px;
          background: #f0f9ff;
          border: 1px solid #bae6fd;
          border-radius: 16px;
          color: #0369a1;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: #e0f2fe;
            border-color: #7dd3fc;
            transform: translateY(-1px);
          }
        }
      }
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    border-top: 1px solid #e4e7ed;
    background: white;
  }
}
</style>
