<!--
  页面名称：发票详情弹窗
  功能描述：展示发票详细信息，包含对账单信息、发票信息、操作记录等
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="发票详情"
    width="700px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="invoice-detail-dialog"
  >
    <div class="dialog-content">
      <!-- 对账单信息 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          <span>对账单信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>对账单号：</label>
            <span>{{ invoiceData?.statementNo }}</span>
          </div>
          <div class="info-item">
            <label>对账金额：</label>
            <span class="amount-text">¥{{ formatAmount(invoiceData?.reconciliationAmount) }}</span>
          </div>
          <div class="info-item">
            <label>家政机构：</label>
            <span>{{ invoiceData?.agencyName }}</span>
          </div>
          <div class="info-item">
            <label>开票状态：</label>
            <el-tag :type="getInvoiceStatusType(invoiceData?.invoiceStatus)" size="small">
              {{ getInvoiceStatusText(invoiceData?.invoiceStatus) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 发票信息 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Money /></el-icon>
          <span>发票信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>发票号码：</label>
            <span>{{ invoiceData?.invoiceNo || '-' }}</span>
          </div>
          <div class="info-item">
            <label>发票类型：</label>
            <span>{{ invoiceData?.invoiceType || '-' }}</span>
          </div>
          <div class="info-item">
            <label>税率：</label>
            <span>{{ invoiceData?.taxRate || '6%' }}</span>
          </div>
          <div class="info-item">
            <label>开票日期：</label>
            <span>{{ invoiceData?.invoiceDate || '-' }}</span>
          </div>
          <div class="info-item">
            <label>发票金额：</label>
            <span>{{
              invoiceData?.invoiceAmount ? `¥${formatAmount(invoiceData.invoiceAmount)}` : '-'
            }}</span>
          </div>
          <div class="info-item">
            <label>备注：</label>
            <span>{{ invoiceData?.remark || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 操作记录 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Clock /></el-icon>
          <span>操作记录</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDateTime(invoiceData?.createTime) || '2024-08-16 10:00' }}</span>
          </div>
          <div class="info-item">
            <label>操作人员：</label>
            <span>{{ invoiceData?.operatorName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>更新时间：</label>
            <span>{{ formatDateTime(invoiceData?.updateTime) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>操作状态：</label>
            <span>{{ getOperationStatusText(invoiceData?.invoiceStatus) }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出详情</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Money, Clock } from '@element-plus/icons-vue'
import { exportInvoiceDetail } from '@/api/settlement/invoice'

/** 弹窗显示状态 */
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

/** 组件属性 */
const props = defineProps<{
  visible: boolean
  invoiceData: any
}>()

/** 组件事件 */
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

/** 处理关闭 */
const handleClose = () => {
  dialogVisible.value = false
}

/** 处理导出 */
const handleExport = async () => {
  try {
    await exportInvoiceDetail(props.invoiceData?.statementNo)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出失败:', error)
  }
}

/** 获取开票状态类型 */
const getInvoiceStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    not_invoiced: 'warning',
    invoiced: 'success',
    voided: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取开票状态文本 */
const getInvoiceStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    not_invoiced: '未开票',
    invoiced: '已开票',
    voided: '已作废'
  }
  return statusMap[status] || status
}

/** 获取操作状态文本 */
const getOperationStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    not_invoiced: '待开票',
    invoiced: '已开票',
    voided: '已作废'
  }
  return statusMap[status] || '未知'
}

/** 格式化日期时间 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount?.toFixed(2) || '0.00'
}
</script>

<style scoped lang="scss">
.invoice-detail-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: white;
    }
  }

  .dialog-content {
    .info-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      background: #fafafa;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-weight: 600;
        color: #303133;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 80px;
          }

          span {
            color: #303133;
          }

          .amount-text {
            color: #f56c6c;
            font-weight: 600;
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    gap: 12px;
  }
}
</style>




