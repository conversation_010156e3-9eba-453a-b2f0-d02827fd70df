import request from '@/config/axios'

// 合作伙伴新增
export const createPartner = (data: {
  name: string
  type: string
  biz: string
  status: string
  risk: string
  owner: string
}) => {
  return request.post({ url: '/publicbiz/partner/create', data })
}

// 合作伙伴操作日志新增
export const createPartnerLog = (data: {
  biz_type: string
  biz_id: number
  content: string
  operator: string
}) => {
  return request.post({ url: '/publicbiz/optlog/create', data })
}

// 合作伙伴分页查询
export const getPartnerPage = (params: any) => {
  return request.get({ url: '/publicbiz/partner/page', params })
}

// 合作伙伴统计卡片数据
export const getPartnerStat = () => {
  return request.get({ url: '/publicbiz/partner/stat' })
}

// 合作伙伴详情
export const getPartnerDetail = (id: number) => {
  return request.get({ url: '/publicbiz/partner/detail', params: { id } })
}

// 更新合作伙伴
export const updatePartner = (data: any) => {
  return request.post({ url: '/publicbiz/partner/update', data })
}

// 删除合作伙伴
export const deletePartner = (id: number) => {
  return request.post({ url: '/publicbiz/partner/delete', params: { id } })
}

// 文件上传（certificate目录）
export const uploadFile = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('directory', 'certificate')
  return request.post({
    url: '/infra/file/upload',
    data: formData,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// 获取合作伙伴操作日志
export const getPartnerOperateLog = (params: {
  pageNo: number
  pageSize: number
  bizId: number
  type?: string
}) => {
  return request.get({ url: '/system/operate-log/page', params })
}

// 获取有效状态合作伙伴列表（下拉框数据）
export const getActivePartnerList = (params?: {
  status?: string
  type?: string
  biz?: string
  keyword?: string
}) => {
  return request.get({ url: '/publicbiz/partner/list/active', params })
}
