# 高校实践订单管理模块

## 功能概述

本模块提供高校实践订单的完整管理功能，包括订单创建、编辑、查看、删除、审批流程管理以及**收款信息管理**等核心功能。

## 主要功能

### 1. 订单基础管理

- 订单创建、编辑、删除
- 订单状态管理（草稿、待审批、审批中、已批准、已拒绝、待支付、执行中、已完成、已取消）
- 支付状态管理（待支付、已支付、已退款、已取消）
- 订单搜索和分页查询
- 订单统计概览

### 2. 收款信息管理 ⭐ 新增功能

- **收款确认**：支持确认高校实践订单的收款
- **收款信息更新**：支持更新已收款订单的收款信息
- **收款信息查询**：查询订单的收款信息详情
- **收款方式管理**：支持现金、微信支付、支付宝、银行转账、POS机刷卡等多种收款方式

### 3. 合同管理

- 电子合同发起
- 纸质合同上传
- 合同状态管理
- 合同文件下载

### 4. 审批流程

- 审批流程发起
- 审批状态跟踪
- 操作日志记录

## 收款功能详细说明

### 收款确认流程

1. 订单状态为"已批准"或"待支付"时，可进行收款确认
2. 填写收款金额、收款方式、收款日期、操作人等信息
3. 确认收款后，订单状态自动变为"执行中"，支付状态变为"已支付"

### 收款信息字段

- `collectionAmount`: 收款金额
- `collectionMethod`: 收款方式
- `collectionDate`: 收款日期
- `operatorName`: 操作人
- `collectionRemark`: 收款备注

### 收款方式支持

| 编码          | 名称      | 说明          |
| ------------- | --------- | ------------- |
| cash          | 现金      | 现金收款      |
| wechat        | 微信支付  | 微信支付收款  |
| alipay        | 支付宝    | 支付宝收款    |
| bank_transfer | 银行转账  | 银行转账收款  |
| pos           | POS机刷卡 | POS机刷卡收款 |
| other         | 其他      | 其他收款方式  |

## 技术实现

### 组件结构

```
UniversityPracticeCenter/
├── index.vue                    # 主页面（订单列表）
├── components/
│   ├── AddUniversityPracticeCenter.vue    # 新增/编辑页面
│   ├── IndividualtrainingOrderView.vue    # 详情查看页面
│   ├── CollectionDialog.vue               # 收款确认对话框 ⭐ 新增
│   ├── CollectionInfo.vue                 # 收款信息展示 ⭐ 新增
│   ├── ElectronicContractDialog.vue       # 电子合同对话框
│   ├── PaperContractDialog.vue            # 纸质合同对话框
│   ├── ApprovalDialog.vue                 # 审批对话框
│   └── OptLog.vue                         # 操作日志
└── README.md                              # 说明文档
```

### API接口

收款功能相关的API接口已添加到 `src/api/OrderCenter/UniversityPracticeCenter/index.ts`：

- `confirmCollection`: 确认收款
- `updateCollection`: 更新收款信息
- `getCollectionInfo`: 查询收款信息

### 状态流转

```
订单创建 → 审批通过 → 确认收款 → 订单执行 → 订单完成
   ↓           ↓         ↓         ↓         ↓
  草稿     待支付/已批准  已支付     执行中     已完成
```

## 使用说明

### 1. 确认收款

1. 在订单列表中找到状态为"已批准"或"待支付"的订单
2. 点击"确认收款"按钮
3. 填写收款信息并提交

### 2. 查看收款信息

1. 点击订单的"查看"按钮进入详情页
2. 在详情页中查看"收款信息"区域
3. 已支付的订单可以编辑收款信息

### 3. 编辑收款信息

1. 在收款信息区域点击"编辑收款信息"按钮
2. 修改收款信息并保存

## 注意事项

1. **权限控制**：收款操作需要专门的"收款"权限
2. **数据验证**：收款金额不能超过订单总金额
3. **状态限制**：只有特定状态的订单才能进行收款操作
4. **日志记录**：所有收款操作都会记录详细的操作日志

## 扩展说明

如需添加新的收款方式，只需在 `CollectionMethodEnum` 枚举类中添加新的枚举值即可。系统会自动支持新的收款方式。

## 更新日志

- **2024-06-21**: 新增收款信息管理功能
  - 新增收款确认对话框组件
  - 新增收款信息展示组件
  - 集成收款功能到主页面和详情页面
  - 完善API接口和类型定义
