import request from '@/config/axios'
import qs from 'qs'

// 文章VO
export interface ArticleVO {
  id: number
  title: string
  content: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId: number
  description?: string
  createTime?: string
  visibleOrgId?: number
  visibleOrgName?: string
}

// 文章列表
export const getArticleList = (params: {
  pageNo: number
  pageSize: number
  title?: string
  sourceOrgId?: number
  categoryId?: number
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.get({ url: '/system/material/article/list', params })
}

// 新增文章
export const createArticle = (data: {
  title: string
  content: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/article/create', data })
}

// 编辑文章
export const updateArticle = (data: {
  id: number
  title: string
  content: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/article/update', data })
}

// 删除文章
export const deleteArticle = (id: number) => {
  return request.post({
    url: '/system/material/article/delete',
    data: qs.stringify({ id }),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 文章详情
export const getArticleDetail = (id: number) => {
  return request.get({ url: '/system/material/article/detail', params: { id } })
}
