import request from '@/config/axios'

/**
 * 任务工单管理API接口
 */

// 工单信息接口
export interface WorkOrder {
  id: number
  workOrderNo: string
  orderNo: string
  workOrderTitle: string
  workOrderType: 'complaint' | 'substitution_request' | 'take_leave' | 'leave_adjustment'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  workOrderStatus: 'pending' | 'processing' | 'resolved' | 'closed'
  assigneeId?: number
  assigneeName?: string
  auntName: string
  complainerName?: string
  agencyId: number
  agencyName: string
  remark?: string
  createTime: string
}

// 工单详情接口
export interface WorkOrderDetail extends WorkOrder {
  workOrderContent: string
  auntOneid: string
  leaveType?: number
  startTime?: string
  endTime?: string
  durationHours?: number
  durationDays?: number
  status?: number
  approveTime?: string
  approveRemark?: string
  complaintType?: string
  complaintLevel?: string
  complaintTime?: string
  customerExpectation?: string
  complainerId?: number
  reassignmentStartDate?: string
  newAuntOneid?: string
  newAuntName?: string
  reassignmentDescription?: string
  reassignmentReason?: string
  reassignmentRemark?: string
  taskCount?: number
  completedTaskCount?: number
  pendingTaskCount?: number
  updateTime?: string
}

// 工单分页查询参数接口
export interface WorkOrderQueryParams {
  pageNo: number
  pageSize: number
  workOrderType?: string
  workOrderStatus?: string
  priority?: string
  agencyName?: string
}

// 工单分页查询响应接口
export interface WorkOrderPageResponse {
  list: WorkOrder[]
  total: number
}

// 工单类型统计响应接口
export interface WorkOrderTypeStats {
  all: number
  complaint: number
  substitution_request: number
  take_leave: number
  leave_adjustment?: number
}

// 工单接单参数接口
export interface WorkOrderAcceptParams {
  workOrderId: number
  assigneeId: number
  assigneeName: string
  acceptRemark?: string
}

// 工单接单响应接口
export interface WorkOrderAcceptResponse {
  success: boolean
  workOrderStatus: string
}

// 工单转派参数接口
export interface WorkOrderTransferParams {
  workOrderId: number
  newAssigneeId: number
  newAssigneeName: string
  transferReason: string
  transferRemark?: string
  priority?: string
}

// 工单转派响应接口
export interface WorkOrderTransferResponse {
  success: boolean
  workOrderStatus: string
}

// 工单处理日志接口
export interface WorkOrderLog {
  id: number
  logType: string
  logTitle: string
  logContent: string
  operatorId?: number
  operatorName: string
  operatorRole: string
  relatedPartyType: string
  relatedPartyName: string
  createTime: string
}

// 工单附件接口
export interface WorkOrderAttachment {
  id: number
  fileName: string
  fileUrl: string
  fileType: string
  fileCategory: string
  uploadPurpose?: string
  createTime: string
}

// 阿姨信息接口
export interface AuntInfo {
  id: number
  auntOneid: string
  name: string
  rating: number
  serviceType: string
}

// 任务信息接口
export interface TaskInfo {
  id: number
  taskNo: string
  taskSequence: number
  plannedStartTime: string
  taskStatus: string
  practitionerName: string
  practitionerOneid: string
  actualEndTime?: string
}

// 任务详情接口
export interface TaskDetail {
  orderNo: string
  serviceCategoryName: string
  taskCount: number
  completedTaskCount: number
  pendingTaskCount: number
  cancelledTaskCount: number
  taskProgress: number
}

/**
 * 工单分页查询
 * @param params 查询参数
 * @returns Promise<WorkOrderPageResponse>
 */
export function getWorkOrderPage(params: WorkOrderQueryParams) {
  return request.post({
    url: '/publicbiz/work-order/page',
    data: params
  })
}

/**
 * 工单类型统计
 * @returns Promise<WorkOrderTypeStats>
 */
export function getWorkOrderTypeStats() {
  return request.get({
    url: '/publicbiz/work-order/typeStats'
  })
}

/**
 * 工单详情查询
 * @param workOrderNo 工单编号
 * @returns Promise<WorkOrderDetail>
 */
export function getWorkOrderDetail(workOrderNo: string) {
  return request.get({
    url: `/publicbiz/work-order/detail/${workOrderNo}`
  })
}

/**
 * 工单接单
 * @param data 接单参数
 * @returns Promise<WorkOrderAcceptResponse>
 */
export function acceptWorkOrder(data: WorkOrderAcceptParams) {
  return request.post({
    url: '/publicbiz/work-order/accept',
    data
  })
}

/**
 * 工单转派
 * @param data 转派参数
 * @returns Promise<WorkOrderTransferResponse>
 */
export function transferWorkOrder(data: WorkOrderTransferParams) {
  return request.post({
    url: '/publicbiz/work-order/transfer',
    data
  })
}

/**
 * 工单处理日志查询
 * @param workOrderNo 工单编号
 * @returns Promise<WorkOrderLog[]>
 */
export function getWorkOrderLogs(workOrderNo: string) {
  return request.get({
    url: `/publicbiz/work-order/logs/${workOrderNo}`
  })
}

/**
 * 工单附件上传
 * @param data 附件数据
 * @returns Promise<WorkOrderAttachment>
 */
export function uploadWorkOrderAttachment(data: FormData) {
  return request.post({
    url: '/publicbiz/work-order/uploadAttachment',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 工单附件列表查询
 * @param workOrderNo 工单编号
 * @returns Promise<WorkOrderAttachment[]>
 */
export function getWorkOrderAttachments(workOrderNo: string) {
  return request.get({
    url: `/publicbiz/work-order/attachments/${workOrderNo}`
  })
}

/**
 * 获取阿姨列表
 * @returns Promise<AuntInfo[]>
 */
export function getWorkOrderPractitioners() {
  return request.get({
    url: '/publicbiz/work-order/workOrderPractitioners'
  })
}

/**
 * 获取服务任务详情
 * @param orderNo 订单号
 * @returns Promise<TaskDetail>
 */
export function getTaskDetail(orderNo: string) {
  return request.get({
    url: `/publicbiz/work-order/taskDetail/${orderNo}`
  })
}

/**
 * 获取任务列表
 * @param orderNo 订单号
 * @param params 查询参数
 * @returns Promise<TaskInfo[]>
 */
export function getTaskList(
  orderNo: string,
  params?: {
    taskStatus?: string
    practitionerName?: string
    practitionerOneid?: string
  }
) {
  return request.get({
    url: `/publicbiz/work-order/taskList/${orderNo}`,
    params
  })
}

/**
 * 重新指派任务
 * @param data 指派参数
 * @returns Promise<{success: boolean, reassignedCount: number, failedTasks: any[]}>
 */
export function reassignTask(data: {
  taskNoList: string[]
  practitionerName: string
  practitionerOneid: string
}) {
  return request.post({
    url: '/publicbiz/work-order/reassignTask',
    data
  })
}

/**
 * 提交处理结果
 * @param data 处理结果参数
 * @returns Promise<{success: boolean, workOrderStatus: string, logId: number}>
 */
export function submitResolution(data: {
  workOrderNo: string
  logType: string
  logContent: string
}) {
  return request.post({
    url: '/publicbiz/work-order/submitResolution',
    data
  })
}

/**
 * 指派阿姨
 * @param data 指派参数
 * @returns Promise<{success: boolean, reassignedTaskCount: number, failedTasks: any[]}>
 */
export function assignAunt(data: {
  orderNo: string
  plannedStartTime: string
  newAuntOneid: string
  newAuntName: string
  reassignmentDescription: string
}) {
  return request.post({
    url: '/publicbiz/work-order/assignAunt',
    data
  })
}

// 保留原有的任务相关接口，以保持向后兼容
export interface Task {
  id: string
  type: 'complaint' | 'replacement' | 'refund' | 'rework' | 'leave' | 'resignation' | 'other'
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  assignee: string
  agencyId: string
  agencyName: string
  customerName: string
  customerPhone: string
  createTime: string
  updateTime: string
  dueTime: string
  completedTime?: string
}

// 查询参数接口
export interface TaskQueryParams {
  type?: string
  status?: string
  priority?: string
  agencyId?: string
  assignee?: string
  keyword?: string
  pageNum: number
  pageSize: number
}

// 新增任务参数接口
export interface CreateTaskParams {
  type: 'complaint' | 'replacement' | 'refund' | 'rework' | 'leave' | 'resignation' | 'other'
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  assignee: string
  agencyId: string
  customerName: string
  customerPhone: string
  dueTime: string
}

// 更新任务参数接口
export interface UpdateTaskParams {
  id: string
  title?: string
  description?: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  status?: 'pending' | 'processing' | 'completed' | 'cancelled'
  assignee?: string
  dueTime?: string
}

/**
 * 获取任务列表
 * @param params 查询参数
 * @returns Promise<{ list: Task[], total: number }>
 */
export function getTaskListOld(params: TaskQueryParams) {
  return request.get({
    url: '/mall/employment/task/list',
    params
  })
}

/**
 * 获取任务详情
 * @param id 任务ID
 * @returns Promise<Task>
 */
export function getTaskDetailOld(id: string) {
  return request.get({
    url: `/mall/employment/task/${id}`
  })
}

/**
 * 新增任务
 * @param data 任务信息
 * @returns Promise<void>
 */
export function createTask(data: CreateTaskParams) {
  return request.post({
    url: '/mall/employment/task',
    data
  })
}

/**
 * 更新任务
 * @param data 更新信息
 * @returns Promise<void>
 */
export function updateTask(data: UpdateTaskParams) {
  return request.put({
    url: `/mall/employment/task/${data.id}`,
    data
  })
}

/**
 * 删除任务
 * @param id 任务ID
 * @returns Promise<void>
 */
export function deleteTask(id: string) {
  return request.delete({
    url: `/mall/employment/task/${id}`
  })
}

/**
 * 更新任务状态
 * @param id 任务ID
 * @param status 状态
 * @returns Promise<void>
 */
export function updateTaskStatus(
  id: string,
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
) {
  return request.put({
    url: `/mall/employment/task/${id}/status`,
    data: { status }
  })
}

/**
 * 分配任务
 * @param id 任务ID
 * @param assignee 负责人
 * @returns Promise<void>
 */
export function assignTask(id: string, assignee: string) {
  return request.put({
    url: `/mall/employment/task/${id}/assign`,
    data: { assignee }
  })
}

/**
 * 完成任务
 * @param id 任务ID
 * @param result 完成结果
 * @returns Promise<void>
 */
export function completeTask(id: string, result: string) {
  return request.put({
    url: `/mall/employment/task/${id}/complete`,
    data: { result }
  })
}

/**
 * 导出任务列表
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export function exportTaskList(params: Omit<TaskQueryParams, 'pageNum' | 'pageSize'>) {
  return request.download({
    url: '/mall/employment/task/export',
    params
  })
}

/**
 * 获取任务类型统计
 * @returns Promise<{ [key: string]: number }>
 */
export function getTaskTypeStatsOld() {
  return request.get({
    url: '/mall/employment/task/stats'
  })
}
