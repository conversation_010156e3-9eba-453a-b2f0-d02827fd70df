<!--
  页面名称：服务任务列表
  功能描述：展示服务任务列表，支持筛选、批量重新指派，包含订单信息和任务统计
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="服务任务列表"
    width="800px"
    :before-close="handleClose"
    class="service-task-dialog"
  >
    <!-- 订单信息区域 -->
    <div class="order-info-section">
      <h3 class="section-title">订单信息</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <div class="label">订单号</div>
            <div class="value">{{ orderInfo.orderNo || '-' }}</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <div class="label">总任务数</div>
            <div class="value">{{ orderInfo.totalTasks || 0 }}个</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <div class="label">服务类型</div>
            <div class="value">{{ orderInfo.serviceType || '-' }}</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <div class="label">已完成</div>
            <div class="value">{{ orderInfo.completedTasks || 0 }}个</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 任务列表区域 -->
    <div class="task-list-section">
      <h3 class="section-title">任务列表</h3>

      <!-- 任务统计 -->
      <div class="task-statistics">
        <div class="stat-item">
          <span class="stat-number total">{{ taskStats.total || 0 }}</span>
          <span class="stat-label">总任务数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number completed">{{ taskStats.completed || 0 }}</span>
          <span class="stat-label">已完成</span>
        </div>
        <div class="stat-item">
          <span class="stat-number pending">{{ taskStats.pending || 0 }}</span>
          <span class="stat-label">待执行</span>
        </div>
        <div class="stat-item">
          <span class="stat-number canceled">{{ taskStats.canceled || 0 }}</span>
          <span class="stat-label">已取消</span>
        </div>
      </div>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :inline="true" class="filter-form">
          <el-form-item label="任务状态">
            <el-select
              v-model="filterForm.taskStatus"
              placeholder="全部"
              clearable
              style="width: 100px"
            >
              <el-option label="全部" value="" />
              <el-option label="待执行" value="pending" />
              <el-option label="执行中" value="processing" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="canceled" />
            </el-select>
          </el-form-item>
          <el-form-item label="执行人员">
            <el-input
              v-model="filterForm.practitionerName"
              placeholder="请输入执行人员姓名"
              clearable
              style="width: 120px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">筛选</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 任务表格 -->
      <div class="task-table-container">
        <el-table :data="taskList" style="width: 100%" height="400" v-loading="loading">
          <el-table-column prop="taskNo" label="任务序号" width="100" />
          <el-table-column prop="plannedStartTime" label="服务时间" width="120">
            <template #default="scope">
              {{ formatServiceDate(scope.row.plannedStartTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="taskStatus" label="任务状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.taskStatus)" size="small">
                {{ getStatusText(scope.row.taskStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="practitionerName" label="执行人员" width="180" />
          <el-table-column prop="actualEndTime" label="完成时间" width="160">
            <template #default="scope">
              {{ formatServiceDate(scope.row.actualEndTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button
                v-if="scope.row.taskStatus === 'pending'"
                size="small"
                @click="handleReassign(scope.row)"
              >
                重新指派
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleBatchReassign">批量重新指派</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 重新指派弹窗 -->
  <el-dialog
    v-model="reassignDialogVisible"
    :title="selectedTask ? `重新指派任务 - ${selectedTask.taskNo}` : '重新指派任务'"
    width="400px"
    :before-close="handleReassignClose"
  >
    <div class="reassign-content">
      <!-- 显示任务信息 -->
      <div v-if="selectedTask" class="task-info">
        <p><strong>任务序号：</strong>{{ selectedTask.taskNo }}</p>
        <p><strong>服务时间：</strong>{{ formatServiceDate(selectedTask.plannedStartTime) }}</p>
        <p><strong>当前执行人员：</strong>{{ selectedTask.practitionerName || '-' }}</p>
      </div>
      
      <el-divider />
      
      <p class="instruction">请输入新阿姨姓名和ID (格式: 姓名(ID))</p>
      <el-input v-model="newExecutor" placeholder="请输入新阿姨姓名和ID" clearable />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleReassignClose">取消</el-button>
        <el-button type="primary" @click="handleConfirmReassign">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTaskDetail, getTaskList, reassignTask } from '@/api/mall/employment/workOrder'

/** 对话框显示状态 */
const dialogVisible = ref(false)

/** 重新指派弹窗显示状态 */
const reassignDialogVisible = ref(false)

/** 新执行人员信息 */
const newExecutor = ref('')

/** 当前选中的任务 */
const selectedTask = ref<any>(null)

/** 加载状态 */
const loading = ref(false)

/** 当前订单号 */
const currentOrderNo = ref('')

/** 订单信息数据 */
const orderInfo = ref({
  orderNo: '',
  totalTasks: 0,
  serviceType: '',
  completedTasks: 0
})

/** 任务统计数据 */
const taskStats = ref({
  total: 0,
  completed: 0,
  pending: 0,
  canceled: 0
})

/** 筛选表单数据 */
const filterForm = reactive({
  taskStatus: '',
  practitionerOneid: '',
  practitionerName: ''
})

/** 任务列表数据 */
const taskList = ref([])

/**
 * 获取状态类型
 * @param status 状态值
 * @returns 状态类型
 */
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    canceled: 'info'
  }
  return statusMap[status] || 'info'
}

/**
 * 获取状态文本
 * @param status 状态值
 * @returns 状态文本
 */
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '待执行',
    processing: '执行中',
    completed: '已完成',
    canceled: '已取消'
  }
  return statusMap[status] || '未知'
}

/**
 * 格式化服务时间为 yyyy/MM/dd 格式
 * @param dateStr 日期字符串
 * @returns 格式化后的日期字符串
 */
const formatServiceDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return '-'
    
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    
    return `${year}/${month}/${day}`
  } catch (error) {
    return '-'
  }
}

/**
 * 获取订单详情
 */
const fetchOrderDetail = async () => {
  if (!currentOrderNo.value) return

  try {
    loading.value = true
    const response = await getTaskDetail(currentOrderNo.value)
    const data = response.data || response
    orderInfo.value = {
      orderNo: data.orderNo || '',
      totalTasks: data.taskCount || 0,
      serviceType: data.serviceCategoryName || '',
      completedTasks: data.completedTaskCount || 0
    }

    // 更新任务统计
    taskStats.value = {
      total: data.taskCount || 0,
      completed: data.completedTaskCount || 0,
      pending: data.pendingTaskCount || 0,
      canceled: data.cancelledTaskCount || 0
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

/**
 * 获取任务列表
 */
const fetchTaskList = async () => {
  if (!currentOrderNo.value) return

  try {
    loading.value = true
    const params: any = {}

    // 只传递有值的筛选参数
    if (filterForm.taskStatus) {
      params.taskStatus = filterForm.taskStatus
    }
    if (filterForm.practitionerOneid) {
      params.practitionerOneid = filterForm.practitionerOneid
    }
    if (filterForm.practitionerName) {
      params.practitionerName = filterForm.practitionerName
    }

    const response = await getTaskList(currentOrderNo.value, params)
    taskList.value = response.data || response || []
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理筛选
 */
const handleFilter = () => {
  fetchTaskList()
  ElMessage.success('筛选条件已应用')
}

/**
 * 处理重置
 */
const handleReset = () => {
  filterForm.taskStatus = ''
  filterForm.practitionerOneid = ''
  filterForm.practitionerName = ''
  fetchTaskList()
  ElMessage.success('筛选条件已重置')
}

/**
 * 处理重新指派
 * @param row 行数据
 */
const handleReassign = (row: any) => {
  // 设置当前选中的任务
  selectedTask.value = row
  // 打开重新指派弹窗
  reassignDialogVisible.value = true
}

/**
 * 处理批量重新指派
 */
const handleBatchReassign = () => {
  // 获取所有待执行状态的任务
  const pendingTasks = taskList.value.filter((task) => task.taskStatus === 'pending')

  if (pendingTasks.length === 0) {
    ElMessage.warning('没有待执行的任务可以重新指派')
    return
  }

  // 打开重新指派弹窗
  newExecutor.value = ''
  reassignDialogVisible.value = true
}

/**
 * 处理重新指派弹窗关闭
 */
const handleReassignClose = () => {
  reassignDialogVisible.value = false
  newExecutor.value = ''
  selectedTask.value = null
}

/**
 * 处理确认重新指派
 */
const handleConfirmReassign = async () => {
  if (!newExecutor.value.trim()) {
    ElMessage.warning('请输入新阿姨姓名和ID')
    return
  }

  try {
    let taskNoList: string[] = []
    let successMessage = ''

    if (selectedTask.value) {
      // 单个任务重新指派
      taskNoList = [String(selectedTask.value.taskNo)]
      successMessage = `任务"${selectedTask.value.taskNo}"重新指派成功`
    } else {
      // 批量重新指派
      const pendingTasks = taskList.value.filter((task) => task.taskStatus === 'pending')
      taskNoList = pendingTasks.map((task) => String(task.taskNo))
      successMessage = `批量重新指派成功，共更新 ${pendingTasks.length} 个待执行任务`
    }

    // 解析新执行人员信息
    const formatRegex = /^(.+)\(([^)]+)\)$/
    const match = newExecutor.value.match(formatRegex)
    
    if (!match) {
      ElMessage.warning('请输入正确的格式：姓名(ID)')
      return
    }
    
    const newPractitionerName = match[1].trim()
    const newPractitionerOneid = match[2].trim()
    
    // 调用重新指派接口
    const response = await reassignTask({
      taskNoList,
      newPractitionerName,
      newPractitionerOneid
    })

    ElMessage.success(successMessage)
    handleReassignClose()
    // 重新获取数据
    fetchOrderDetail()
    fetchTaskList()
  } catch (error) {
    console.error('重新指派失败:', error)
    ElMessage.error('重新指派失败')
  }
}

/**
 * 处理关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
  // 重置数据
  orderInfo.value = {
    orderNo: '',
    totalTasks: 0,
    serviceType: '',
    completedTasks: 0
  }
  taskStats.value = {
    total: 0,
    completed: 0,
    pending: 0,
    canceled: 0
  }
  taskList.value = []
  currentOrderNo.value = ''
}

/**
 * 打开对话框
 * @param orderNo 订单号
 */
const openDialog = (orderNo: string) => {
  currentOrderNo.value = orderNo
  dialogVisible.value = true

  // 获取数据
  fetchOrderDetail()
  fetchTaskList()
}

// 暴露方法供父组件调用
defineExpose({
  openDialog
})
</script>

<style scoped lang="scss">
.service-task-dialog {
  .order-info-section {
    margin-bottom: 24px;
    padding: 16px;

    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .info-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 16px;
      padding: 12px;

      .label {
        font-weight: 500;
        color: #606266;
        font-size: 14px;
        margin-bottom: 4px;
      }

      .value {
        color: #303133;
        font-weight: 600;
        font-size: 16px;
      }
    }
  }

  .task-list-section {
    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .task-statistics {
      display: flex;
      gap: 24px;
      margin-bottom: 20px;
      padding: 16px;
      background-color: #f5f7fa;
      border-radius: 8px;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        .stat-number {
          font-size: 24px;
          font-weight: 700;

          &.total {
            color: #409eff;
          }

          &.completed {
            color: #67c23a;
          }

          &.pending {
            color: #e6a23c;
          }

          &.canceled {
            color: #909399;
          }
        }

        .stat-label {
          font-size: 12px;
          color: #606266;
        }
      }
    }

    .filter-section {
      margin-bottom: 16px;

      .filter-form {
        .el-form-item {
          margin-bottom: 16px;
        }
      }
    }

    .task-table-container {
      border: 1px solid #ebeef5;
      border-radius: 4px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .reassign-content {
    .task-info {
      margin-bottom: 16px;
      
      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
        
        strong {
          color: #303133;
          margin-right: 8px;
        }
      }
    }
    
    .instruction {
      margin: 0 0 16px 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .service-task-dialog {
    .task-statistics {
      flex-wrap: wrap;
      gap: 16px;

      .stat-item {
        flex: 1;
        min-width: 80px;
      }
    }
  }
}
</style>
taskIds