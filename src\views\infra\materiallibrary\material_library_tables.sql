-- 素材分类表
CREATE TABLE `material_category` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `name` VARCHAR(128) NOT NULL COMMENT '分类名称',
  `parent_id` BIGINT DEFAULT NULL COMMENT '父级分类ID，顶级为NULL',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '分类描述',
  `sort` INT DEFAULT 0 COMMENT '排序值，越小越靠前',
  -- 公共字段
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='素材分类表';

-- 素材图片表
CREATE TABLE `material_image` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `name` VARCHAR(128) NOT NULL COMMENT '图片名称',
  `category_id` BIGINT NOT NULL COMMENT '所属分类ID',
  `url` VARCHAR(512) NOT NULL COMMENT '图片内容URL',
  `source_org_id` BIGINT DEFAULT NULL COMMENT '来源机构ID',
  `source_org_name` VARCHAR(128) DEFAULT NULL COMMENT '来源机构名称',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '图片描述',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态，1-正常，0-禁用',
  -- 公共字段
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='素材图片表';

-- 素材视频表
CREATE TABLE `material_video` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `name` VARCHAR(128) NOT NULL COMMENT '视频名称',
  `category_id` BIGINT NOT NULL COMMENT '所属分类ID',
  `url` VARCHAR(512) NOT NULL COMMENT '视频内容URL',
  `cover_url` VARCHAR(512) DEFAULT NULL COMMENT '视频封面URL',
  `duration` INT DEFAULT NULL COMMENT '视频时长（秒）',
  `source_org_id` BIGINT DEFAULT NULL COMMENT '来源机构ID',
  `source_org_name` VARCHAR(128) DEFAULT NULL COMMENT '来源机构名称',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '视频描述',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态，1-正常，0-禁用',
  -- 公共字段
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='素材视频表';

-- 素材文章表
CREATE TABLE `material_article` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `title` VARCHAR(256) NOT NULL COMMENT '文章标题',
  `category_id` BIGINT NOT NULL COMMENT '所属分类ID',
  `content` TEXT NOT NULL COMMENT '文章内容',
  `source_org_id` BIGINT DEFAULT NULL COMMENT '来源机构ID',
  `source_org_name` VARCHAR(128) DEFAULT NULL COMMENT '来源机构名称',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '文章摘要',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态，1-正常，0-禁用',
  -- 公共字段
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='素材文章表';

-- 素材文档表
CREATE TABLE `material_document` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `name` VARCHAR(128) NOT NULL COMMENT '文档名称',
  `category_id` BIGINT NOT NULL COMMENT '所属分类ID',
  `url` VARCHAR(512) NOT NULL COMMENT '文档内容URL',
  `file_type` VARCHAR(32) DEFAULT NULL COMMENT '文档类型（如pdf、docx等）',
  `source_org_id` BIGINT DEFAULT NULL COMMENT '来源机构ID',
  `source_org_name` VARCHAR(128) DEFAULT NULL COMMENT '来源机构名称',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '文档描述',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态，1-正常，0-禁用',
  -- 公共字段
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='素材文档表';

-- 素材图文表
CREATE TABLE `material_graphic` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `name` VARCHAR(128) NOT NULL COMMENT '图文名称',
  `category_id` BIGINT NOT NULL COMMENT '所属分类ID',
  `content` TEXT NOT NULL COMMENT '图文内容',
  `cover_url` VARCHAR(512) DEFAULT NULL COMMENT '封面图片URL',
  `source_org_id` BIGINT DEFAULT NULL COMMENT '来源机构ID',
  `source_org_name` VARCHAR(128) DEFAULT NULL COMMENT '来源机构名称',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '图文描述',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态，1-正常，0-禁用',
  -- 公共字段
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='素材图文表';

-- 文档类型表
CREATE TABLE `material_document_type` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `name` VARCHAR(64) NOT NULL COMMENT '文档类型名称',
  `code` VARCHAR(32) NOT NULL COMMENT '文档类型编码（如pdf、docx、xls等）',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '类型描述',
  `sort` INT DEFAULT 0 COMMENT '排序值，越小越靠前',
  -- 公共字段
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档类型表';

-- 建议为常用字段加索引
CREATE INDEX idx_material_category_parent_id ON material_category(parent_id);
CREATE INDEX idx_material_image_category_id ON material_image(category_id);
CREATE INDEX idx_material_video_category_id ON material_video(category_id);
CREATE INDEX idx_material_article_category_id ON material_article(category_id);
CREATE INDEX idx_material_document_category_id ON material_document(category_id);
CREATE INDEX idx_material_graphic_category_id ON material_graphic(category_id);
CREATE UNIQUE INDEX idx_document_type_code ON material_document_type(code);

-- 外键说明（不加约束，仅注释说明）
-- material_image.category_id、material_video.category_id、material_article.category_id、material_document.category_id、material_graphic.category_id 均为 material_category.id 的外键
-- source_org_id 字段为机构表的外键
-- material_document.file_type 字段可与 material_document_type.code 逻辑关联 