<!--
  页面名称：新建高校实践订单
  功能描述：新建高校实践订单，支持关联商机、关联线索自动填充，项目信息填写
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="drawerTitle"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="top"
      class="order-form"
    >
      <!-- 项目信息 -->
      <div class="form-section">
        <div class="section-title">项目信息</div>

        <!-- 关联商机 -->
        <el-form-item label="关联商机" prop="businessOpportunity">
          <el-select
            v-model="form.businessOpportunity"
            placeholder="请选择关联商机"
            style="width: 100%"
            @change="handleBusinessOpportunityChange"
          >
            <el-option label="请选择关联商机" value="" />
            <el-option
              v-for="opportunity in businessOpportunities"
              :key="opportunity.id"
              :label="`${opportunity.name} - ${opportunity.customerName}`"
              :value="opportunity.id"
            />
          </el-select>

          <!-- 商机信息展示 -->
          <div v-if="selectedOpportunity" class="info-box business-opportunity-info">
            <div class="info-header">
              <el-icon class="info-icon"><InfoFilled /></el-icon>
              <span class="info-title">商机信息已自动填充</span>
            </div>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">商机描述：</span>
                <span class="info-value">{{ selectedOpportunity.name }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">预估金额：</span>
                <span class="info-value amount">¥{{ selectedOpportunity.totalPrice }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">创建时间：</span>
                <span class="info-value">2024-05-15</span>
              </div>
              <div class="info-item">
                <span class="info-label">商机状态：</span>
                <el-tag
                  :type="getOpportunityStatusType(selectedOpportunity.businessStage)"
                  size="small"
                >
                  {{ selectedOpportunity.businessStage }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="form-tip">选择关联商机可自动填充部分项目信息</div>
        </el-form-item>

        <!-- 关联线索 -->
        <el-form-item label="关联线索" prop="lead">
          <el-select
            v-model="form.lead"
            placeholder="请选择关联线索"
            style="width: 100%"
            @change="handleLeadChange"
          >
            <el-option label="请选择关联线索" value="" />
            <el-option
              v-for="lead in leads"
              :key="lead.id"
              :label="`${lead.leadId} - ${lead.customerName} ${lead.customerPhone}`"
              :value="lead.id"
            />
          </el-select>

          <!-- 线索信息展示 -->
          <div v-if="selectedLead" class="info-box lead-info">
            <div class="info-header">
              <el-icon class="info-icon lead-icon"><InfoFilled /></el-icon>
              <span class="info-title">线索信息已自动填充</span>
            </div>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">联系人：</span>
                <span class="info-value">{{ selectedLead.customerName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">联系电话：</span>
                <span class="info-value phone">{{ selectedLead.customerPhone }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">线索来源：</span>
                <span class="info-value">{{ selectedLead.leadSource }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">线索描述：</span>
                <span class="info-value">{{ selectedLead.businessModule }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">线索状态：</span>
                <el-tag :type="getLeadStatusType(selectedLead.leadStatus)" size="small">
                  {{ selectedLead.leadStatus }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="form-tip">选择关联线索可自动填充客户信息</div>
        </el-form-item>

        <!-- 项目名称 -->
        <el-form-item label="项目名称" prop="projectName" required>
          <el-input
            v-model="form.projectName"
            placeholder="请输入项目名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <!-- 合作高校 -->
        <el-form-item label="合作高校" prop="universityName" required>
          <el-input
            v-model="form.universityName"
            placeholder="请输入合作高校名称"
            maxlength="100"
          />
        </el-form-item>

        <!-- 合作企业 -->
        <el-form-item label="合作企业" prop="enterpriseName" required>
          <el-input
            v-model="form.enterpriseName"
            placeholder="请输入合作企业名称"
            maxlength="100"
          />
        </el-form-item>

        <!-- 项目周期 -->
        <el-form-item label="项目周期" prop="startDate" required>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-date-picker
                v-model="form.startDate"
                type="date"
                placeholder="开始日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-col>
            <el-col :span="12">
              <el-date-picker
                v-model="form.endDate"
                type="date"
                placeholder="结束日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-col>
          </el-row>
        </el-form-item>

        <!-- 项目负责人 -->
        <el-form-item label="项目负责人" prop="managerName" required>
          <el-input v-model="form.managerName" placeholder="请输入项目负责人" maxlength="50" />
        </el-form-item>

        <!-- 订单金额 -->
        <el-form-item label="订单金额" prop="totalAmount" required>
          <el-input
            v-model.number="form.totalAmount"
            placeholder="请输入订单金额"
            type="number"
            min="0"
            style="width: 100%"
          >
            <template #prefix>¥</template>
          </el-input>
        </el-form-item>

        <!-- 支付状态 -->
        <el-form-item label="支付状态" prop="paymentStatus">
          <el-select v-model="form.paymentStatus" placeholder="请选择支付状态" style="width: 100%">
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已退款" value="refunded" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
          <div class="form-tip">选择"已支付"后将显示支付信息填写表单</div>
        </el-form-item>

        <!-- 支付信息 - 当支付状态为"已支付"时显示 -->
        <div v-if="form.paymentStatus === 'paid'" class="form-section payment-section">
          <div class="section-title">
            <el-icon class="section-icon"><Wallet /></el-icon>
            支付信息
          </div>

          <!-- 收款金额 -->
          <el-form-item label="收款金额" prop="collectionAmount" required>
            <el-input
              v-model.number="form.collectionAmount"
              placeholder="请输入实际收款金额"
              type="number"
              min="0"
              style="width: 100%"
            >
              <template #prefix>¥</template>
            </el-input>
            <div class="form-tip">建议收款金额与订单金额保持一致</div>
          </el-form-item>

          <!-- 收款方式 -->
          <el-form-item label="收款方式" prop="collectionMethod" required>
            <el-select
              v-model="form.collectionMethod"
              placeholder="请选择收款方式"
              style="width: 100%"
            >
              <el-option label="现金" value="cash" />
              <el-option label="微信支付" value="wechat" />
              <el-option label="支付宝" value="alipay" />
              <el-option label="银行转账" value="bank_transfer" />
              <el-option label="POS机刷卡" value="pos" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>

          <!-- 收款日期 -->
          <el-form-item label="收款日期" prop="collectionDate" required>
            <el-date-picker
              v-model="form.collectionDate"
              type="date"
              placeholder="年-月-日 --:--"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>

          <!-- 操作人 -->
          <el-form-item label="操作人" prop="operatorName" required>
            <el-input
              v-model="form.operatorName"
              placeholder="请输入操作人姓名"
              maxlength="50"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 收款备注 -->
          <el-form-item label="收款备注" prop="collectionRemark">
            <el-input
              v-model="form.collectionRemark"
              type="textarea"
              :rows="3"
              placeholder="请输入收款备注信息(可选)"
              maxlength="200"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </div>

        <!-- 合同类型 -->
        <el-form-item label="合同类型" prop="contractType">
          <el-radio-group v-model="form.contractType">
            <el-radio value="electronic">电子合同</el-radio>
            <el-radio value="paper">纸质合同</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 附件管理 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon class="section-icon"><Document /></el-icon>
            附件管理
          </div>

          <!-- 附件上传 -->
          <el-form-item label="上传附件">
            <el-upload
              ref="attachmentUploadRef"
              action="#"
              :auto-upload="false"
              :on-change="handleAttachmentChange"
              :on-remove="handleAttachmentRemove"
              :file-list="attachmentList"
              :limit="5"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx"
              class="attachment-upload"
            >
              <el-button type="primary" size="small">
                <el-icon><Plus /></el-icon>
                选择附件
              </el-button>
              <template #tip>
                <div class="upload-tip"
                  >支持格式: PDF、Word、Excel、JPG、PNG, 单个文件不超过10MB, 最多5个文件</div
                >
              </template>
            </el-upload>
          </el-form-item>

          <!-- 附件列表展示 -->
          <div v-if="attachmentList.length > 0" class="attachment-list">
            <div class="attachment-header">
              <span class="attachment-title">已选择附件 ({{ attachmentList.length }}/5)</span>
              <div v-if="form.contractFileUrl" class="contract-url-info">
                <el-tag type="success" size="small">合同附件已上传</el-tag>
                <span class="url-text">{{ form.contractFileUrl }}</span>
              </div>
            </div>
            <div class="attachment-items">
              <div v-for="(file, index) in attachmentList" :key="index" class="attachment-item">
                <div class="attachment-info">
                  <el-icon class="file-icon"><Document /></el-icon>
                  <span class="file-name">{{ file.name }}</span>
                  <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
                  <el-tag
                    :type="
                      file.status === 'success'
                        ? 'success'
                        : file.status === 'fail'
                          ? 'danger'
                          : 'warning'
                    "
                    size="small"
                  >
                    {{
                      file.status === 'success'
                        ? '上传成功'
                        : file.status === 'fail'
                          ? '上传失败'
                          : '上传中'
                    }}
                  </el-tag>
                </div>
                <div class="attachment-actions">
                  <el-button type="danger" size="small" @click="removeAttachment(index)">
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled, Wallet, Document, Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadInstance, UploadFile } from 'element-plus'
import type {
  UniversityPracticeOrder,
  AddOrderParams,
  UpdateOrderParams,
  BusinessOption,
  LeadOption,
  CustomerOption,
  ManagerOption
} from '@/api/OrderCenter/UniversityPracticeCenter'
import { UniversityPracticeOrderApi } from '@/api/OrderCenter/UniversityPracticeCenter'
import request from '@/config/axios'

// Props
interface Props {
  visible: boolean
  isEdit?: boolean
  orderData?: UniversityPracticeOrder | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 表单引用
const formRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()
const attachmentUploadRef = ref<UploadInstance>()

// 加载状态
const loading = ref(false)
const uploadingAttachments = ref(false)

// 文件列表
const fileList = ref<UploadFile[]>([])
const attachmentList = ref<UploadFile[]>([])

// 表单数据
const form = ref<{
  businessOpportunity: string | number
  lead: string | number
  projectName: string
  universityName: string
  enterpriseName: string
  startDate: string
  endDate: string
  totalAmount: number
  managerId: number
  managerName: string
  // 下沉字段：商机和线索关联
  opportunityId: number
  opportunityName: string
  leadId: number
  leadName: string
  customerName: string
  businessType: string
  businessStage: string
  contractType: string
  paymentStatus: string
  collectionAmount: number
  collectionMethod: string
  collectionDate: string
  operatorName: string
  collectionRemark: string
  contractFile: File | null
  contractFileUrl: string
}>({
  businessOpportunity: '',
  lead: '',
  projectName: '',
  universityName: '',
  enterpriseName: '',
  startDate: '',
  endDate: '',
  totalAmount: 0,
  managerId: 0,
  managerName: '',
  // 下沉字段：商机和线索关联
  opportunityId: 0,
  opportunityName: '',
  leadId: 0,
  leadName: '',
  customerName: '',
  businessType: '',
  businessStage: '',

  contractType: 'electronic',
  paymentStatus: 'pending',
  collectionAmount: 0,
  collectionMethod: 'cash',
  collectionDate: '',
  operatorName: '',
  collectionRemark: '',
  contractFile: null as File | null,
  contractFileUrl: '' as string
})

// 抽屉标题
const drawerTitle = computed(() => {
  return props.isEdit ? '编辑高校实践订单' : '新建高校实践订单'
})

// 商机列表
const businessOpportunities = ref<BusinessOption[]>([])

// 线索列表
const leads = ref<LeadOption[]>([
  {
    id: 1,
    leadId: 'LX202406001',
    customerName: '张三',
    customerPhone: '138****1234',
    businessModule: '高校业务',
    leadSource: '电话咨询',
    leadStatus: '已转化'
  },
  {
    id: 2,
    leadId: 'LX202406002',
    customerName: '李四',
    customerPhone: '139****5678',
    businessModule: '高校业务',
    leadSource: '电话咨询',
    leadStatus: '已转化'
  },
  {
    id: 3,
    leadId: 'LX202406003',
    customerName: '王五',
    customerPhone: '137****9012',
    businessModule: '高校业务',
    leadSource: '网络咨询',
    leadStatus: '跟进中'
  }
])

// 选中的商机
const selectedOpportunity = computed(() => {
  if (!form.value.businessOpportunity || form.value.businessOpportunity === '') return null
  return businessOpportunities.value.find((item) => item.id === form.value.businessOpportunity)
})

// 选中的线索
const selectedLead = computed(() => {
  if (!form.value.lead || form.value.lead === '') return null
  return leads.value.find((item) => item.id === form.value.lead)
})

// 表单校验规则
const rules: FormRules = {
  // 合同附件验证规则（可选）
  contractFileUrl: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value && !/^https?:\/\//.test(value)) {
          callback(new Error('合同附件URL格式不正确'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '项目名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  universityName: [{ required: true, message: '请输入合作高校', trigger: 'blur' }],
  enterpriseName: [{ required: true, message: '请输入合作企业', trigger: 'blur' }],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value && form.value.endDate && new Date(value) >= new Date(form.value.endDate)) {
          callback(new Error('开始日期不能晚于或等于结束日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value && form.value.startDate && new Date(value) <= new Date(form.value.startDate)) {
          callback(new Error('结束日期不能早于或等于开始日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  totalAmount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '订单金额必须大于0', trigger: 'blur' }
  ],
  managerName: [{ required: true, message: '请输入项目负责人', trigger: 'blur' }],
  collectionAmount: [
    { required: true, message: '请输入收款金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '收款金额必须大于0', trigger: 'blur' }
  ],
  collectionMethod: [{ required: true, message: '请选择收款方式', trigger: 'change' }],
  collectionDate: [{ required: true, message: '请选择收款日期', trigger: 'change' }],
  operatorName: [{ required: true, message: '请输入操作人', trigger: 'blur' }]
}

// 获取商机状态类型
const getOpportunityStatusType = (status: string) => {
  const statusMap: Record<string, 'warning' | 'success' | 'info'> = {
    跟进中: 'warning',
    已确认: 'success',
    已关闭: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取线索状态类型
const getLeadStatusType = (status: string) => {
  const statusMap: Record<string, 'warning' | 'success' | 'info'> = {
    跟进中: 'warning',
    已转化: 'success',
    已失效: 'info'
  }
  return statusMap[status] || 'info'
}

// 支付状态中英文映射
const paymentStatusMap = {
  pending: '待支付',
  paid: '已支付',
  refunded: '已退款',
  cancelled: '已取消'
}

// 支付状态中文转英文
const getPaymentStatusValue = (chineseStatus: string) => {
  for (const [key, value] of Object.entries(paymentStatusMap)) {
    if (value === chineseStatus) return key
  }
  return 'pending'
}

// 支付状态英文转中文
const getPaymentStatusLabel = (englishStatus: string) => {
  return paymentStatusMap[englishStatus as keyof typeof paymentStatusMap] || '待支付'
}

// 商机选择变化
const handleBusinessOpportunityChange = (value: number) => {
  if (value && selectedOpportunity.value) {
    // 自动填充项目信息
    form.value.projectName = selectedOpportunity.value.name
    form.value.totalAmount = selectedOpportunity.value.totalPrice

    // 自动填充商机相关字段
    form.value.opportunityId = selectedOpportunity.value.id
    form.value.opportunityName = selectedOpportunity.value.name
    form.value.customerName = selectedOpportunity.value.customerName
    form.value.businessType = selectedOpportunity.value.businessType
    form.value.businessStage = selectedOpportunity.value.businessStage

    console.log('商机选择变化，自动填充字段:', {
      opportunityId: form.value.opportunityId,
      opportunityName: form.value.opportunityName,
      customerName: form.value.customerName
    })
  } else if (!value) {
    // 清空商机相关字段
    form.value.businessOpportunity = ''
    form.value.opportunityId = 0
    form.value.opportunityName = ''
    form.value.customerName = ''
    form.value.businessType = ''
    form.value.businessStage = ''

    console.log('清空商机关联，重置相关字段')
  }
}

// 线索选择变化
const handleLeadChange = (value: number) => {
  if (value && selectedLead.value) {
    // 自动填充线索相关字段
    form.value.leadId = selectedLead.value.id
    form.value.leadName = selectedLead.value.customerName

    // 可以根据线索信息自动填充客户相关字段
    form.value.managerName = selectedLead.value.customerName || ''

    console.log('线索选择变化，自动填充字段:', {
      leadId: form.value.leadId,
      leadName: form.value.leadName,
      managerName: form.value.managerName
    })
  } else if (!value) {
    // 清空线索相关字段
    form.value.lead = ''
    form.value.leadId = 0
    form.value.leadName = ''
    form.value.managerName = ''

    console.log('清空线索关联，重置相关字段')
  }
}

// 获取下拉数据
const fetchDropdownData = async () => {
  console.log('fetchDropdownData 函数被调用')
  try {
    loading.value = true
    console.log('开始获取下拉数据...')

    // 尝试调用API获取数据
    try {
      console.log('开始调用 UniversityPracticeOrderApi.getDropdownData...')
      const result = await UniversityPracticeOrderApi.getDropdownData({
        orderType: 'practice',
        businessLine: '高校实践'
      })

      console.log('API返回结果:', result)
      console.log('商机选项:', result.businessOptions)
      console.log('线索选项:', result.leadOptions)

      // 如果API返回了商机数据，则使用API数据
      if (Array.isArray(result.businessOptions) && result.businessOptions.length > 0) {
        businessOpportunities.value = result.businessOptions
        console.log('使用API返回的商机数据，数量:', result.businessOptions.length)
        console.log('商机数据详情:', result.businessOptions)
      } else {
        businessOpportunities.value = []
        console.log('API没有返回商机数据，商机列表为空')
        console.log('result.businessOptions:', result.businessOptions)
        console.log('result.businessOptions类型:', typeof result.businessOptions)
        console.log('result.businessOptions是否为数组:', Array.isArray(result.businessOptions))
      }

      // 如果API返回了线索数据，则使用API数据；否则保持静态数据
      if (Array.isArray(result.leadOptions) && result.leadOptions.length > 0) {
        leads.value = result.leadOptions
        console.log('使用API返回的线索数据')
      } else {
        console.log('API没有返回线索数据，保持静态数据')
      }
    } catch (apiError) {
      console.warn('API调用失败，保持现有数据:', apiError)
      // 商机列表保持为空
      businessOpportunities.value = []
      // 线索列表保持现有静态数据
    }

    console.log('最终商机列表:', businessOpportunities.value)
    console.log('最终线索列表:', leads.value)
  } catch (error) {
    console.error('获取下拉数据失败:', error)
    ElMessage.error('获取下拉数据失败')
  } finally {
    loading.value = false
  }
}

// 监听抽屉显示状态，每次打开时获取下拉数据
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      console.log('抽屉打开，获取下拉数据')
      fetchDropdownData()
    }
  },
  { immediate: true }
)

// 文件选择变化
const handleFileChange = (file: UploadFile) => {
  // 检查文件大小（10MB）
  const isLt10M = file.size! / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }

  // 检查文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/png'
  ]
  if (!allowedTypes.includes(file.raw?.type || '')) {
    ElMessage.error('只支持 PDF、Word、JPG、PNG 格式的文件!')
    return false
  }

  form.value.contractFile = file.raw || null
  return true
}

// 文件移除
const handleFileRemove = () => {
  form.value.contractFile = null
}

// 日期选择器禁用函数
const disabledStartDate = (time: Date) => {
  if (form.value.endDate) {
    return time.getTime() > new Date(form.value.endDate).getTime()
  }
  return false
}

const disabledEndDate = (time: Date) => {
  if (form.value.startDate) {
    return time.getTime() < new Date(form.value.startDate).getTime()
  }
  return false
}

// 日期选择器变化
const handleStartDateChange = (date: Date) => {
  if (date) {
    form.value.startDate = date.toISOString().slice(0, 10)
  } else {
    form.value.startDate = ''
  }
}

const handleEndDateChange = (date: Date) => {
  if (date) {
    form.value.endDate = date.toISOString().slice(0, 10)
  } else {
    form.value.endDate = ''
  }
}

// 监听支付状态变化
watch(
  () => form.value.paymentStatus,
  (newStatus) => {
    if (newStatus === 'paid') {
      // 当支付状态变为"已支付"时，设置默认值
      if (!form.value.collectionAmount) {
        form.value.collectionAmount = form.value.totalAmount
      }
      if (!form.value.collectionDate) {
        form.value.collectionDate = new Date().toISOString().slice(0, 10)
      }
      if (!form.value.operatorName) {
        form.value.operatorName = form.value.managerName
      }
    } else {
      // 当支付状态变为其他状态时，清空支付信息
      form.value.collectionAmount = 0
      form.value.collectionMethod = 'cash'
      form.value.collectionDate = ''
      form.value.operatorName = ''
      form.value.collectionRemark = ''
    }
  }
)

// 监听表单数据变化，用于调试
watch(
  () => form.value.startDate,
  (newVal) => {
    console.log('开始日期变化:', newVal)
  }
)

watch(
  () => form.value.endDate,
  (newVal) => {
    console.log('结束日期变化:', newVal)
  }
)

// 监听弹窗显示状态，重置表单
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.isEdit && props.orderData) {
        // 编辑模式，填充现有数据
        initEditForm()
      } else {
        // 新增模式，重置表单
        resetForm()
      }
    }
  },
  { immediate: true }
)

// 监听编辑数据变化，实时更新表单
watch(
  () => props.orderData,
  (newVal) => {
    if (newVal && props.isEdit && props.visible) {
      // 如果订单数据变化，重新初始化表单
      console.log('检测到编辑数据变化，重新初始化表单:', newVal)
      initEditForm()
    }
  },
  { deep: true, immediate: true }
)

// 监听编辑数据变化
watch(
  () => props.orderData,
  (newVal) => {
    if (newVal && props.isEdit && props.visible) {
      initEditForm()
    }
  }
)

// 初始化编辑表单数据
const initEditForm = () => {
  if (props.orderData) {
    console.log('初始化编辑表单，订单数据:', props.orderData) // 调试日志

    // 确保日期格式正确
    const startDate = props.orderData.startDate
      ? formatDateForPicker(props.orderData.startDate)
      : ''
    const endDate = props.orderData.endDate ? formatDateForPicker(props.orderData.endDate) : ''

    console.log('格式化后的日期:', { startDate, endDate }) // 调试日志

    // 设置表单数据，确保所有字段都有值
    form.value = {
      businessOpportunity: Number(props.orderData.opportunityId) || '',
      lead: Number(props.orderData.leadId) || '',
      leadId: props.orderData.leadId || 0,
      projectName: props.orderData.projectName || '',
      universityName: props.orderData.universityName || '',
      enterpriseName: props.orderData.enterpriseName || '',
      startDate: startDate,
      endDate: endDate,
      totalAmount: props.orderData.totalAmount || 0,
      managerId: props.orderData.managerId || 0,
      managerName: props.orderData.managerName || '',
      // 下沉字段：商机和线索关联
      opportunityId: props.orderData.opportunityId || 0,
      opportunityName: props.orderData.opportunityName || '',
      leadName: props.orderData.leadName || '',
      customerName: props.orderData.universityName || '', // 使用universityName作为客户名称
      businessType: '高校实践', // 固定值
      businessStage: '跟进中', // 固定值

      contractType: props.orderData.contractType || 'electronic',
      paymentStatus: props.orderData.paymentStatus || 'pending',
      collectionAmount: props.orderData.collectionAmount || 0,
      collectionMethod: props.orderData.collectionMethod || 'cash',
      collectionDate: props.orderData.collectionDate
        ? formatDateForPicker(props.orderData.collectionDate)
        : '',
      operatorName: props.orderData.operatorName || '',
      collectionRemark: props.orderData.collectionRemark || '',
      contractFile: null,
      contractFileUrl: props.orderData.contractFileUrl || ''
    }

    console.log('表单数据已填充完成:', form.value) // 调试日志

    // 清空文件列表
    fileList.value = []

    // 清除表单验证
    formRef.value?.clearValidate()

    // 如果有合同文件URL，处理合同附件的显示
    if (props.orderData.contractFileUrl) {
      console.log('合同文件URL:', props.orderData.contractFileUrl)
      // 将合同文件URL添加到附件列表中显示
      const contractFile: UploadFile = {
        uid: Date.now(),
        name: '合同附件',
        status: 'success',
        url: props.orderData.contractFileUrl,
        size: 0
      }
      attachmentList.value = [contractFile]
    }

    // 如果有收款信息，设置默认值
    if (props.orderData.paymentStatus === 'paid') {
      console.log('检测到已支付状态，设置收款信息默认值')
      if (!form.value.collectionAmount && props.orderData.totalAmount) {
        form.value.collectionAmount = props.orderData.totalAmount
      }
      if (!form.value.collectionDate) {
        form.value.collectionDate = new Date().toISOString().split('T')[0]
      }
      if (!form.value.operatorName && props.orderData.managerName) {
        form.value.operatorName = props.orderData.managerName
      }
    }
  }
}

// 格式化日期为日期选择器可用的格式
const formatDateForPicker = (dateString: string) => {
  if (!dateString) return ''

  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) {
      console.warn('无效的日期格式:', dateString)
      return ''
    }

    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')

    return `${year}-${month}-${day}`
  } catch (error) {
    console.error('日期格式化错误:', error)
    return ''
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    businessOpportunity: '',
    lead: '',
    leadId: 0,
    projectName: '',
    universityName: '',
    enterpriseName: '',
    startDate: '',
    endDate: '',
    totalAmount: 0,
    managerId: 0,
    managerName: '',
    // 下沉字段：商机和线索关联
    opportunityId: 0,
    opportunityName: '',
    leadName: '',
    customerName: '',
    businessType: '高校实践',
    businessStage: '跟进中',

    contractType: 'electronic',
    paymentStatus: 'pending',
    collectionAmount: 0,
    collectionMethod: 'cash',
    collectionDate: '',
    operatorName: '',
    collectionRemark: '',
    contractFile: null,
    contractFileUrl: ''
  }
  fileList.value = []
  attachmentList.value = []
  formRef.value?.clearValidate()
}

// 附件管理相关方法
// 处理附件选择变化 - 参考ManagementCourseForOnline.vue的实现方式
const handleAttachmentChange = async (file: UploadFile, fileList: UploadFile[]) => {
  console.log('附件选择变化:', file, fileList)
  attachmentList.value = fileList

  // 如果选择了新文件，自动上传
  if (file && file.raw && file.status === 'ready') {
    try {
      await uploadSingleAttachment(file)
    } catch (error) {
      console.error('文件上传失败:', error)
      // 从列表中移除上传失败的文件
      const index = attachmentList.value.findIndex((f) => f.uid === file.uid)
      if (index > -1) {
        attachmentList.value.splice(index, 1)
      }
    }
  }
}

// 处理附件移除
const handleAttachmentRemove = (file: UploadFile, fileList: UploadFile[]) => {
  console.log('附件移除:', file, fileList)
  attachmentList.value = fileList

  // 如果移除的是合同附件，清空合同文件URL
  if (file && file.url === form.value.contractFileUrl) {
    form.value.contractFileUrl = ''
    console.log('已清空合同文件URL')
  }
}

// 移除指定索引的附件
const removeAttachment = (index: number) => {
  const removedFile = attachmentList.value[index]
  attachmentList.value.splice(index, 1)

  // 如果移除的是合同附件，清空合同文件URL
  if (removedFile && removedFile.url === form.value.contractFileUrl) {
    form.value.contractFileUrl = ''
    console.log('已清空合同文件URL')
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 上传单个附件到服务器 - 参考handleCoverChange的实现方式
const uploadSingleAttachment = async (file: UploadFile) => {
  if (!file.raw) {
    throw new Error(`文件 ${file.name} 数据不完整`)
  }

  const formData = new FormData()
  formData.append('file', file.raw)
  formData.append('directory', 'university-practice-order')

  // 使用request.postOriginal直接调用文件上传接口，参考ManagementCourseForOnline.vue
  const res = await request.postOriginal({
    url: '/infra/file/upload',
    data: formData,
    headers: { 'Content-Type': 'multipart/form-data' }
  })

  // 添加详细的调试日志
  console.log('文件上传响应:', res)
  console.log('响应数据结构:', {
    hasRes: !!res,
    hasResData: !!res.data,
    code: res.data?.code,
    hasData: !!res.data?.data,
    dataType: typeof res.data?.data,
    dataValue: res.data?.data,
    msg: res.data?.msg
  })

  // 根据接口返回格式：{ "code": 0, "data": "https://...", "msg": "" }
  // 简化条件：只要res.data有值且fileUrl满足URL格式即可回写
  const fileUrl = res.data

  console.log('解析后的数据:', {
    hasResData: !!res.data,
    fileUrl,
    originalRes: res
  })

  // 只要res.data有值且fileUrl满足URL格式验证就可以回写
  if (res.data && fileUrl && typeof fileUrl === 'string' && /^https?:\/\//.test(fileUrl)) {
    // 条件满足，进行附件回写
    console.log('条件满足，开始附件回写')

    // 上传成功，更新文件状态和URL
    file.url = fileUrl
    file.status = 'success'

    // 保存合同文件URL到表单数据中
    form.value.contractFileUrl = fileUrl

    ElMessage.success(`附件 ${file.name} 上传成功，合同文件URL已保存`)
    console.log('合同文件URL已保存:', fileUrl)
    return { success: true, fileUrl, fileName: file.name }
  } else {
    // 条件不满足，不上传附件
    console.error('附件上传条件不满足:', {
      hasResData: !!res.data,
      hasFileUrl: !!fileUrl,
      fileUrlType: typeof fileUrl,
      isValidUrl: fileUrl && typeof fileUrl === 'string' ? /^https?:\/\//.test(fileUrl) : false
    })

    file.status = 'fail'

    // 根据具体失败原因给出错误信息
    if (!res.data) {
      throw new Error('接口未返回数据')
    } else if (!fileUrl) {
      throw new Error('接口未返回文件URL')
    } else if (typeof fileUrl !== 'string') {
      throw new Error(`文件URL类型错误: ${typeof fileUrl}`)
    } else if (!/^https?:\/\//.test(fileUrl)) {
      throw new Error(`文件URL格式无效: ${fileUrl}`)
    } else {
      throw new Error(`上传失败: data=${fileUrl}`)
    }
  }
}

// 批量上传附件到服务器（保留原有方法以兼容）
const uploadAttachments = async () => {
  if (attachmentList.value.length === 0) {
    ElMessage.warning('请先选择要上传的附件')
    return
  }

  uploadingAttachments.value = true

  try {
    const uploadPromises = attachmentList.value
      .filter((file) => file.status !== 'success') // 只上传未成功的文件
      .map(async (file) => {
        try {
          return await uploadSingleAttachment(file)
        } catch (error) {
          console.error(`文件 ${file.name} 上传失败:`, error)
          throw error
        }
      })

    const results = await Promise.all(uploadPromises)
    console.log('所有附件上传完成:', results)

    ElMessage.success('所有附件上传完成')
  } catch (error) {
    console.error('附件上传失败:', error)
    ElMessage.error(`附件上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    uploadingAttachments.value = false
  }
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 调用商机线索下沉接口
const callSinkInterfaces = async (orderId: number, orderNo: string) => {
  try {
    // 如果有选择商机，调用商机下沉接口
    if (
      form.value.businessOpportunity &&
      form.value.businessOpportunity !== '' &&
      selectedOpportunity.value
    ) {
      console.log('调用商机下沉接口:', {
        opportunityId: selectedOpportunity.value.id,
        orderId,
        orderNo
      })

      await UniversityPracticeOrderApi.sinkBusinessOpportunity({
        opportunityId: selectedOpportunity.value.id,
        orderId,
        orderNo,
        status: '已转化', // 商机状态更新为已转化
        remark: `已转化为高校实践订单: ${orderNo}`
      })
      console.log('商机下沉接口调用成功')
    } else {
      console.log('未选择商机，跳过商机下沉接口调用')
    }

    // 如果有选择线索，调用线索下沉接口
    if (form.value.lead && form.value.lead !== '' && selectedLead.value) {
      console.log('调用线索下沉接口:', {
        leadId: selectedLead.value.id,
        orderId,
        orderNo
      })

      await UniversityPracticeOrderApi.sinkLead({
        leadId: selectedLead.value.id,
        orderId,
        orderNo,
        status: '已转化', // 线索状态更新为已转化
        remark: `已转化为高校实践订单: ${orderNo}`
      })
      console.log('线索下沉接口调用成功')
    } else {
      console.log('未选择线索，跳过线索下沉接口调用')
    }
  } catch (error) {
    console.error('下沉接口调用失败:', error)
    // 下沉接口失败不影响订单保存，只记录日志
    ElMessage.warning('订单保存成功，但商机线索状态更新失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    let orderId: number | undefined
    let orderNo: string | undefined

    if (props.isEdit && props.orderData) {
      // 编辑模式
      const updateParams: UpdateOrderParams = {
        id: props.orderData.id!,
        orderNo: props.orderData.orderNo!,
        // 下沉字段：商机和线索关联
        opportunityId: form.value.opportunityId || undefined,
        leadId: form.value.leadId || undefined,
        opportunityName: form.value.opportunityName || undefined,
        leadName: form.value.leadName || undefined,
        projectName: form.value.projectName,
        universityName: form.value.universityName,
        enterpriseName: form.value.enterpriseName,
        startDate: form.value.startDate,
        endDate: form.value.endDate,
        totalAmount: form.value.totalAmount,
        managerId: form.value.managerId,
        managerName: form.value.managerName,
        contractType: form.value.contractType,
        contractFileUrl: form.value.contractFileUrl,
        paymentStatus: form.value.paymentStatus
      }

      // 只有当支付状态为"已支付"时才添加支付信息
      if (form.value.paymentStatus === 'paid') {
        updateParams.collectionAmount = form.value.collectionAmount
        updateParams.collectionMethod = form.value.collectionMethod
        updateParams.collectionDate = form.value.collectionDate
        updateParams.operatorName = form.value.operatorName
        updateParams.collectionRemark = form.value.collectionRemark
      }

      console.log('更新参数:', updateParams) // 调试日志
      await UniversityPracticeOrderApi.updateOrder(updateParams)
      orderId = props.orderData.id
      orderNo = props.orderData.orderNo
      ElMessage.success('编辑成功')
    } else {
      // 新增模式
      const addParams: AddOrderParams = {
        // 下沉字段：商机和线索关联
        opportunityId: form.value.opportunityId || undefined,
        leadId: form.value.leadId || undefined,
        opportunityName: form.value.opportunityName || undefined,
        leadName: form.value.leadName || undefined,
        projectName: form.value.projectName,
        universityName: form.value.universityName,
        enterpriseName: form.value.enterpriseName,
        startDate: form.value.startDate,
        endDate: form.value.endDate,
        totalAmount: form.value.totalAmount,
        managerId: 1,
        managerName: form.value.managerName,
        contractType: form.value.contractType,
        contractFileUrl: form.value.contractFileUrl,
        paymentStatus: form.value.paymentStatus
      }

      // 只有当支付状态为"已支付"时才添加支付信息
      if (form.value.paymentStatus === 'paid') {
        addParams.collectionAmount = form.value.collectionAmount
        addParams.collectionMethod = form.value.collectionMethod
        addParams.collectionDate = form.value.collectionDate
        addParams.operatorName = form.value.operatorName
        addParams.collectionRemark = form.value.collectionRemark
      }

      const result = await UniversityPracticeOrderApi.createOrder(addParams)
      orderId = result.id
      orderNo = result.orderNo
      ElMessage.success('创建成功')
    }

    // 合同文件信息已经在选择文件时自动上传，这里只需要确保合同文件URL被正确保存
    if (form.value.contractFileUrl) {
      console.log('订单包含合同文件:', form.value.contractFileUrl)
      ElMessage.success(`订单保存成功，包含合同文件: ${form.value.contractFileUrl}`)
    }

    // 调用商机线索下沉接口
    if (orderId && orderNo) {
      await callSinkInterfaces(orderId, orderNo)
    }

    emit('success', form.value)
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.order-form {
  padding: 20px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.form-section {
  margin-bottom: 30px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    display: flex;
    align-items: center;
    gap: 8px;

    .section-icon {
      font-size: 18px;
      color: #409eff;
    }
  }

  &.payment-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;

    .section-title {
      border-bottom-color: #28a745;
      color: #28a745;

      .section-icon {
        color: #28a745;
      }
    }

    .el-form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.info-box {
  margin-top: 12px;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;

  .info-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .info-icon {
      font-size: 16px;
      color: #409eff;

      &.lead-icon {
        color: #722ed1;
      }
    }

    .info-title {
      font-weight: 500;
      color: #303133;
    }
  }

  .info-content {
    .info-item {
      display: flex;
      margin-bottom: 12px;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        margin-bottom: 0;
        border-bottom: none;
      }

      .info-label {
        font-weight: 600;
        color: #606266;
        min-width: 90px;
        flex-shrink: 0;
        font-size: 14px;
      }

      .info-value {
        color: #303133;
        flex: 1;
        font-size: 14px;
        line-height: 1.5;

        &.amount {
          color: #67c23a;
          font-weight: bold;
        }
      }
    }
  }

  &.business-opportunity-info {
    background: #f0f9ff;
    border-color: #409eff;

    .info-content {
      .info-item {
        .info-label {
          min-width: 90px;
          color: #409eff;
          font-weight: 600;
        }

        .info-value {
          color: #303133;
          font-weight: 500;

          &.amount {
            color: #67c23a;
            font-weight: bold;
            font-size: 16px;
          }
        }
      }
    }
  }

  &.lead-info {
    background: #f9f0ff;
    border-color: #722ed1;

    .info-content {
      .info-item {
        .info-label {
          min-width: 90px;
          color: #722ed1;
          font-weight: 600;
        }

        .info-value {
          color: #303133;
          font-weight: 500;

          &.lead-id {
            color: #722ed1;
            font-weight: bold;
            font-family: 'Courier New', monospace;
          }

          &.phone {
            color: #409eff;
            font-weight: 600;
          }
        }
      }
    }
  }
}

.form-tip {
  font-size: 13px;
  color: #606266;
  margin-top: 8px;
  line-height: 1.5;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  font-weight: 500;
  width: 100%;
  box-sizing: border-box;

  &::before {
    content: '💡 ';
    margin-right: 4px;
  }
}

.contract-upload {
  width: 100%;

  .upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }
}

.attachment-upload {
  width: 100%;

  .upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }
}

.attachment-list {
  margin-top: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;

  .attachment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;

    .attachment-title {
      font-weight: 500;
      color: #303133;
    }

    .contract-url-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .url-text {
        font-size: 12px;
        color: #606266;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .attachment-items {
    .attachment-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .attachment-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .file-icon {
          color: #409eff;
          font-size: 16px;
        }

        .file-name {
          color: #303133;
          font-weight: 500;
          flex: 1;
        }

        .file-size {
          color: #909399;
          font-size: 12px;
        }
      }

      .attachment-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-drawer__header) {
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 0;
}

:deep(.el-drawer__body) {
  padding: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;

  // 必填字段标签样式
  &::after {
    content: ' *';
    color: #f56c6c;
    margin-left: 4px;
  }
}

// 非必填字段标签样式
:deep(.el-form-item:not(.is-required) .el-form-item__label::after) {
  display: none;
}

:deep(.el-form-item.is-required .el-form-item__label::before) {
  color: #f56c6c;
}
</style>
