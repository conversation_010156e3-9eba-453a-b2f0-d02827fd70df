## 商机分页列表

**接口地址**:`/publicbiz/business/list`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称      | 参数说明               | 请求类型 | 是否必须 | 数据类型       | schema |
| ------------- | ---------------------- | -------- | -------- | -------------- | ------ |
| pageNo        | 页码，从 1 开始        | query    | true     | string         |        |
| pageSize      | 每页条数，最大值为 100 | query    | true     | string         |        |
| id            |                        | query    | false    | integer(int64) |        |
| tenantId      |                        | query    | false    | integer(int64) |        |
| name          |                        | query    | false    | string         |        |
| customerId    |                        | query    | false    | integer(int64) |        |
| customerName  |                        | query    | false    | string         |        |
| businessType  |                        | query    | false    | string         |        |
| totalPrice    |                        | query    | false    | number         |        |
| businessStage |                        | query    | false    | string         |        |
| ownerUserId   |                        | query    | false    | integer(int64) |        |
| ownerUserName |                        | query    | false    | string         |        |
| creator       |                        | query    | false    | string         |        |
| updater       |                        | query    | false    | string         |        |
| deleted       |                        | query    | false    | boolean        |        |

**响应状态**:

| 状态码 | 说明 | schema                               |
| ------ | ---- | ------------------------------------ |
| 200    | OK   | CommonResultPageResultBusinessRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultBusinessRespVO | PageResultBusinessRespVO |
| &emsp;&emsp;list | 数据 | array | BusinessRespVO |
| &emsp;&emsp;&emsp;&emsp;id |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;tenantId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;name |  | string |  |
| &emsp;&emsp;&emsp;&emsp;customerId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;customerName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;businessType |  | string |  |
| &emsp;&emsp;&emsp;&emsp;totalPrice |  | number |  |
| &emsp;&emsp;&emsp;&emsp;businessStage |  | string |  |
| &emsp;&emsp;&emsp;&emsp;ownerUserId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;ownerUserName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;creator |  | string |  |
| &emsp;&emsp;&emsp;&emsp;createTime |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updater |  | string |  |
| &emsp;&emsp;&emsp;&emsp;updateTime |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;deleted |  | boolean |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 0,
				"tenantId": 0,
				"name": "",
				"customerId": 0,
				"customerName": "",
				"businessType": "",
				"totalPrice": 0,
				"businessStage": "",
				"ownerUserId": 0,
				"ownerUserName": "",
				"creator": "",
				"createTime": "",
				"updater": "",
				"updateTime": "",
				"deleted": true
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 新增商机

**接口地址**:`/publicbiz/business/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0,
  "tenantId": 0,
  "name": "",
  "customerId": 0,
  "customerName": "",
  "businessType": "",
  "totalPrice": 0,
  "businessStage": "",
  "ownerUserId": 0,
  "ownerUserName": "",
  "creator": "",
  "createTime": "",
  "updater": "",
  "updateTime": "",
  "deleted": true
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| businessSaveReqVO | 商机中心 - 商机新增/更新 Request VO | body | true | BusinessSaveReqVO | BusinessSaveReqVO |
| &emsp;&emsp;id |  |  | false | integer(int64) |  |
| &emsp;&emsp;tenantId |  |  | false | integer(int64) |  |
| &emsp;&emsp;name |  |  | true | string |  |
| &emsp;&emsp;customerId |  |  | true | integer(int64) |  |
| &emsp;&emsp;customerName |  |  | true | string |  |
| &emsp;&emsp;businessType |  |  | false | string |  |
| &emsp;&emsp;totalPrice |  |  | false | number |  |
| &emsp;&emsp;businessStage |  |  | false | string |  |
| &emsp;&emsp;ownerUserId |  |  | false | integer(int64) |  |
| &emsp;&emsp;ownerUserName |  |  | false | string |  |
| &emsp;&emsp;creator |  |  | false | string |  |
| &emsp;&emsp;createTime |  |  | false | string(date-time) |  |
| &emsp;&emsp;updater |  |  | false | string |  |
| &emsp;&emsp;updateTime |  |  | false | string(date-time) |  |
| &emsp;&emsp;deleted |  |  | false | boolean |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```

## 编辑商机

**接口地址**:`/publicbiz/business/update`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0,
  "tenantId": 0,
  "name": "",
  "customerId": 0,
  "customerName": "",
  "businessType": "",
  "totalPrice": 0,
  "businessStage": "",
  "ownerUserId": 0,
  "ownerUserName": "",
  "creator": "",
  "createTime": "",
  "updater": "",
  "updateTime": "",
  "deleted": true
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| businessSaveReqVO | 商机中心 - 商机新增/更新 Request VO | body | true | BusinessSaveReqVO | BusinessSaveReqVO |
| &emsp;&emsp;id |  |  | false | integer(int64) |  |
| &emsp;&emsp;tenantId |  |  | false | integer(int64) |  |
| &emsp;&emsp;name |  |  | true | string |  |
| &emsp;&emsp;customerId |  |  | true | integer(int64) |  |
| &emsp;&emsp;customerName |  |  | true | string |  |
| &emsp;&emsp;businessType |  |  | false | string |  |
| &emsp;&emsp;totalPrice |  |  | false | number |  |
| &emsp;&emsp;businessStage |  |  | false | string |  |
| &emsp;&emsp;ownerUserId |  |  | false | integer(int64) |  |
| &emsp;&emsp;ownerUserName |  |  | false | string |  |
| &emsp;&emsp;creator |  |  | false | string |  |
| &emsp;&emsp;createTime |  |  | false | string(date-time) |  |
| &emsp;&emsp;updater |  |  | false | string |  |
| &emsp;&emsp;updateTime |  |  | false | string(date-time) |  |
| &emsp;&emsp;deleted |  |  | false | boolean |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 逻辑删除商机

**接口地址**:`/publicbiz/business/delete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 商机详情

**接口地址**:`/publicbiz/business/detail`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema                           |
| ------ | ---- | -------------------------------- |
| 200    | OK   | CommonResultBusinessDetailRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | BusinessDetailRespVO | BusinessDetailRespVO |
| &emsp;&emsp;business |  | BusinessRespVO | BusinessRespVO |
| &emsp;&emsp;&emsp;&emsp;id |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;tenantId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;name |  | string |  |
| &emsp;&emsp;&emsp;&emsp;customerId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;customerName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;businessType |  | string |  |
| &emsp;&emsp;&emsp;&emsp;totalPrice |  | number |  |
| &emsp;&emsp;&emsp;&emsp;businessStage |  | string |  |
| &emsp;&emsp;&emsp;&emsp;ownerUserId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;ownerUserName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;creator |  | string |  |
| &emsp;&emsp;&emsp;&emsp;createTime |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updater |  | string |  |
| &emsp;&emsp;&emsp;&emsp;updateTime |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;deleted |  | boolean |  |
| &emsp;&emsp;followups | 商机跟进记录列表 | array | BusinessFollowupRespVO |
| &emsp;&emsp;&emsp;&emsp;id |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;tenantId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;businessId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;content |  | string |  |
| &emsp;&emsp;&emsp;&emsp;followTime |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;followUserId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;followUserName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;creator |  | string |  |
| &emsp;&emsp;&emsp;&emsp;createTime |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updater |  | string |  |
| &emsp;&emsp;&emsp;&emsp;updateTime |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;deleted |  | boolean |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"business": {
			"id": 0,
			"tenantId": 0,
			"name": "",
			"customerId": 0,
			"customerName": "",
			"businessType": "",
			"totalPrice": 0,
			"businessStage": "",
			"ownerUserId": 0,
			"ownerUserName": "",
			"creator": "",
			"createTime": "",
			"updater": "",
			"updateTime": "",
			"deleted": true
		},
		"followups": [
			{
				"id": 0,
				"tenantId": 0,
				"businessId": 0,
				"content": "",
				"followTime": "",
				"followUserId": 0,
				"followUserName": "",
				"creator": "",
				"createTime": "",
				"updater": "",
				"updateTime": "",
				"deleted": true
			}
		]
	},
	"msg": ""
}
```

## 新增商机跟进

**接口地址**:`/publicbiz/business/followup/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0,
  "tenantId": 0,
  "businessId": 0,
  "content": "",
  "followTime": "",
  "followUserId": 0,
  "followUserName": "",
  "creator": "",
  "createTime": "",
  "updater": "",
  "updateTime": "",
  "deleted": true
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| businessFollowupSaveReqVO | 商机中心 - 商机跟进新增/更新 Request VO | body | true | BusinessFollowupSaveReqVO | BusinessFollowupSaveReqVO |
| &emsp;&emsp;id |  |  | false | integer(int64) |  |
| &emsp;&emsp;tenantId |  |  | false | integer(int64) |  |
| &emsp;&emsp;businessId |  |  | true | integer(int64) |  |
| &emsp;&emsp;content |  |  | true | string |  |
| &emsp;&emsp;followTime |  |  | false | string(date-time) |  |
| &emsp;&emsp;followUserId |  |  | false | integer(int64) |  |
| &emsp;&emsp;followUserName |  |  | false | string |  |
| &emsp;&emsp;creator |  |  | false | string |  |
| &emsp;&emsp;createTime |  |  | false | string(date-time) |  |
| &emsp;&emsp;updater |  |  | false | string |  |
| &emsp;&emsp;updateTime |  |  | false | string(date-time) |  |
| &emsp;&emsp;deleted |  |  | false | boolean |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```
