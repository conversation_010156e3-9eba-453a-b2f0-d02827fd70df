<!--
  页面名称：收款回写页面
  功能描述：通过订单号查询高校实践订单，支持收款信息的回写和更新
-->
<template>
  <div class="collection-writeback-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>收款回写</h2>
      <p class="page-description">通过订单号查询高校实践订单，支持收款信息的回写和更新</p>
    </div>

    <!-- 查询区域 -->
    <div class="query-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>订单号查询</span>
          </div>
        </template>

        <el-form :inline="true" :model="queryForm" @submit.prevent="handleQuery">
          <el-form-item label="订单号" prop="orderNo">
            <el-input
              v-model="queryForm.orderNo"
              placeholder="请输入订单号"
              style="width: 200px"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" :loading="queryLoading">
              查询订单
            </el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 订单信息展示 -->
    <div v-if="orderData" class="order-info-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>订单信息</span>
            <el-tag :type="getOrderStatusType(orderData.orderStatus || '')" size="small">
              {{ getOrderStatusText(orderData.orderStatus || '') }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ orderData.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="项目名称">{{ orderData.projectName }}</el-descriptions-item>
          <el-descriptions-item label="合作高校">{{
            orderData.universityName
          }}</el-descriptions-item>
          <el-descriptions-item label="合作企业">{{
            orderData.enterpriseName
          }}</el-descriptions-item>
          <el-descriptions-item label="项目周期">
            {{ formatDate(orderData.startDate) }} - {{ formatDate(orderData.endDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="订单金额">
            <span class="amount">¥{{ formatAmount(orderData.totalAmount || 0) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="项目负责人">{{
            orderData.managerName
          }}</el-descriptions-item>
          <el-descriptions-item label="当前支付状态">
            <el-tag :type="getPaymentStatusType(orderData.paymentStatus || '')" size="small">
              {{ getPaymentStatusText(orderData.paymentStatus || '') }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- 收款信息回写 -->
    <div v-if="orderData" class="collection-writeback-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>收款信息回写</span>
          </div>
        </template>

        <el-form
          ref="collectionFormRef"
          :model="collectionForm"
          :rules="collectionRules"
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="收款状态" prop="paymentStatus">
                <el-select
                  v-model="collectionForm.paymentStatus"
                  placeholder="请选择收款状态"
                  style="width: 100%"
                >
                  <el-option label="待支付" value="pending" />
                  <el-option label="已支付" value="paid" />
                  <el-option label="已退款" value="refunded" />
                  <el-option label="已取消" value="cancelled" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款金额" prop="collectionAmount">
                <el-input-number
                  v-model="collectionForm.collectionAmount"
                  :min="0"
                  :max="orderData.totalAmount || 0"
                  :precision="2"
                  style="width: 100%"
                  placeholder="请输入收款金额"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="收款方式" prop="collectionMethod">
                <el-select
                  v-model="collectionForm.collectionMethod"
                  placeholder="请选择收款方式"
                  style="width: 100%"
                >
                  <el-option label="现金" value="cash" />
                  <el-option label="微信支付" value="wechat" />
                  <el-option label="支付宝" value="alipay" />
                  <el-option label="银行转账" value="bank_transfer" />
                  <el-option label="POS机刷卡" value="pos" />
                  <el-option label="其他" value="other" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款日期" prop="collectionDate">
                <el-date-picker
                  v-model="collectionForm.collectionDate"
                  type="date"
                  placeholder="请选择收款日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="操作人" prop="operatorName">
                <el-input
                  v-model="collectionForm.operatorName"
                  placeholder="请输入操作人姓名"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款备注" prop="collectionRemark">
                <el-input
                  v-model="collectionForm.collectionRemark"
                  placeholder="请输入收款备注（可选）"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              提交收款信息
            </el-button>
            <el-button @click="resetCollectionForm">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作结果 -->
    <div v-if="operationResult" class="operation-result-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>操作结果</span>
          </div>
        </template>

        <div class="result-content">
          <el-result
            :icon="operationResult.success ? 'success' : 'error'"
            :title="operationResult.title"
            :sub-title="operationResult.message"
          >
            <template #extra>
              <el-button type="primary" @click="handleQuery">继续查询</el-button>
              <el-button @click="resetAll">重置页面</el-button>
            </template>
          </el-result>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  UniversityPracticeOrderApi,
  type UniversityPracticeOrder,
  type UpdateCollectionParams
} from '@/api/OrderCenter/UniversityPracticeCenter'

// 响应式数据
/** 查询表单数据 */
const queryForm = ref({
  orderNo: '' as string
})

/** 收款表单数据 */
const collectionForm = ref({
  paymentStatus: 'pending',
  collectionAmount: 0,
  collectionMethod: 'cash',
  collectionDate: '',
  operatorName: '',
  collectionRemark: ''
})

/** 查询加载状态 */
const queryLoading = ref(false)

/** 提交加载状态 */
const submitLoading = ref(false)

/** 订单数据 */
const orderData = ref<UniversityPracticeOrder | null>(null)

/** 操作结果 */
const operationResult = ref<{
  success: boolean
  title: string
  message: string
} | null>(null)

// 表单引用
const collectionFormRef = ref<FormInstance>()

// 表单校验规则
const collectionRules: FormRules = {
  paymentStatus: [{ required: true, message: '请选择收款状态', trigger: 'change' }],
  collectionAmount: [
    { required: true, message: '请输入收款金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '收款金额必须大于等于0', trigger: 'blur' }
  ],
  collectionMethod: [{ required: true, message: '请选择收款方式', trigger: 'change' }],
  collectionDate: [{ required: true, message: '请选择收款日期', trigger: 'change' }],
  operatorName: [{ required: true, message: '请输入操作人姓名', trigger: 'blur' }]
}

// 计算属性
const canSubmit = computed(() => {
  return orderData.value && collectionForm.value.paymentStatus === 'paid'
})

// 方法
/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount.toLocaleString('zh-CN')
}

/** 格式化日期 */
const formatDate = (dateString?: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}

/** 获取订单状态类型 */
const getOrderStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'warning',
    approved: 'success',
    rejected: 'danger',
    pending_payment: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    approved: '已批准',
    rejected: '已拒绝',
    pending_payment: '待支付',
    in_progress: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 获取支付状态类型 */
const getPaymentStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    pending: 'warning',
    paid: 'success',
    refunded: 'info',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取支付状态文本 */
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    refunded: '已退款',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 查询订单 */
const handleQuery = async () => {
  if (!queryForm.value.orderNo) {
    ElMessage.warning('请输入订单号')
    return
  }

  queryLoading.value = true
  try {
    const result = await UniversityPracticeOrderApi.getOrderByOrderNo(queryForm.value.orderNo)
    orderData.value = result

    // 初始化收款表单
    initCollectionForm()

    // 清空操作结果
    operationResult.value = null

    ElMessage.success('查询成功')
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请检查订单号是否正确')
    orderData.value = null
  } finally {
    queryLoading.value = false
  }
}

/** 初始化收款表单 */
const initCollectionForm = () => {
  if (orderData.value) {
    collectionForm.value = {
      paymentStatus: orderData.value.paymentStatus || 'pending',
      collectionAmount: orderData.value.collectionAmount || orderData.value.totalAmount || 0,
      collectionMethod: orderData.value.collectionMethod || 'cash',
      collectionDate: orderData.value.collectionDate || new Date().toISOString().split('T')[0],
      operatorName: orderData.value.operatorName || orderData.value.managerName || '',
      collectionRemark: orderData.value.collectionRemark || ''
    }
  }
}

/** 提交收款信息 */
const handleSubmit = async () => {
  if (!collectionFormRef.value || !orderData.value) return

  try {
    await collectionFormRef.value.validate()

    if (
      collectionForm.value.paymentStatus === 'paid' &&
      collectionForm.value.collectionAmount <= 0
    ) {
      ElMessage.warning('收款金额必须大于0')
      return
    }

    submitLoading.value = true

    const updateParams: UpdateCollectionParams = {
      orderId: orderData.value.id!,
      orderNo: orderData.value.orderNo!,
      paymentStatus: collectionForm.value.paymentStatus,
      collectionAmount: collectionForm.value.collectionAmount,
      collectionMethod: collectionForm.value.collectionMethod,
      collectionDate: collectionForm.value.collectionDate,
      operatorName: collectionForm.value.operatorName,
      collectionRemark: collectionForm.value.collectionRemark
    }

    await UniversityPracticeOrderApi.updateCollection(updateParams)

    // 显示成功结果
    operationResult.value = {
      success: true,
      title: '收款信息更新成功',
      message: `订单 ${orderData.value.orderNo} 的收款信息已成功更新`
    }

    ElMessage.success('收款信息更新成功')
  } catch (error) {
    console.error('提交失败:', error)

    // 显示失败结果
    operationResult.value = {
      success: false,
      title: '收款信息更新失败',
      message: '请检查输入信息后重试'
    }

    ElMessage.error('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

/** 重置查询表单 */
const resetQuery = () => {
  queryForm.value.orderNo = ''
  orderData.value = null
  operationResult.value = null
}

/** 重置收款表单 */
const resetCollectionForm = () => {
  if (orderData.value) {
    initCollectionForm()
  }
}

/** 重置所有 */
const resetAll = () => {
  resetQuery()
  collectionForm.value = {
    paymentStatus: 'pending',
    collectionAmount: 0,
    collectionMethod: 'cash',
    collectionDate: '',
    operatorName: '',
    collectionRemark: ''
  }
  collectionFormRef.value?.clearValidate()
}
</script>

<style scoped lang="scss">
.collection-writeback-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 20px;
    text-align: center;

    h2 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 24px;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .query-section,
  .order-info-section,
  .collection-writeback-section,
  .operation-result-section {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
    }
  }

  .amount {
    color: #67c23a;
    font-weight: bold;
    font-size: 16px;
  }

  .result-content {
    padding: 20px 0;
  }
}
</style>
