<!--
  页面名称：人员变动标签页
  功能描述：展示家政服务订单的人员变动记录
-->
<template>
  <div class="personnel-change-tab">
    <div class="change-content">
      <el-table :data="personnelChanges" style="width: 100%" size="small">
        <el-table-column prop="changeType" label="变动类型" width="120" />
        <el-table-column prop="originalPersonnel" label="原人员" width="120" />
        <el-table-column prop="newPersonnel" label="新人员" width="120" />
        <el-table-column prop="changeTime" label="变动时间" width="150" />
        <el-table-column prop="changeReason" label="变动原因" min-width="200" />
        <el-table-column prop="operator" label="操作人" width="150" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { User } from '@element-plus/icons-vue'

interface Props {
  orderDetail: any
}

defineProps<Props>()

// 人员变动数据
const personnelChanges = ref([
  {
    changeType: '请假顶岗',
    originalPersonnel: '张阿姨',
    newPersonnel: '王阿姨',
    changeTime: '2024-06-10 08:30',
    changeReason: '张阿姨因家中有急事请假,安排王阿姨顶岗',
    operator: '运营专员-李小明'
  }
])
</script>

<style scoped lang="scss">
.personnel-change-tab {
  .change-content {
    .el-table {
      .el-table__header {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
