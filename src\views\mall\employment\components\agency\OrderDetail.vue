<!--
  页面名称：工单详情
  功能描述：展示工单详细信息，包括基本信息、关联方信息、投诉情况说明等
-->
<template>
  <div class="order-detail">
    <!-- 工单基本信息 -->
    <div class="section">
      <h3 class="section-title">工单基本信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">工单号</span>
          <span class="value">{{ orderDetail.taskNo }}</span>
        </div>
        <div class="info-item">
          <span class="label">工单类型</span>
          <span class="value">{{ orderDetail.type }}</span>
        </div>
        <div class="info-item">
          <span class="label">紧急程度</span>
          <span class="value urgency-high">{{ orderDetail.urgency }}</span>
        </div>
        <div class="info-item">
          <span class="label">工单状态</span>
          <span class="value">{{ orderDetail.status }}</span>
        </div>
        <div class="info-item">
          <span class="label">创建时间</span>
          <span class="value">{{ orderDetail.createTime }}</span>
        </div>
        <div class="info-item">
          <span class="label">当前处理人</span>
          <span class="value">{{ orderDetail.handler }}</span>
        </div>
      </div>
    </div>
    <!-- 关联方信息 -->
    <div class="section">
      <h3 class="section-title">关联方信息</h3>
      <div class="info-list">
        <div class="info-item">
          <span class="label">关联订单/阿姨</span>
          <div class="value-group">
            <span class="value">{{ orderDetail.relatedOrder }}</span>
            <span class="sub-value">被投诉方: {{ orderDetail.complainee }}</span>
          </div>
        </div>
        <div class="info-item">
          <span class="label">申请方</span>
          <span class="value">{{ orderDetail.applicant }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联机构</span>
          <span class="value">{{ orderDetail.relatedAgency }}</span>
        </div>
      </div>
    </div>

    <!-- 投诉情况说明 -->
    <div v-if="isComplaintType" class="section">
      <h3 class="section-title">投诉情况说明</h3>
      <div class="complaint-content">
        <div class="info-row">
          <div class="info-item">
            <span class="label">投诉类型</span>
            <span class="value">{{ orderDetail.complaintTypeInfo.main }}</span>
          </div>
          <div class="info-item">
            <span class="label">投诉等级</span>
            <span class="complaint-level">
              <i class="fas fa-exclamation-triangle"></i>
              {{ orderDetail.complaintTypeInfo.level }}
            </span>
          </div>
        </div>
        <div class="info-block">
            <div class="complaint-header">
            <span class="label-block">客户情况说明</span>
          </div>
          <div class="complaint-time-header">
            <span class="complaint-time">投诉时间：{{ orderDetail.complaintTime }}</span>
          </div>
          <div class="complaint-header">
            <span class="label-block">投诉内容：</span>
          </div>
          <div class="complaint-detail-box">
            {{ orderDetail.complaintContent }}
          </div>
        </div>
        <div class="info-block">
          <div class="complaint-header">
            <span class="label-block">客户期望：</span>
          </div>
          <div class="complaint-detail-box">
            {{ orderDetail.customerExpectation }}
          </div>
        </div>
      </div>
    </div>

    <!-- 相关证据材料 -->
    <div v-if="isComplaintType" class="section">
      <h3 class="section-title">相关证据材料</h3>
      <div class="evidence-content">
        <div class="evidence-grid">
          <div v-if="attachmentList.length === 0" class="no-attachments">
            <i class="fas fa-inbox"></i>
            <span>暂无附件</span>
          </div>
          <div 
            v-for="(attachment, index) in attachmentList" 
            :key="index" 
            class="evidence-item"
            @click="viewAttachment(attachment)"
          >
            <div class="evidence-icon">
              <i :class="getFileIcon(attachment.fileType)"></i>
            </div>
            <div class="evidence-name">{{ attachment.fileName }}</div>
            <div class="evidence-action">
              <i class="fas fa-eye"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    

    <!-- 离职申请信息 -->
    <div v-if="isResignationType" class="section">
      <h3 class="section-title">离职申请信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">申请人</span>
          <span class="value">{{ orderDetail.applicantName }}</span>
        </div>
        <div class="info-item">
          <span class="label">员工编号</span>
          <span class="value">{{ orderDetail.employeeId }}</span>
        </div>
        <div class="info-item">
          <span class="label">所属机构</span>
          <span class="value">{{ orderDetail.affiliatedOrganization }}</span>
        </div>
        <div class="info-item">
          <span class="label">入职时间</span>
          <span class="value">{{ orderDetail.employmentDate }}</span>
        </div>
        <div class="info-item">
          <span class="label">申请离职时间</span>
          <span class="value">{{ orderDetail.resignationDate }}</span>
        </div>
        <div class="info-item">
          <span class="label">离职原因</span>
          <span class="value">{{ orderDetail.resignationReason }}</span>
        </div>
      </div>
    </div>

    <!-- 在职期间服务记录 -->
    <div v-if="isResignationType" class="section">
      <h3 class="section-title">在职期间服务记录</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">总服务订单</span>
          <span class="value">{{ orderDetail.totalServiceOrders }}</span>
        </div>
        <div class="info-item">
          <span class="label">已完成订单</span>
          <span class="value">{{ orderDetail.completedOrders }}</span>
        </div>
        <div class="info-item">
          <span class="label">未完成订单</span>
          <span class="value unfinished-orders">{{ orderDetail.unfinishedOrders }}</span>
        </div>
        <div class="info-item">
          <span class="label">客户好评率</span>
          <span class="value">{{ orderDetail.customerSatisfactionRate }}</span>
        </div>
        <div class="info-item">
          <span class="label">投诉记录</span>
          <span class="value">{{ orderDetail.complaintRecords }}</span>
        </div>
        <div class="info-item">
          <span class="label">累计收入</span>
          <span class="value">{{ orderDetail.accumulatedIncome }}</span>
        </div>
      </div>
    </div>

    <!-- 订单交接处理 -->
    <div v-if="isResignationType" class="section">
      <h3 class="section-title handover-title">
        <i class=" warning-icon"></i>
        订单交接处理
      </h3>
      <div class="handover-content">
        <div class="warning-notice">
          <i class="fas fa-exclamation-triangle warning-icon"></i>
          <span class="warning-text">注意:该阿姨还有3个未完成订单需要处理,必须完成订单交接后才能批准离职。</span>
        </div>
        <div class="handover-actions">
          <el-button type="primary" class="start-handover-btn">
            <i class="fas fa-exchange-alt"></i>
            开始订单交接
          </el-button>
          <el-button class="view-orders-btn">
            <i class="fas fa-list"></i>
            查看未完成订单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 离职流程进度 -->
    <div v-if="isResignationType" class="section">
      <h3 class="section-title">离职流程进度</h3>
      <div class="progress-timeline">
        <div class="timeline-line"></div>
        
        <!-- 步骤1：提交离职申请 -->
        <div class="timeline-step completed">
          <div class="step-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="step-content">
            <div class="step-title">提交离职申请</div>
            <div class="step-details">
              <div class="step-detail">阿姨提交离职申请</div>
              <div class="step-time">2024-06-28 14:30</div>
            </div>
          </div>
        </div>

        <!-- 步骤2：订单交接 -->
        <div class="timeline-step current">
          <div class="step-icon">
            <i class="fas fa-exchange-alt"></i>
          </div>
          <div class="step-content">
            <div class="step-title">订单交接</div>
            <div class="step-details">
              <div class="step-detail">处理未完成订单交接</div>
            </div>
          </div>
        </div>

        <!-- 步骤3：离职审批 -->
        <div class="timeline-step pending">
          <div class="step-icon">
            <i class="fas fa-check"></i>
          </div>
          <div class="step-content">
            <div class="step-title">离职审批</div>
            <div class="step-details">
              <div class="step-detail">管理员审批离职申请</div>
            </div>
          </div>
        </div>

        <!-- 步骤4：档案归档 -->
        <div class="timeline-step pending">
          <div class="step-icon">
            <i class="fas fa-archive"></i>
          </div>
          <div class="step-content">
            <div class="step-title">档案归档</div>
            <div class="step-details">
              <div class="step-detail">员工档案归档处理</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理日志 -->
    <div class="section">
      <h3 class="section-title">处理日志</h3>
      <div class="log-content">
        <div class="timeline">
          <div class="timeline-line"></div>
          <div class="log-item" v-for="(log, index) in logList" :key="index">
            <div class="log-dot"></div>
            <div class="log-card">
              <div class="log-header">
                <div class="log-type">
                  <i :class="getLogTypeIcon(log.logType)"></i>
                  {{ getLogTypeText(log.logType) }}
                </div>
                <div class="log-time">{{ log.createTimeStr }}</div>
              </div>
              <div class="log-details">
                <div class="log-detail-item">
                  <span class="label">操作人：</span>
                  <span class="value">{{ log.operatorName }}</span>
                </div>
                <div class="log-detail-item">
                  <span class="label">内容：</span>
                  <span class="value">{{ log.logContent }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理意见 -->
    <div class="section">
      <h3 class="section-title">处理意见</h3>
      <div class="comment-content">
        <el-input
          v-model="processingComment"
          type="textarea"
          :rows="4"
          placeholder="在此输入您的处理意见或回复..."
          resize="both"
        />
        <div class="attachment-section">
          <el-button
            type="text"
            @click="addAttachment"
            class="attachment-btn"
            :loading="uploadLoading"
          >
            <i class="fas fa-paperclip"></i>
            添加附件 (图片/文件)
          </el-button>

          <!-- 隐藏的文件输入框 -->
          <input
            ref="uploadRef"
            type="file"
            multiple
            accept="image/*,.pdf,.doc,.docx"
            style="display: none"
            @change="handleFileUpload"
          />

          <!-- 附件列表 -->
          <div v-if="attachments.length > 0" class="attachments-list">
            <div v-for="attachment in attachments" :key="attachment.id" class="attachment-item">
              <div class="attachment-info">
                <div class="attachment-icon">
                  <i v-if="attachment.type.startsWith('image/')" class="fas fa-image"></i>
                  <i v-else-if="attachment.type === 'application/pdf'" class="fas fa-file-pdf"></i>
                  <i v-else class="fas fa-file-word"></i>
                </div>
                <div class="attachment-details">
                  <div class="attachment-name">{{ attachment.name }}</div>
                  <div class="attachment-meta">
                    {{ formatFileSize(attachment.size) }} • {{ attachment.uploadTime }}
                  </div>
                </div>
              </div>
              <div class="attachment-actions">
                <el-button
                  type="text"
                  size="small"
                  @click="removeAttachment(attachment.id)"
                  class="remove-btn"
                >
                  <i class="fas fa-trash"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="action-buttons">
      <el-button @click="onClose">关闭</el-button>
      <el-button type="primary" @click="onSubmitResult">提交处理结果</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { 
  getWorkOrderDetail, 
  getWorkOrderLogs, 
  getWorkOrderAttachments,
  getTaskDetail,
  submitWorkOrderResolution
} from '@/api/mall/employment/workOrder'
import { ElMessage } from 'element-plus'
// 导入文件上传接口
import { updateFile } from '@/api/infra/file'

/** 定义props */
interface Props {
  task?: any
}

const props = withDefaults(defineProps<Props>(), {
  task: null
})

/** 定义emits */
const emit = defineEmits<{
  close: []
}>()

/** 工单详情数据 */
const orderDetail = ref({
  // 基本信息
  id: '',
  workOrderNo: '',
  orderNo: '',
  workOrderTitle: '',
  workOrderContent: '',
  workOrderType: '',
  priority: '',
  workOrderStatus: '',
  assigneeId: null,
  assigneeName: '',
  auntOneid: '',
  auntName: '',
  leaveType: null,
  startTime: '',
  endTime: '',
  durationHours: 0,
  durationDays: 0,
  status: 0,
  approveTime: '',
  approveRemark: '',
  complaintType: '',
  complaintLevel: '',
  complaintTime: '',
  customerExpectation: '',
  complainerId: null,
  complainerName: '',
  agencyId: null,
  agencyName: '',
  reassignmentStartDate: '',
  newAuntOneid: '',
  newAuntName: '',
  reassignmentDescription: '',
  reassignmentReason: '',
  reassignmentRemark: '',
  taskCount: 0,
  completedTaskCount: 0,
  pendingTaskCount: 0,
  remark: '',
  createTime: '',
  updateTime: '',
  
  // 前端展示字段映射
  taskNo: '',
  urgency: '',
  type: '',
  status: '',
  handler: '',
  relatedOrder: '',
  complainee: '',
  applicant: '',
  relatedAgency: '',
  complaintContent: '',
  customerPhone: '',
  bestContactTime: '',
  
  // 投诉相关
  complaintTypeInfo: {
    main: '',
    level: ''
  },
  
  // 离职申请信息
  applicantName: '',
  employeeId: '',
  affiliatedOrganization: '',
  employmentDate: '',
  resignationDate: '',
  resignationReason: '',
  
  // 在职期间服务记录
  totalServiceOrders: '',
  completedOrders: '',
  unfinishedOrders: '',
  customerSatisfactionRate: '',
  complaintRecords: '',
  accumulatedIncome: ''
})

/** 处理日志列表 */
const logList = ref([])

/** 附件列表 */
const attachmentList = ref([])

/** 任务详情 */
const taskDetail = ref({})

/** 处理意见 */
const processingComment = ref('')

/** 附件列表 */
const attachments = ref<any[]>([])

/** 文件上传相关 */
const uploadRef = ref()
const uploadLoading = ref(false)

/** 计算属性：判断是否为投诉类型 */
const isComplaintType = computed(() => {
  return orderDetail.value.workOrderType === 'complaint'
})

/** 计算属性：判断是否为离职类型 */
const isResignationType = computed(() => {
  return orderDetail.value.workOrderType === 'separation_application'
})

/** 计算属性：判断是否为换人申请类型 */
const isSubstitutionType = computed(() => {
  return orderDetail.value.workOrderType === 'substitution_request'
})

/** 计算属性：判断是否为请假类型 */
const isTakeLeaveType = computed(() => {
  return orderDetail.value.workOrderType === 'take_leave'
})

/** 获取工单详情 */
const fetchOrderDetail = async (workOrderNo: string) => {
  try {
    const res = await getWorkOrderDetail(workOrderNo)
    const data = res
    // 映射接口数据到前端展示字段
    orderDetail.value = {
      ...orderDetail.value,
      ...data,
      // 前端展示字段映射
      taskNo: data.workOrderNo,
      urgency: getPriorityText(data.priority),
      type: getWorkOrderTypeText(data.workOrderType),
      status: getWorkOrderStatusText(data.workOrderStatus),
      handler: data.assigneeName || '-',
      relatedOrder: data.orderNo,
      complainee: data.auntName ? `${data.auntName}(${data.auntOneid})` : '-',
      applicant: data.complainerName ? `雇主: ${data.complainerName}` : '-',
      relatedAgency: data.agencyName,
      complaintContent: data.workOrderContent || '',
      customerPhone: '138****1234', // 接口中暂无此字段
      bestContactTime: '工作日 9:00-18:00', // 接口中暂无此字段
      
      // 投诉相关
      complaintTypeInfo: {
        main: getComplaintTypeText(data.complaintType),
        level: getComplaintLevelText(data.complaintLevel)
      },
      
      // 离职申请信息
      applicantName: data.auntName || '',
      employeeId: data.auntOneid || '',
      affiliatedOrganization: data.agencyName || '',
      employmentDate: '2023-03-15', // 接口中暂无此字段
      resignationDate: '2024-07-15', // 接口中暂无此字段
      resignationReason: '个人原因,需要照顾家庭', // 接口中暂无此字段
      
      // 在职期间服务记录
      totalServiceOrders: `${data.taskCount || 0}个`,
      completedTaskCount: data.completedTaskCount || 0,
      pendingTaskCount: data.pendingTaskCount || 0,
      completedOrders: `${data.completedTaskCount || 0}个`,
      unfinishedOrders: `${data.pendingTaskCount || 0}个`,
      customerSatisfactionRate: '96.5%', // 接口中暂无此字段
      complaintRecords: '0次', // 接口中暂无此字段
      accumulatedIncome: '¥15,680' // 接口中暂无此字段
    }
    
    // 获取相关数据
    await Promise.all([
      fetchWorkOrderLogs(workOrderNo),
      fetchWorkOrderAttachments(workOrderNo),
      fetchTaskDetail(data.orderNo)
    ])
  } catch (error) {
    console.error('获取工单详情失败:', error)
  }
}

/** 获取工单处理日志 */
const fetchWorkOrderLogs = async (workOrderNo: string) => {
  try {
    const res = await getWorkOrderLogs(workOrderNo)
    logList.value = res || []
  } catch (error) {
    console.error('获取工单日志失败:', error)
  }
}

/** 获取工单附件列表 */
const fetchWorkOrderAttachments = async (workOrderNo: string) => {
  try {
    const res = await getWorkOrderAttachments(workOrderNo)
    attachmentList.value = res || []
  } catch (error) {
    console.error('获取工单附件失败:', error)
  }
}

/** 获取任务详情 */
const fetchTaskDetail = async (orderNo: string) => {
  if (!orderNo) return
  try {
    const res = await getTaskDetail(orderNo)
    taskDetail.value = res || {}
  } catch (error) {
    console.error('获取任务详情失败:', error)
  }
}

/** 关闭详情页 */
const onClose = () => {
  emit('close')
}

/** 监听task变化，更新详情数据 */
watch(() => props.task, (newTask) => {
  if (newTask) {
    // 如果有task数据，直接获取工单详情
    if (newTask.workOrderNo || newTask.id) {
      fetchOrderDetail(newTask.workOrderNo || newTask.id)
    } else {
      // 将task数据映射到orderDetail
      orderDetail.value = {
        ...orderDetail.value,
        taskNo: newTask.taskNo || newTask.id,
        urgency: newTask.urgency === 'high' ? '高' : newTask.urgency === 'medium' ? '中' : '低',
        createTime: newTask.createTime,
        type: newTask.typeText || newTask.type,
        status: newTask.statusText || newTask.status,
        handler: newTask.handler || '-',
        relatedOrder: newTask.orderNo || newTask.relatedOrder,
        complainee: newTask.practitioner || newTask.relatedPractitioner,
        applicant: newTask.applicant,
        relatedAgency: newTask.agency,
        complaintTypeInfo: {
          main: '服务质量投诉',
          level: '高'
        },
        complaintTime: '2024-06-27 14:30',
        complaintContent: '阿姨在服务过程中态度恶劣,经常玩手机,不认真工作。昨天下午我回家发现厨房没有打扫,客厅也很乱,完全不符合我们的服务标准。我们支付了高额的服务费用,但得到的服务质量很差。',
        customerExpectation: '希望更换阿姨,或者对当前阿姨进行培训,确保服务质量达到标准。同时要求对已支付的服务费用进行部分退款。',
        customerPhone: '138****1234',
        bestContactTime: '工作日 9:00-18:00',
        processingComment: '',
        // 离职申请信息
        applicantName: '陈美华',
        employeeId: 'AY00128',
        affiliatedOrganization: '阳光家政',
        employmentDate: '2023-03-15',
        resignationDate: '2024-07-15',
        resignationReason: '个人原因,需要照顾家庭',
        // 在职期间服务记录
        totalServiceOrders: '45个',
        completedOrders: '42个',
        unfinishedOrders: '3个',
        customerSatisfactionRate: '96.5%',
        complaintRecords: '0次',
        accumulatedIncome: '¥15,680'
      }
    }
  }
}, { immediate: true })

/** 提交处理结果 */
const onSubmitResult = async () => {
  if (!processingComment.value.trim()) {
    ElMessage.warning('请输入处理意见')
    return
  }
  
  try {
    // 构建附件数组，按照接口文档的字段要求
    const attachmentsArray = attachments.value.map(item => ({
      fileName: item.name,
      fileUrl: item.url,
      fileType: item.type,
      fileCategory: getFileCategory(item.type), // 根据文件类型判断分类
      uploadPurpose: '处理结果附件'
    }))
    
    const data = {
      workOrderNo: orderDetail.value.workOrderNo,
      logType: 'resolution',
      logContent: processingComment.value,
      attachments: attachmentsArray
    }
    
    const res = await submitWorkOrderResolution(data)
    ElMessage.success('处理结果提交成功')
    // 重新获取工单详情
    await fetchOrderDetail(orderDetail.value.workOrderNo)
    processingComment.value = ''
    // 清空附件列表
    attachments.value = []
  } catch (error) {
    console.error('提交处理结果失败:', error)
    ElMessage.error('提交失败，请重试')
  }
}

/** 工具函数：获取工单类型文本 */
const getWorkOrderTypeText = (type: string) => {
  const typeMap = {
    'complaint': '投诉',
    'substitution_request': '换人申请',
    'take_leave': '请假/顶岗',
    'separation_application': '离职申请'
  }
  return typeMap[type] || type
}

/** 工具函数：获取工单状态文本 */
const getWorkOrderStatusText = (status: string) => {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'resolved': '已解决',
    'closed': '已关闭'
  }
  return statusMap[status] || status
}

/** 工具函数：获取优先级文本 */
const getPriorityText = (priority: string) => {
  const priorityMap = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'urgent': '紧急'
  }
  return priorityMap[priority] || priority
}

/** 工具函数：获取投诉类型文本 */
const getComplaintTypeText = (type: string) => {
  const typeMap = {
    'service_quality': '服务质量',
    'attitude': '服务态度',
    'punctuality': '守时问题',
    'other': '其他'
  }
  return typeMap[type] || type
}

/** 工具函数：获取投诉等级文本 */
const getComplaintLevelText = (level: string) => {
  const levelMap = {
    'low': '轻微',
    'medium': '中等',
    'high': '严重',
    'urgent': '紧急'
  }
  return levelMap[level] || level
}

/** 工具函数：获取日志类型文本 */
const getLogTypeText = (logType: string) => {
  const typeMap = {
    'creation': '工单创建',
    'status_change': '状态变更',
    'assignment': '分配处理人',
    'comment': '处理意见',
    'resolution': '处理结果'
  }
  return typeMap[logType] || logType
}

/** 工具函数：获取日志类型图标 */
const getLogTypeIcon = (logType: string) => {
  const iconMap = {
    'creation': 'fas fa-file-alt',
    'status_change': 'fas fa-exchange-alt',
    'assignment': 'fas fa-user-plus',
    'comment': 'fas fa-comment',
    'resolution': 'fas fa-check-circle'
  }
  return iconMap[logType] || 'fas fa-info-circle'
}

/** 工具函数：获取文件类型图标 */
const getFileIcon = (fileType: string) => {
  if (fileType?.includes('image')) {
    return 'fas fa-image'
  } else if (fileType?.includes('audio')) {
    return 'fas fa-volume-up'
  } else if (fileType?.includes('video')) {
    return 'fas fa-video'
  } else if (fileType?.includes('pdf')) {
    return 'fas fa-file-pdf'
  } else if (fileType?.includes('word') || fileType?.includes('document')) {
    return 'fas fa-file-word'
  } else if (fileType?.includes('excel') || fileType?.includes('spreadsheet')) {
    return 'fas fa-file-excel'
  } else {
    return 'fas fa-file'
  }
}

/** 查看附件 */
const viewAttachment = (attachment: any) => {
  if (attachment.fileUrl) {
    window.open(attachment.fileUrl, '_blank')
  }
}

/** 添加附件 */
const addAttachment = () => {
  uploadRef.value?.click()
}

/** 文件上传前处理 */
const beforeUpload = (file: File) => {
  // 检查文件类型
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]
  const isValidType = allowedTypes.includes(file.type)

  if (!isValidType) {
    ElMessage.error('只支持图片、PDF和Word文档格式！')
    return false
  }

  // 检查文件大小 (10MB)
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB！')
    return false
  }

  return true
}

/** 文件上传处理 */
const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (!files || files.length === 0) return

  uploadLoading.value = true

  try {
    // 逐个上传文件
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      
      if (!beforeUpload(file)) {
        continue
      }

      // 创建FormData，参考套餐主图上传的实现
      const uploadFormData = new FormData()
      uploadFormData.append('file', file)

      // 调用文件上传接口
      const response = await updateFile(uploadFormData)
      
      if (response && response.data) {
        const attachment = {
          id: Date.now() + i,
          name: file.name,
          size: file.size,
          type: file.type,
          url: response.data,
          uploadTime: new Date().toLocaleString()
        }

        attachments.value.push(attachment)
        ElMessage.success(`文件 ${file.name} 上传成功`)
      }
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败，请重试')
  } finally {
    uploadLoading.value = false
    // 清空input值，允许重复上传同一文件
    target.value = ''
  }
}

/** 删除附件 */
const removeAttachment = (attachmentId: number) => {
  const index = attachments.value.findIndex((item) => item.id === attachmentId)
  if (index > -1) {
    attachments.value.splice(index, 1)
    ElMessage.success('附件删除成功')
  }
}

/** 格式化文件大小 */
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/** 根据文件类型获取文件分类 */
const getFileCategory = (fileType: string) => {
  if (fileType?.startsWith('image/')) {
    return 'evidence' // 图片作为证据材料
  } else if (fileType === 'application/pdf') {
    return 'document' // PDF作为文档
  } else if (fileType?.includes('word') || fileType?.includes('document')) {
    return 'document' // Word文档作为文档
  } else {
    return 'other' // 其他类型
  }
}

onMounted(() => {
  // 如果有task数据，直接使用；否则获取默认数据
  if (props.task) {
    // task数据会在watch中处理
  } else {
    // TODO: 从路由参数获取工单ID
    const orderId = 'GD20240627001'
    fetchOrderDetail(orderId)
  }
})
</script>

<style scoped lang="scss">
.order-detail {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
  background: white;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;

    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      width: 32px;
      height: 32px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #e9ecef;
      }

      i {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .section {
    margin-bottom: 30px;

    .section-title {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      padding-bottom: 8px;
      border-bottom: 1px solid #e9ecef;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px 30px;

      .info-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .label {
          font-weight: 500;
          color: #666;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .value {
          color: #333;
          font-size: 16px;
          font-weight: 500;

          &.urgency-high {
            color: #e74c3c;
            font-weight: 600;
          }

          &.unfinished-orders {
            color: #e74c3c;
            font-weight: 600;
          }
        }
      }
    }

    .info-list {
      .info-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: #666;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .value-group {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .value {
            color: #333;
            font-weight: 600;
            font-size: 16px;
          }

          .sub-value {
            color: #999;
            font-size: 13px;
            margin-left: 0;
          }
        }

        .value {
          color: #333;
          font-size: 16px;
          font-weight: 500;
        }
      }
    }

      .complaint-content {
        .info-row {
          display: flex;
          margin-bottom: 20px;

          .info-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .label {
              font-weight: 500;
              color: #666;
              margin-bottom: 8px;
              font-size: 14px;
            }

            .value {
              color: #333;
              font-size: 16px;
              font-weight: 500;
            }

            .complaint-level {
              display: flex;
              align-items: center;
              gap: 6px;
              background: #fff3cd;
              color: #f39c12;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 500;

              i {
                font-size: 12px;
              }
            }
          }
        }

        .info-block {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .complaint-time-header {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 8px;

            .complaint-time {
              font-size: 12px;
              color: #999;
            }
          }

          .complaint-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .label-block {
              font-weight: 500;
              color: #666;
              font-size: 14px;
            }

            .complaint-time {
              font-size: 12px;
              color: #999;
            }
          }

          .complaint-header:not(:has(.complaint-time)) {
            justify-content: flex-start;
          }

          .complaint-detail-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            min-height: 80px;
            color: #333;
            line-height: 1.6;
            font-size: 14px;
          }
        }
      }

      .customer-content {
        .info-block {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .complaint-time-header {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 8px;

            .complaint-time {
              font-size: 12px;
              color: #999;
            }
          }

          .complaint-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .label-block {
              font-weight: 500;
              color: #666;
              font-size: 14px;
            }

            .complaint-time {
              font-size: 12px;
              color: #999;
            }
          }

          .complaint-header:not(:has(.complaint-time)) {
            justify-content: flex-start;
          }

          .complaint-detail-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            min-height: 80px;
            color: #333;
            line-height: 1.6;
            font-size: 14px;
          }
        }
      }

      .evidence-content {
        .evidence-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;

          .no-attachments {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border: 2px dashed #e9ecef;
            border-radius: 8px;
            padding: 40px 20px;
            color: #999;
            font-size: 14px;
            grid-column: 1 / -1;

            i {
              font-size: 24px;
              margin-bottom: 8px;
              color: #ccc;
            }
          }

          .evidence-item {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: #e9ecef;
              border-color: #409eff;
            }

            .evidence-icon {
              margin-right: 8px;
              color: #409eff;
              font-size: 16px;
            }

            .evidence-name {
              flex: 1;
              color: #333;
              font-size: 14px;
            }

            .evidence-action {
              color: #666;
              font-size: 14px;
              cursor: pointer;

              &:hover {
                color: #409eff;
              }
            }
          }
        }
      }

      

      .log-content {
        .timeline {
          position: relative;
          padding-left: 20px;
          max-height: 300px; // 限制最大高度，大约3条日志的高度
          overflow-y: auto; // 添加垂直滚动条
          
          // 自定义滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
          }
          
          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }
          
          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
            
            &:hover {
              background: #a8a8a8;
            }
          }

          .timeline-line {
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
          }

          .log-item {
            position: relative;
            margin-bottom: 20px;

            &:last-child {
              margin-bottom: 0;
            }

            .log-dot {
              position: absolute;
              left: -15px;
              top: 20px;
              width: 12px;
              height: 12px;
              background: #409eff;
              border-radius: 50%;
              border: 2px solid white;
              box-shadow: 0 0 0 2px #409eff;
            }

            .log-card {
              background: white;
              border: 1px solid #e9ecef;
              border-radius: 8px;
              padding: 16px;
              margin-left: 20px;

              .log-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;

                .log-type {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  font-weight: 600;
                  color: #333;
                  font-size: 14px;

                  i {
                    color: #409eff;
                    font-size: 14px;
                  }
                }

                .log-time {
                  font-size: 12px;
                  color: #999;
                }
              }

              .log-details {
                .log-detail-item {
                  display: flex;
                  margin-bottom: 8px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .label {
                    font-weight: 500;
                    color: #666;
                    min-width: 60px;
                    margin-right: 8px;
                  }

                  .value {
                    color: #333;
                    flex: 1;
                  }
                }
              }
            }
          }
        }
      }

      .comment-content {
        .el-textarea {
          margin-bottom: 12px;
        }

        .attachment-section {
          .attachment-btn {
            color: #409eff;
            font-size: 13px;
            padding: 0;

            i {
              margin-right: 6px;
            }

            &:hover {
              color: #66b1ff;
            }
          }

          .attachments-list {
            margin-top: 12px;

            .attachment-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 12px;
              background: #f8f9fa;
              border-radius: 6px;
              margin-bottom: 8px;
              border: 1px solid #e9ecef;

              &:last-child {
                margin-bottom: 0;
              }

              .attachment-info {
                display: flex;
                align-items: center;
                flex: 1;

                .attachment-icon {
                  width: 32px;
                  height: 32px;
                  background: #e3f2fd;
                  border-radius: 4px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 12px;

                  i {
                    color: #1976d2;
                    font-size: 14px;
                  }
                }

                .attachment-details {
                  flex: 1;

                  .attachment-name {
                    font-weight: 500;
                    color: #333;
                    font-size: 14px;
                    margin-bottom: 4px;
                    word-break: break-all;
                  }

                  .attachment-meta {
                    color: #666;
                    font-size: 12px;
                  }
                }
              }

              .attachment-actions {
                .remove-btn {
                  color: #f56c6c;
                  padding: 4px;

                  &:hover {
                    color: #f78989;
                  }

                  i {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }

        .handover-title {
          display: flex;
          align-items: center;
          gap: 10px;
          color: #e74c3c;
          font-weight: 600;

          .warning-icon {
            color: #e74c3c;
            font-size: 18px;
          }
        

        .warning-notice {
          display: flex;
          align-items: flex-start;
          gap: 10px;
          margin-bottom: 20px;

          .warning-icon {
            color: #e74c3c;
            font-size: 16px;
            margin-top: 2px;
          }

          .warning-text {
            color: #333;
            font-size: 14px;
            line-height: 1.6;
            flex: 1;
          }
        }

        .handover-actions {
          display: flex;
          gap: 12px;

          .start-handover-btn {
            background: #409eff;
            border-color: #409eff;
            color: white;

            &:hover {
              background: #337ecc;
              border-color: #337ecc;
            }

            i {
              margin-right: 6px;
            }
          }

          .view-orders-btn {
            background: white;
            border-color: #d9d9d9;
            color: #666;

            &:hover {
              border-color: #409eff;
              color: #409eff;
            }

            i {
              margin-right: 6px;
            }
          }
        }
      }

      .progress-timeline {
        position: relative;
        padding-left: 30px;

        .timeline-line {
          position: absolute;
          left: 15px;
          top: 0;
          bottom: 0;
          width: 2px;
          background: #e9ecef;
        }

        .timeline-step {
          position: relative;
          margin-bottom: 30px;

          &:last-child {
            margin-bottom: 0;
          }

          .step-icon {
            position: absolute;
            left: -22px;
            top: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #e9ecef;

            i {
              color: white;
              font-size: 12px;
            }
          }

          .step-content {
            margin-left: 20px;
            
            .step-title {
              font-size: 16px;
              font-weight: 600;
              color: #333;
              margin-bottom: 8px;
            }

            .step-details {
              .step-detail {
                color: #666;
                font-size: 14px;
                margin-bottom: 4px;
              }

              .step-time {
                color: #999;
                font-size: 12px;
              }
            }
          }

          // 已完成状态
          &.completed {
            .step-icon {
              background: #28a745;
              box-shadow: 0 0 0 2px #28a745;
            }

            .step-title {
              color: #28a745;
            }
          }

          // 当前状态
          &.current {
            .step-icon {
              background: #007bff;
              box-shadow: 0 0 0 2px #007bff;
            }

            .step-title {
              color: #007bff;
            }
          }

          // 待处理状态
          &.pending {
            .step-icon {
              background: #6c757d;
              box-shadow: 0 0 0 2px #6c757d;
            }

            .step-title {
              color: #6c757d;
            }
          }
        }
      }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .el-button {
      min-width: 80px;
    }
  }
}
</style> 