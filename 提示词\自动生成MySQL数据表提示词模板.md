# 自动生成MySQL数据表（含公共字段）的AI提示词模板

---

## 1. 标准公共字段清单（建议）

| 字段名        | 类型         | 说明               | 备注/建议                |
| ------------- | ------------ | ------------------ | ------------------------ |
| id            | BIGINT       | 主键，自增         | PRIMARY KEY AUTO_INCREMENT |
| tenant_id     | BIGINT       | 租户ID             | 多租户场景建议必加        |
| creator       | VARCHAR(64)  | 创建人             | 可为空，记录操作人        |
| create_time   | DATETIME     | 创建时间           | 默认CURRENT_TIMESTAMP    |
| updater       | VARCHAR(64)  | 更新人             | 可为空，记录操作人        |
| update_time   | DATETIME     | 更新时间           | 默认CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |
| deleted       | BIT(1)       | 是否删除（软删除） | 0-未删除，1-已删除，默认0 |

你可以根据实际需要增减，比如加上组织ID、部门ID等。

---

## 2. 复制给AI的完整提示词模板

```
请根据以下页面字段信息，帮我自动生成一份MySQL数据表的建表SQL，要求如下：
- 除了页面字段外，**每张表都要自动包含以下公共字段**：
  - id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增'
  - tenant_id BIGINT COMMENT '租户ID'
  - creator VARCHAR(64) COMMENT '创建人'
  - create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
  - updater VARCHAR(64) COMMENT '更新人'
  - update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
  - deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除'
- 页面字段的字段名、类型、长度、是否必填、默认值、备注等要与页面字段完全对应。
- 字段类型要合理选择（如字符串用VARCHAR，数字用INT/BIGINT，日期用DATE/DATETIME，布尔用TINYINT(1)）。
- 如有唯一性、索引、外键、枚举、默认值等需求请自动补全。
- 每个字段都要加详细的中文注释（COMMENT）。
- 表本身要加注释（如：COMMENT='xxx表'）。
- 生成的SQL要符合MySQL 5.7/8.0标准，支持utf8mb4编码。
- 如果有枚举/下拉选项，请在注释中列出所有可选值。
- 生成的SQL要美观、易读、可直接执行。

页面字段信息如下（请用表格或JSON格式给出）：
| 字段名      | 类型     | 必填 | 备注/说明           | 其他要求         |
| ----------- | -------- | ---- | ------------------ | --------------- |
| name        | string   | 是   | 用户名             | 唯一            |
| gender      | enum     | 否   | 性别               | 男/女/未知      |
| age         | number   | 否   | 年龄               |                 |
| phone       | string   | 否   | 手机号             |                 |
| status      | enum     | 是   | 状态               | 启用/禁用       |
```

---

## 3. 进阶建议

- 如果有多张表，公共字段都要自动加上。
- 如果有组织ID、部门ID等多组织需求，也可加到公共字段里。
- 你可以让AI自动生成所有索引、唯一约束、外键等。
- 你可以让AI自动生成表的DDL和注释文档。
- 可根据实际业务扩展更多公共字段。

---

## 4. 结论

只要用本提示词+页面字段信息，AI就能一键生成**带有所有公共字段的高质量MySQL建表SQL**，极大提升开发效率和规范性！

如需具体SQL示例、自动化脚本、表结构优化建议，随时告诉我！ 