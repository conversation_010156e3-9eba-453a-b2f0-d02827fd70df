import request from '@/config/axios'

/**
 * 资讯管理API接口
 */

// 资讯信息接口
export interface News {
  id: number
  newsTitle: string
  newsSummary: string
  newsContent: string
  categoryId: number
  categoryName: string
  coverImageUrl: string
  materialId?: number
  author: string
  publishTime?: string
  status: 'draft' | 'published' | 'offline'
  viewCount: number
  likeCount: number
  shareCount: number
  commentCount: number
  sort: number
  createTime: string
  updateTime: string
  tenantId?: number
  creator?: string
  updater?: string
}

// 查询参数接口
export interface NewsQueryParams {
  pageNo: number
  pageSize: number
  newsTitle?: string
  categoryId?: number
  status?: string
  contentSource?: string
  startTime?: string
  endTime?: string
}

// 新增资讯参数接口
export interface CreateNewsParams {
  newsTitle: string
  newsSummary: string
  newsContent: string
  categoryId: number
  coverImageUrl: string
  materialId?: number
  author: string
  publishTime?: string
  status: 'draft' | 'published'
  sort?: number
}

// 更新资讯参数接口
export interface UpdateNewsParams {
  id: number
  newsTitle: string
  newsSummary: string
  newsContent: string
  categoryId: number
  coverImageUrl: string
  materialId?: number
  author: string
  publishTime?: string
  status: 'draft' | 'published'
  sort?: number
}

// 状态更新参数接口
export interface UpdateStatusParams {
  id: number
  status: 'published' | 'offline'
}

/**
 * 分页查询资讯列表
 * @param params 查询参数
 * @returns Promise<{ list: News[], total: number }>
 */
export function getNewsList(params: NewsQueryParams) {
  return request.post({
    url: '/publicbiz/news/page',
    data: params
  })
}

/**
 * 获取资讯详情
 * @param id 资讯ID
 * @returns Promise<News>
 */
export function getNewsDetail(id: number) {
  return request.get({
    url: `/publicbiz/news/detail/${id}`
  })
}

/**
 * 创建资讯
 * @param data 资讯信息
 * @returns Promise<{ id: number, newsTitle: string, createTime: string }>
 */
export function createNews(data: CreateNewsParams) {
  return request.post({
    url: '/publicbiz/news/create',
    data
  })
}

/**
 * 更新资讯
 * @param data 更新信息
 * @returns Promise<{ id: number, updateTime: string }>
 */
export function updateNews(data: UpdateNewsParams) {
  return request.post({
    url: '/publicbiz/news/update',
    data
  })
}

/**
 * 删除资讯
 * @param id 资讯ID
 * @returns Promise<void>
 */
export function deleteNews(id: number) {
  return request.delete({
    url: `/publicbiz/news/delete/${id}`
  })
}

/**
 * 更新资讯状态
 * @param id 资讯ID
 * @param status 状态
 * @returns Promise<{ id: number, status: string, updateTime: string }>
 */
export function updateNewsStatus(id: number, status: 'published' | 'offline') {
  return request.post({
    url: '/publicbiz/news/updateStatus',
    data: { id, status }
  })
}

// 素材库文章查询参数接口
export interface MaterialArticles {
  pageNo: number
  pageSize: number
  title?: string
  sourceOrgId?: number
  categoryId?: number
  visibleOrgId?: number
  visibleOrgName?: string
}

// 素材库文章对象接口
export interface MaterialArticle {
  id: number
  title: string
  content: string
  author: string
  source: string
  publishDate: string
  readCount: number
  sourceOrgId?: number
  sourceOrgName?: string
  categoryId?: number
  description?: string
  createTime?: string
  visibleOrgId?: number
  visibleOrgName?: string
}

/**
 * 获取素材库文章列表
 * @param params 查询参数
 * @returns Promise<{ list: MaterialArticle[], total: number }>
 */
export function getMaterialArticles(params: MaterialArticles) {
  return request.get({
    url: '/system/material/article/list',
    params
  })
}
