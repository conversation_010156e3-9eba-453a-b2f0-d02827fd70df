<!--
  页面名称：家政服务订单查看详情
  功能描述：展示家政服务订单详情，支持多个标签页切换
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="家政服务订单详情"
    direction="rtl"
    size="80%"
    :before-close="handleClose"
  >
    <div class="order-view-container">
      <!-- 订单头部信息 -->
      <div class="order-header">
        <div class="service-banner">
          <div class="service-title"
            >{{ orderDetail.serviceType }} - {{ orderDetail.customerName }}</div
          >
          <div class="service-status">
            <el-button size="small" type="primary" plain>{{
              getServiceStatusText(orderDetail.serviceStatus)
            }}</el-button>
          </div>
          <!-- 隐藏编辑按钮 -->
          <!-- <el-button size="small" type="primary" plain class="edit-btn" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button> -->
        </div>
        <div class="order-info">
          <div class="order-number">订单号：{{ orderDetail.orderNumber }}</div>
          <div class="payment-status">
            支付状态：
            <el-tag type="success" size="small">{{
              getPaymentStatusText(orderDetail.paymentStatus)
            }}</el-tag>
          </div>
        </div>
      </div>

      <!-- 标签页导航 -->
      <div class="tab-navigation">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="基本信息" name="basic" />
          <el-tab-pane label="服务任务列表" name="tasks" />
          <el-tab-pane label="收款信息" name="payment" />
          <el-tab-pane label="操作日志" name="operation" />
          <el-tab-pane label="人员变动" name="personnel" />
          <el-tab-pane label="收支记录" name="income" />
          <el-tab-pane label="服务评价" name="evaluation" />
          <el-tab-pane label="售后记录" name="afterSales" />
        </el-tabs>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <div v-if="activeTab === 'basic'">
          <BasicInfoTab :order-detail="orderDetail" />
        </div>
        <div v-else-if="activeTab === 'tasks'">
          <ServiceTaskTab :order-detail="orderDetail" />
        </div>
        <div v-else-if="activeTab === 'payment'">
          <PaymentInfoTab :order-detail="orderDetail" />
        </div>
        <div v-else-if="activeTab === 'operation'">
          <OperationLogTab :order-detail="orderDetail" />
        </div>
        <div v-else-if="activeTab === 'personnel'">
          <PersonnelChangeTab :order-detail="orderDetail" />
        </div>
        <div v-else-if="activeTab === 'income'">
          <IncomeExpenseTab
            ref="incomeExpenseTabRef"
            :order-detail="orderDetail"
            :records="incomeExpenseRecords"
            @add-record="handleAddRecord"
            @edit-record="handleEditRecord"
            @update-records="handleUpdateIncomeRecords"
          />
        </div>
        <div v-else-if="activeTab === 'evaluation'">
          <ServiceEvaluationTab
            :order-detail="orderDetail"
            :evaluation-data="serviceEvaluationData"
            @add-evaluation="handleAddEvaluation"
            @edit-evaluation="handleEditEvaluation"
          />
        </div>
        <div v-else-if="activeTab === 'afterSales'">
          <AfterSalesTab
            ref="afterSalesTabRef"
            :order-detail="orderDetail"
            :records="afterSalesRecords"
            @add-after-sales="handleAddAfterSales"
            @edit-record="handleEditAfterSales"
            @update-records="handleUpdateAfterSalesRecords"
          />
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </div>

    <!-- 新增收支记录抽屉 -->
    <IncomeExpenseForm
      v-model:visible="incomeFormVisible"
      :order-id="orderDetail.id"
      :record-data="editingRecord"
      @success="handleIncomeSuccess"
    />

    <!-- 新增售后工单抽屉 -->
    <AfterSalesForm
      v-model:visible="afterSalesFormVisible"
      :order-id="orderDetail.id"
      :record-data="editingAfterSalesRecord"
      @success="handleAfterSalesSuccess"
    />

    <!-- 服务评价表单抽屉 -->
    <ServiceEvaluationForm
      v-model:visible="evaluationFormVisible"
      :order-id="orderDetail.id"
      :evaluation-data="editingEvaluationRecord"
      @success="handleEvaluationSuccess"
    />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import BasicInfoTab from './tabs/BasicInfoTab.vue'
import ServiceTaskTab from './tabs/ServiceTaskTab.vue'
import PaymentInfoTab from './tabs/PaymentInfoTab.vue'
import OperationLogTab from './tabs/OperationLogTab.vue'
import PersonnelChangeTab from './tabs/PersonnelChangeTab.vue'
import IncomeExpenseTab from './tabs/IncomeExpenseTab.vue'
import ServiceEvaluationTab from './tabs/ServiceEvaluationTab.vue'
import AfterSalesTab from './tabs/AfterSalesTab.vue'
import IncomeExpenseForm from './forms/IncomeExpenseForm.vue'
import AfterSalesForm from './forms/AfterSalesForm.vue'
import ServiceEvaluationForm from './forms/ServiceEvaluationForm.vue'

// Props
interface Props {
  visible: boolean
  orderId?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  edit: [orderData: any]
}>()

// 响应式数据
const activeTab = ref('basic')
const incomeFormVisible = ref(false)
const afterSalesFormVisible = ref(false)
const evaluationFormVisible = ref(false)
const editingRecord = ref<any>(null)
const editingAfterSalesRecord = ref<any>(null)
const editingEvaluationRecord = ref<any>(null)
const incomeExpenseTabRef = ref()
const afterSalesTabRef = ref()

// 收支记录数据
const incomeExpenseRecords = ref([
  {
    id: 1,
    type: '额外收入',
    amount: 50,
    date: '2024-06-10',
    description: '额外帮助客户采购婴儿用品,客户支付的跑腿费。'
  },
  {
    id: 2,
    type: '服务收入',
    amount: 12800,
    date: '2024-06-01',
    description: '月嫂服务基础费用。'
  },
  {
    id: 3,
    type: '额外支出',
    amount: 200,
    date: '2024-06-05',
    description: '购买婴儿用品垫付费用。'
  }
])

// 售后记录数据
const afterSalesRecords = ref([
  {
    id: 1,
    workOrderType: '物品损坏',
    problemDescription: '客户反映在服务过程中，婴儿床的床垫出现了轻微磨损。',
    workOrderStatus: 'pending',
    processingResult: '',
    createTime: '2024-06-15 10:30:00'
  },
  {
    id: 2,
    workOrderType: '服务不满意',
    problemDescription: '客户对服务人员的专业度表示不满意，希望更换服务人员。',
    workOrderStatus: 'processing',
    processingResult: '已安排新的服务人员，正在协调交接事宜。',
    createTime: '2024-06-12 14:20:00'
  }
])

// 服务评价数据
const serviceEvaluationData = ref({
  overallRating: 5,
  tags: ['非常专业', '准时到达', '清洁彻底'],
  comment: '非常满意的一次服务!团队很专业,干活利索,边边角角都处理得很干净,下次有需要还会再来。',
  evaluationTime: '2024-06-15 16:30:00'
})

// 订单详情数据
const orderDetail = reactive({
  id: '',
  orderNumber: 'DS202406001',
  customerName: '李女士',
  customerPhone: '13812345678',
  serviceType: '月嫂服务',
  serviceAgency: '爱家月嫂服务中心',
  servicePersonnel: '张阿姨',
  serviceAddress: '上海市浦东新区世纪大道1号',
  serviceAmount: 12800,
  paymentStatus: 'paid',
  serviceStatus: 'in_service',
  appointmentTime: '2024/6/1 09:00:00'
})

// 方法定义
const handleClose = () => {
  emit('update:visible', false)
}

const handleEdit = () => {
  emit('edit', orderDetail)
}

const handleTabClick = (tab: any) => {
  console.log('切换到标签页:', tab.props.name)
}

const handleAddRecord = () => {
  editingRecord.value = null
  incomeFormVisible.value = true
}

const handleEditRecord = (record: any) => {
  // 设置编辑数据并打开表单
  editingRecord.value = record
  incomeFormVisible.value = true
}

const handleAddAfterSales = () => {
  afterSalesFormVisible.value = true
}

const handleIncomeSuccess = (data?: any) => {
  const wasEditing = editingRecord.value !== null
  console.log('收支记录成功处理:', { data, wasEditing, editingRecord: editingRecord.value })

  if (data) {
    if (wasEditing && editingRecord.value) {
      // 编辑模式：更新现有记录
      const currentRecord = editingRecord.value
      const updatedRecord = { ...currentRecord, ...data }
      console.log('调用更新方法:', updatedRecord)
      incomeExpenseTabRef.value?.updateRecord(updatedRecord)
    } else {
      // 新增模式：添加新记录
      console.log('调用添加方法:', data)
      incomeExpenseTabRef.value?.addRecord(data)
    }
  }

  incomeFormVisible.value = false
  editingRecord.value = null
}

const handleUpdateIncomeRecords = (records: any[]) => {
  console.log('更新收支记录数据:', records)
  incomeExpenseRecords.value = records
}

const handleUpdateAfterSalesRecords = (records: any[]) => {
  console.log('更新售后记录数据:', records)
  afterSalesRecords.value = records
}

const handleEditAfterSales = (record: any) => {
  // 设置编辑数据并打开表单
  editingAfterSalesRecord.value = record
  afterSalesFormVisible.value = true
}

const handleAddEvaluation = () => {
  editingEvaluationRecord.value = null
  evaluationFormVisible.value = true
}

const handleEditEvaluation = (evaluation: any) => {
  editingEvaluationRecord.value = evaluation
  evaluationFormVisible.value = true
}

const handleAfterSalesSuccess = (data?: any) => {
  console.log('售后记录成功处理:', data)
  const wasEditing = editingAfterSalesRecord.value !== null

  if (data) {
    if (wasEditing && editingAfterSalesRecord.value) {
      // 编辑模式：更新现有记录
      const currentRecord = editingAfterSalesRecord.value
      const updatedRecord = { ...currentRecord, ...data }
      console.log('调用售后更新方法:', updatedRecord)
      afterSalesTabRef.value?.updateRecord(updatedRecord)
    } else {
      // 新增模式：添加新记录
      console.log('调用售后添加方法:', data)
      afterSalesTabRef.value?.addRecord(data)
    }
  }

  afterSalesFormVisible.value = false
  editingAfterSalesRecord.value = null
}

const handleEvaluationSuccess = (data?: any) => {
  console.log('服务评价成功处理:', data)

  if (data) {
    // 更新服务评价数据
    serviceEvaluationData.value = { ...data }
    console.log('服务评价数据已更新:', data)
  }

  evaluationFormVisible.value = false
  editingEvaluationRecord.value = null
}

const getServiceStatusText = (status: string): string => {
  const statusMap = {
    pending: '待派单',
    assigned: '已派单',
    in_service: '服务中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const getPaymentStatusText = (status: string): string => {
  const statusMap = {
    unpaid: '未支付',
    paid: '已支付',
    partial: '部分支付'
  }
  return statusMap[status] || '未知'
}

// 监听visible变化，加载订单详情
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.orderId) {
      loadOrderDetail()
    }
  }
)

const loadOrderDetail = async () => {
  try {
    // 这里应该调用API获取订单详情
    // const response = await getOrderDetail(props.orderId)
    // Object.assign(orderDetail, response.data)
    console.log('加载订单详情:', props.orderId)

    // 模拟数据加载
    if (props.orderId) {
      orderDetail.id = props.orderId
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
  }
}
</script>

<style scoped lang="scss">
.order-view-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.order-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  margin-bottom: 0;

  .service-banner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .service-title {
      font-size: 20px;
      font-weight: bold;
    }

    .service-status {
      margin-left: auto;
      margin-right: 10px;
    }

    .edit-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .order-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;

    .order-number {
      font-weight: 500;
    }

    .payment-status {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.tab-navigation {
  background: #f5f5f5;
  padding: 0 20px;
  border-bottom: 1px solid #e4e7ed;

  :deep(.el-tabs) {
    .el-tabs__header {
      margin: 0;
    }

    .el-tabs__content {
      display: none;
    }
  }
}

.content-area {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.drawer-footer {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
  background: white;
}
</style>
