import request from '@/config/axios'

/**
 * 工单管理API接口
 */

// 工单详情查询接口
export function getWorkOrderDetail(workOrderNo: string) {
  return request.get({
    url: `/publicbiz/work-order/detail/${workOrderNo}`
  })
}

// 工单分页查询接口
export function getWorkOrderPage(params: any) {
  return request.post({
    url: '/publicbiz/work-order/page',
    data: params
  })
}

// 工单类型统计接口
export function getWorkOrderTypeStats() {
  return request.get({
    url: '/publicbiz/work-order/typeStats'
  })
}

// 工单接单接口
export function acceptWorkOrder(data: any) {
  return request.post({
    url: '/publicbiz/work-order/accept',
    data
  })
}

// 工单转派接口
export function transferWorkOrder(data: any) {
  return request.post({
    url: '/publicbiz/work-order/transfer',
    data
  })
}

// 工单处理日志查询接口
export function getWorkOrderLogs(workOrderNo: string) {
  return request.get({
    url: `/publicbiz/work-order/logs/${workOrderNo}`
  })
}

// 工单附件上传接口
export function uploadWorkOrderAttachment(data: any) {
  return request.post({
    url: '/publicbiz/work-order/uploadAttachment',
    data
  })
}

// 工单附件列表查询接口
export function getWorkOrderAttachments(workOrderNo: string) {
  return request.get({
    url: `/publicbiz/work-order/attachments/${workOrderNo}`
  })
}

// 获取阿姨列表接口
export function getWorkOrderPractitioners() {
  return request.get({
    url: '/publicbiz/work-order/workOrderPractitioners'
  })
}

// 获取服务任务详细接口
export function getTaskDetail(orderNo: string) {
  return request.get({
    url: `/publicbiz/work-order/taskDetail/${orderNo}`
  })
}

// 获取任务列表接口
export function getTaskList(orderNo: string, data?: any) {
  return request.put({
    url: `/publicbiz/work-order/taskList/${orderNo}`,
    data
  })
}

// 重新指派任务接口
export function reassignTask(data: any) {
  return request.post({
    url: '/publicbiz/work-order/reassignTask',
    data
  })
}

// 任务工单提交处理结果接口
export function submitWorkOrderResolution(data: any) {
  return request.post({
    url: '/publicbiz/work-order/submitResolution',
    data
  })
}

// 指派阿姨接口
export function assignAunt(data: any) {
  return request.post({
    url: '/publicbiz/work-order/assignAunt',
    data
  })
}
