# 资源中心-考题管理接口文档

## 1. 接口概述

### 1.1 基础信息

- **基础路径**: `/publicbiz/question`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求头**: Content-Type: application/json

### 1.2 统一响应格式

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

**响应字段说明:**

| 字段名    | 类型    | 说明                           |
| --------- | ------- | ------------------------------ |
| code      | Integer | 响应码，0表示成功，非0表示失败 |
| msg       | String  | 响应消息                       |
| data      | Object  | 响应数据                       |
| timestamp | Long    | 响应时间戳                     |

## 2. 考题管理接口

### 2.1 考题分页查询

**接口地址：** `GET /page`

**功能说明：** 分页查询考题列表，支持多条件筛选

**请求参数:**

| 参数名     | 类型    | 必填 | 说明                                             |
| ---------- | ------- | ---- | ------------------------------------------------ |
| pageNo     | Integer | 是   | 页码，从1开始                                    |
| pageSize   | Integer | 是   | 每页大小，最大100                                |
| level1Name | String  | 否   | 一级分类名称                                     |
| level2Name | String  | 否   | 二级分类名称                                     |
| level3Name | String  | 否   | 三级分类名称                                     |
| type       | String  | 否   | 题型：单选题、多选题、判断题、简答题等           |
| biz        | String  | 否   | 业务模块：家政业务、高校业务、培训业务、认证业务 |
| bizName    | String  | 否   | 业务模块名称                                     |
| keyword    | String  | 否   | 题干内容关键词搜索                               |

**响应字段:**

| 字段名             | 类型   | 说明         |
| ------------------ | ------ | ------------ |
| total              | Long   | 总记录数     |
| list               | Array  | 考题列表     |
| list[].id          | Long   | 考题ID       |
| list[].level1Name  | String | 一级分类名称 |
| list[].level2Name  | String | 二级分类名称 |
| list[].level3Name  | String | 三级分类名称 |
| list[].certName    | String | 认定点名称   |
| list[].title       | String | 题干内容     |
| list[].type        | String | 题型         |
| list[].biz         | String | 业务模块     |
| list[].bizName     | String | 业务模块名称 |
| list[].answer      | String | 参考答案     |
| list[].creator     | String | 创建人       |
| list[].creatorName | String | 创建人姓名   |
| list[].createTime  | String | 创建时间     |

**请求示例:**

```http
GET /publicbiz/question/page?pageNo=1&pageSize=10&level1Name=职业技能等级认定&type=单选题
```

**返回示例:**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "total": 15,
    "list": [
      {
        "id": 1,
        "level1Name": "职业技能等级认定",
        "level2Name": "家政服务类",
        "level3Name": "家政服务员",
        "certName": "职业道德基础",
        "title": "家政服务员职业道德的核心要求是什么？",
        "type": "单选题",
        "biz": "家政业务",
        "bizName": "家政服务业务",
        "answer": "A. 诚实守信、尊重客户 B. 只关注工作效率 C. 随意处理客户物品 D. 不需要保护客户隐私 正确答案：A",
        "creator": "张老师",
        "creatorName": "张明华",
        "createTime": "2024-01-15 10:30:00"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### 2.2 考题详情查询

**接口地址：** `GET /get/{id}`

**功能说明：** 根据ID查询考题详细信息，包含选项信息

**请求参数:**

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 考题ID |

**响应字段:**

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 考题ID |
| level1Name | String | 一级分类名称 |
| level1Code | String | 一级分类代码 |
| level2Name | String | 二级分类名称 |
| level2Code | String | 二级分类代码 |
| level3Name | String | 三级分类名称 |
| level3Code | String | 三级分类代码 |
| certName | String | 认定点名称 |
| certCode | String | 认定点代码 |
| title | String | 题干内容 |
| type | String | 题型 |
| answer | String | 参考答案 |
| biz | String | 业务模块 |
| difficulty | Integer | 难度等级：1-简单，2-中等，3-困难 |
| score | BigDecimal | 题目分值 |
| options | Array | 选项列表（所有题型都包含此字段） |
| options[].optionKey | String | 选项标识：A、B、C、D等 |
| options[].optionContent | String | 选项内容 |
| options[].isCorrect | Boolean | 是否正确答案 |
| options[].optionType | String | 选项类型：choice-选择项，matchLeft-匹配左列，matchRight-匹配右列 |
| options[].sortOrder | Integer | 排序序号 |

**请求示例：**

```http
GET /publicbiz/question/get/1
```

**返回示例:**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "level1Name": "职业技能等级认定",
    "level1Code": "ZY001",
    "level2Name": "家政服务类",
    "level2Code": "JZ001",
    "level3Name": "家政服务员",
    "level3Code": "JZFW001",
    "certName": "职业道德基础",
    "certCode": "KP001",
    "title": "家政服务员职业道德的核心要求是什么？",
    "type": "单选题",
    "answer": "A",
    "biz": "家政业务",
    "difficulty": 1,
    "score": 2.0,
    "options": [
      {
        "optionKey": "A",
        "optionContent": "诚实守信、尊重客户",
        "isCorrect": true,
        "optionType": "choice",
        "sortOrder": 1
      },
      {
        "optionKey": "B",
        "optionContent": "只关注工作效率",
        "isCorrect": false,
        "optionType": "choice",
        "sortOrder": 2
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### 2.3 新增考题

**接口地址：** `POST /add`

**功能说明：** 新增考题信息

**请求参数:**

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| level1Name | String | 是 | 一级分类名称 |
| level1Code | String | 是 | 一级分类代码 |
| level2Name | String | 是 | 二级分类名称 |
| level2Code | String | 是 | 二级分类代码 |
| level3Name | String | 是 | 三级分类名称 |
| level3Code | String | 是 | 三级分类代码 |
| certName | String | 是 | 认定点名称 |
| certCode | String | 是 | 认定点代码 |
| title | String | 是 | 题干内容 |
| type | String | 是 | 题型 |
| answer | String | 是 | 参考答案 |
| biz | String | 是 | 业务模块 |
| bizName | String | 否 | 业务模块名称 |
| difficulty | Integer | 否 | 难度等级，默认1 |
| score | BigDecimal | 否 | 题目分值，默认0 |
| options | Array | 否 | 选项列表（适用于所有题型） |
| options[].optionKey | String | 否 | 选项标识：A、B、C、D等 |
| options[].optionContent | String | 否 | 选项内容 |
| options[].isCorrect | Boolean | 否 | 是否正确答案 |
| options[].optionType | String | 否 | 选项类型：choice-选择项，matchLeft-匹配左列，matchRight-匹配右列 |
| options[].sortOrder | Integer | 否 | 排序序号 |

**请求示例：**

```json
POST /publicbiz/question/add
{
  "level1Name": "职业技能等级认定",
  "level1Code": "ZY001",
  "level2Name": "家政服务类",
  "level2Code": "JZ001",
  "level3Name": "家政服务员",
  "level3Code": "JZFW001",
  "certName": "职业道德基础",
  "certCode": "KP001",
  "title": "家政服务员职业道德的核心要求是什么？",
  "type": "单选题",
  "answer": "A",
  "biz": "家政业务",
  "bizName": "家政服务业务",
  "difficulty": 1,
  "score": 2.0,
  "options": [
    {
      "optionKey": "A",
      "optionContent": "诚实守信、尊重客户",
      "isCorrect": true,
      "optionType": "choice",
      "sortOrder": 1
    },
    {
      "optionKey": "B",
      "optionContent": "只关注工作效率",
      "isCorrect": false,
      "optionType": "choice",
      "sortOrder": 2
    }
  ]
}
```

**返回示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 123
  },
  "timestamp": 1640995200000
}
```

### 2.4 编辑考题

**接口地址：** `PUT /update`

**功能说明：** 编辑考题信息

**请求参数:**

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| id | Long | 是 | 考题ID |
| level1Name | String | 是 | 一级分类名称 |
| level1Code | String | 是 | 一级分类代码 |
| level2Name | String | 是 | 二级分类名称 |
| level2Code | String | 是 | 二级分类代码 |
| level3Name | String | 是 | 三级分类名称 |
| level3Code | String | 是 | 三级分类代码 |
| certName | String | 是 | 认定点名称 |
| certCode | String | 是 | 认定点代码 |
| title | String | 是 | 题干内容 |
| type | String | 是 | 题型 |
| answer | String | 是 | 参考答案 |
| biz | String | 是 | 业务模块 |
| bizName | String | 否 | 业务模块名称 |
| difficulty | Integer | 否 | 难度等级 |
| score | BigDecimal | 否 | 题目分值 |
| options | Array | 否 | 选项列表（适用于所有题型） |
| options[].optionKey | String | 否 | 选项标识：A、B、C、D等 |
| options[].optionContent | String | 否 | 选项内容 |
| options[].isCorrect | Boolean | 否 | 是否正确答案 |
| options[].optionType | String | 否 | 选项类型：choice-选择项，matchLeft-匹配左列，matchRight-匹配右列 |
| options[].sortOrder | Integer | 否 | 排序序号 |

**请求示例：**

```json
PUT /publicbiz/question/update
{
  "id": 123,
  "level1Name": "职业技能等级认定",
  "level1Code": "ZY001",
  "level2Name": "家政服务类",
  "level2Code": "JZ001",
  "level3Name": "家政服务员",
  "level3Code": "JZFW001",
  "certName": "职业道德基础",
  "certCode": "KP001",
  "title": "家政服务员职业道德的核心要求是什么？（修改版）",
  "type": "单选题",
  "answer": "A",
  "biz": "家政业务",
  "bizName": "家政服务业务",
  "difficulty": 2,
  "score": 3.0,
  "options": [
    {
      "optionKey": "A",
      "optionContent": "诚实守信、尊重客户",
      "isCorrect": true,
      "optionType": "choice",
      "sortOrder": 1
    },
    {
      "optionKey": "B",
      "optionContent": "只关注工作效率",
      "isCorrect": false,
      "optionType": "choice",
      "sortOrder": 2
    },
    {
      "optionKey": "C",
      "optionContent": "随意处理客户物品",
      "isCorrect": false,
      "optionType": "choice",
      "sortOrder": 3
    },
    {
      "optionKey": "D",
      "optionContent": "不需要保护客户隐私",
      "isCorrect": false,
      "optionType": "choice",
      "sortOrder": 4
    }
  ]
}
```

**返回示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```

### 2.5 删除考题

**接口地址：** `DELETE /delete/{id}`

**功能说明：** 删除考题（软删除）

**请求参数：**

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 考题ID |

**请求示例：**

```http
DELETE /publicbiz/question/delete/123
```

**返回示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```

### 2.6 考题统计报表

**接口地址：** `GET /statistics`

**功能说明：** 获取考题统计数据，包括题目总数、选择题数、判断题数、简答题数

**请求参数:**

| 参数名     | 类型   | 必填 | 说明         |
| ---------- | ------ | ---- | ------------ |
| biz        | String | 否   | 业务模块筛选 |
| level1Name | String | 否   | 一级分类筛选 |
| level2Name | String | 否   | 二级分类筛选 |
| level3Name | String | 否   | 三级分类筛选 |

**响应字段：**

| 字段名   | 类型    | 说明                  |
| -------- | ------- | --------------------- |
| total    | Integer | 题目总数              |
| single   | Integer | 选择题数（单选+多选） |
| judge    | Integer | 判断题数              |
| short    | Integer | 简答题数              |
| fill     | Integer | 填空题数              |
| material | Integer | 材料题数              |
| sort     | Integer | 排序题数              |
| match    | Integer | 匹配题数              |
| upload   | Integer | 文件上传题数          |

**请求示例：**

```http
GET /publicbiz/question/statistics?biz=家政业务
```

**返回示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "total": 15,
    "single": 6,
    "judge": 2,
    "short": 2,
    "fill": 1,
    "material": 1,
    "sort": 1,
    "match": 1,
    "upload": 1
  },
  "timestamp": 1640995200000
}
```

### 2.7 批量导入考题

**接口地址：** `POST /batch-import`

**功能说明：** 批量导入考题，支持Excel文件上传

**请求参数：**

| 参数名 | 类型          | 必填 | 说明      |
| ------ | ------------- | ---- | --------- |
| file   | MultipartFile | 是   | Excel文件 |

**请求示例：**

```http
POST /publicbiz/question/batch-import
Content-Type: multipart/form-data

file: [Excel文件]
```

**返回示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "total": 100,
    "success": 95,
    "failed": 5,
    "failedList": [
      {
        "row": 10,
        "reason": "题型格式错误"
      },
      {
        "row": 25,
        "reason": "必填字段缺失"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### 2.8 下载导入模板

**接口地址：** `GET /download-template`

**功能说明：** 下载考题导入Excel模板

**请求参数：** 无

**请求示例：**

```http
GET /publicbiz/question/download-template
```

**返回示例：** 直接返回Excel文件流

## 3. 考题分类管理接口

### 3.1 分类分页查询

**接口地址：** `GET /category/page`

**功能说明：** 分页查询考题分类列表

**请求参数：**

| 参数名   | 类型    | 必填 | 说明                             |
| -------- | ------- | ---- | -------------------------------- |
| pageNo   | Integer | 是   | 页码，从1开始                    |
| pageSize | Integer | 是   | 每页大小，最大100                |
| biz      | String  | 否   | 业务模块筛选                     |
| level    | Integer | 否   | 分类层级：1-一级，2-二级，3-三级 |
| parentId | Long    | 否   | 父级分类ID                       |

**响应字段：**

| 字段名             | 类型   | 说明         |
| ------------------ | ------ | ------------ |
| total              | Long   | 总记录数     |
| list               | Array  | 分类列表     |
| list[].id          | Long   | 分类ID       |
| list[].level1Name  | String | 一级分类名称 |
| list[].level1Code  | String | 一级分类代码 |
| list[].level2Name  | String | 二级分类名称 |
| list[].level2Code  | String | 二级分类代码 |
| list[].level3Name  | String | 三级分类名称 |
| list[].level3Code  | String | 三级分类代码 |
| list[].certName    | String | 认定点名称   |
| list[].certCode    | String | 认定点代码   |
| list[].biz         | String | 业务模块     |
| list[].bizName     | String | 业务模块名称 |
| list[].creator     | String | 创建人       |
| list[].creatorName | String | 创建人姓名   |
| list[].createTime  | String | 创建时间     |

**请求示例：**

```http
GET /publicbiz/question/category/page?pageNo=1&pageSize=10&biz=家政业务
```

**返回示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "total": 5,
    "list": [
      {
        "id": 1,
        "level1Name": "职业技能等级认定",
        "level1Code": "ZY001",
        "level2Name": "家政服务类",
        "level2Code": "JZ001",
        "level3Name": "家政服务员",
        "level3Code": "JZFW001",
        "certName": "家庭清洁技能",
        "certCode": "JTQJ001",
        "biz": "家政业务",
        "bizName": "家政服务业务",
        "creator": "王五",
        "creatorName": "王五老师",
        "createTime": "2024-01-17 10:30:00"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### 3.2 分类列表查询

**接口地址：** `GET /category/list`

**功能说明：** 查询分类列表（不分页），用于下拉选择

**请求参数:**

| 参数名   | 类型   | 必填 | 说明                              |
| -------- | ------ | ---- | --------------------------------- |
| biz      | String | 否   | 业务模块筛选                      |
| level    | Array  | 否   | 分类层级筛选，支持多个层级：1,2,3 |
| parentId | Long   | 否   | 父级分类ID                        |

**响应字段：**

| 字段名            | 类型    | 说明                             |
| ----------------- | ------- | -------------------------------- |
| list              | Array   | 分类列表                         |
| list[].id         | Long    | 分类ID                           |
| list[].level1Name | String  | 一级分类名称                     |
| list[].level1Code | String  | 一级分类代码                     |
| list[].level2Name | String  | 二级分类名称                     |
| list[].level2Code | String  | 二级分类代码                     |
| list[].level3Name | String  | 三级分类名称                     |
| list[].level3Code | String  | 三级分类代码                     |
| list[].certName   | String  | 认定点名称                       |
| list[].certCode   | String  | 认定点代码                       |
| list[].biz        | String  | 业务模块                         |
| list[].bizName    | String  | 业务模块名称                     |
| list[].level      | Integer | 分类层级：1-一级，2-二级，3-三级 |

**请求示例：**

```http
GET /publicbiz/question/category/list?biz=家政业务&level=1,2,3
```

**返回示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "level1Name": "职业技能等级认定",
        "level1Code": "ZY001",
        "level2Name": "",
        "level2Code": "",
        "level3Name": "",
        "level3Code": "",
        "certName": "",
        "certCode": "",
        "biz": "家政业务",
        "bizName": "家政服务业务",
        "level": 1
      },
      {
        "id": 2,
        "level1Name": "职业技能等级认定",
        "level1Code": "ZY001",
        "level2Name": "家政服务类",
        "level2Code": "JZ001",
        "level3Name": "",
        "level3Code": "",
        "certName": "",
        "certCode": "",
        "biz": "家政业务",
        "bizName": "家政服务业务",
        "level": 2
      },
      {
        "id": 3,
        "level1Name": "职业技能等级认定",
        "level1Code": "ZY001",
        "level2Name": "家政服务类",
        "level2Code": "JZ001",
        "level3Name": "家政服务员",
        "level3Code": "JZFW001",
        "certName": "职业道德基础",
        "certCode": "KP001",
        "biz": "家政业务",
        "bizName": "家政服务业务",
        "level": 3
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### 3.3 新增分类

**接口地址：** `POST /category/add`

**功能说明：** 新增考题分类

**请求参数:**

| 参数名     | 类型    | 必填 | 说明                             |
| ---------- | ------- | ---- | -------------------------------- |
| level1Name | String  | 是   | 一级分类名称                     |
| level1Code | String  | 是   | 一级分类代码                     |
| level2Name | String  | 否   | 二级分类名称                     |
| level2Code | String  | 否   | 二级分类代码                     |
| level3Name | String  | 否   | 三级分类名称                     |
| level3Code | String  | 否   | 三级分类代码                     |
| certName   | String  | 否   | 认定点名称                       |
| certCode   | String  | 否   | 认定点代码                       |
| biz        | String  | 是   | 业务模块                         |
| bizName    | String  | 否   | 业务模块名称                     |
| parentId   | Long    | 否   | 父级分类ID                       |
| level      | Integer | 是   | 分类层级：1-一级，2-二级，3-三级 |

**请求示例：**

```json
POST /publicbiz/question/category/add
{
  "level1Name": "职业技能等级认定",
  "level1Code": "ZY001",
  "level2Name": "家政服务类",
  "level2Code": "JZ001",
  "level3Name": "家政服务员",
  "level3Code": "JZFW001",
  "certName": "家庭清洁技能",
  "certCode": "JTQJ001",
  "biz": "家政业务",
  "bizName": "家政服务业务",
  "parentId": 0,
  "level": 3
}
```

**返回示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 123
  },
  "timestamp": 1640995200000
}
```

### 3.4 编辑分类

**接口地址：** `PUT /category/update`

**功能说明：** 编辑考题分类

**请求参数:**

| 参数名     | 类型   | 必填 | 说明         |
| ---------- | ------ | ---- | ------------ |
| id         | Long   | 是   | 分类ID       |
| level1Name | String | 是   | 一级分类名称 |
| level1Code | String | 是   | 一级分类代码 |
| level2Name | String | 否   | 二级分类名称 |
| level2Code | String | 否   | 二级分类代码 |
| level3Name | String | 否   | 三级分类名称 |
| level3Code | String | 否   | 三级分类代码 |
| certName   | String | 否   | 认定点名称   |
| certCode   | String | 否   | 认定点代码   |
| biz        | String | 是   | 业务模块     |
| bizName    | String | 否   | 业务模块名称 |

**请求示例：**

```json
PUT /publicbiz/question/category/update
{
  "id": 123,
  "level1Name": "职业技能等级认定",
  "level1Code": "ZY001",
  "level2Name": "家政服务类",
  "level2Code": "JZ001",
  "level3Name": "家政服务员（修改版）",
  "level3Code": "JZFW001",
  "certName": "家庭清洁技能",
  "certCode": "JTQJ001",
  "biz": "家政业务",
  "bizName": "家政服务业务"
}
```

**返回示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```

### 3.5 删除分类

**接口地址：** `DELETE /category/delete/{id}`

**功能说明：** 删除考题分类（软删除）

**请求参数：**

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 分类ID |

**请求示例：**

```http
DELETE /publicbiz/question/category/delete/123
```

**返回示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```

## 4. 数据字典

### 4.1 题型枚举

| 值         | 说明       | 答案格式示例           |
| ---------- | ---------- | ---------------------- |
| 单选题     | 单项选择题 | A                      |
| 多选题     | 多项选择题 | A,C,D                  |
| 判断题     | 判断题     | 正确 或 错误           |
| 简答题     | 简答题     | 详细文字描述           |
| 填空题     | 填空题     | 答案1\|答案2\|答案3    |
| 材料题     | 材料题     | 详细文字描述           |
| 排序题     | 排序题     | C,A,B,D                |
| 匹配题     | 匹配题     | 1-A,2-B,3-C            |
| 文件上传题 | 文件上传题 | 评分标准和文件要求说明 |

### 4.2 业务模块枚举

| 值       | 说明             |
| -------- | ---------------- |
| 家政业务 | 家政服务相关业务 |
| 高校业务 | 高等院校相关业务 |
| 培训业务 | 职业培训相关业务 |
| 认证业务 | 职业认证相关业务 |

### 4.3 难度等级枚举

| 值  | 说明 |
| --- | ---- |
| 1   | 简单 |
| 2   | 中等 |
| 3   | 困难 |

### 4.4 分类层级枚举

| 值  | 说明     |
| --- | -------- |
| 1   | 一级分类 |
| 2   | 二级分类 |
| 3   | 三级分类 |

## 5. 错误码说明

### 5.1 通用错误码

| 错误码 | 错误信息       | 说明                     |
| ------ | -------------- | ------------------------ |
| 0      | 操作成功       | 请求处理成功             |
| 400    | 请求参数错误   | 请求参数格式或内容错误   |
| 401    | 未授权访问     | 用户未登录或token无效    |
| 403    | 权限不足       | 用户无权限访问该资源     |
| 404    | 资源不存在     | 请求的资源不存在         |
| 500    | 服务器内部错误 | 服务器处理请求时发生错误 |

### 5.2 业务错误码

| 错误码 | 错误信息                 | 说明                               |
| ------ | ------------------------ | ---------------------------------- |
| 10001  | 考题不存在               | 指定的考题ID不存在                 |
| 10002  | 考题已被删除             | 考题已被软删除，无法进行操作       |
| 10003  | 分类不存在               | 指定的分类ID不存在                 |
| 10004  | 分类已被删除             | 分类已被软删除，无法进行操作       |
| 10005  | 分类下存在考题，无法删除 | 删除分类前需先删除分类下的所有考题 |
| 10006  | 题型不支持               | 题型参数值不在支持的枚举范围内     |
| 10007  | 文件格式不正确           | 上传的文件不是有效的Excel格式      |
| 10008  | 文件大小超限             | 上传文件大小超过10MB限制           |
| 10009  | 导入数据格式错误         | Excel中的数据格式不符合要求        |
| 10010  | 分类代码重复             | 分类代码已存在，请使用唯一代码     |

## 6. 接口调用示例

### 6.1 完整的考题管理流程

```javascript
// 1. 查询考题统计
const statistics = await fetch('/publicbiz/question/statistics?biz=家政业务')

// 2. 分页查询考题列表
const questionList = await fetch('/publicbiz/question/page?pageNo=1&pageSize=10&biz=家政业务')

// 3. 新增考题
const newQuestion = await fetch('/publicbiz/question/add', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    level1Name: '职业技能等级认定',
    level1Code: 'ZY001',
    level2Name: '家政服务类',
    level2Code: 'JZ001',
    level3Name: '家政服务员',
    level3Code: 'JZFW001',
    certName: '职业道德基础',
    certCode: 'KP001',
    title: '家政服务员职业道德的核心要求是什么？',
    type: '单选题',
    answer: 'A',
    biz: '家政业务',
    options: [
      {
        optionKey: 'A',
        optionContent: '诚实守信、尊重客户',
        isCorrect: true,
        optionType: 'choice',
        sortOrder: 1
      },
      {
        optionKey: 'B',
        optionContent: '只关注工作效率',
        isCorrect: false,
        optionType: 'choice',
        sortOrder: 2
      }
    ]
  })
})

// 4. 查询考题详情
const questionDetail = await fetch('/publicbiz/question/get/123')

// 5. 编辑考题
const updateQuestion = await fetch('/publicbiz/question/update', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    id: 123,
    title: '修改后的题目内容',
    options: [
      // 更新后的选项列表
    ]
    // ... 其他字段
  })
})
```

### 6.2 分类管理流程

```javascript
// 1. 查询分类列表
const categoryList = await fetch('/publicbiz/question/category/list?biz=家政业务')

// 2. 新增分类
const newCategory = await fetch('/publicbiz/question/category/add', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    level1Name: '职业技能等级认定',
    level1Code: 'ZY001',
    biz: '家政业务',
    level: 1
  })
})
```

## 7. 接口调用说明

### 7.1 认证方式

所有接口都需要在请求头中携带认证信息：

```http
Authorization: Bearer {token}
```

### 7.2 请求限制

- 单次请求超时时间：30秒
- 分页查询最大页面大小：100
- 文件上传最大大小：10MB

### 7.3 注意事项

1. 所有时间字段格式为：`yyyy-MM-dd HH:mm:ss`
2. 文件URL需要是完整的可访问地址
3. 删除操作为软删除，数据不会物理删除
4. 分页参数pageNo从1开始计数

## 8. 注意事项

1. **接口调用频率限制**：建议每秒不超过100次请求
2. **文件上传限制**：Excel文件大小不超过10MB，支持.xlsx和.xls格式
3. **数据一致性**：删除分类前需确保该分类下无考题
4. **字段长度限制**：题干内容建议不超过1000字符
5. **编码格式**：所有接口均使用UTF-8编码
6. **时间格式**：统一使用 `yyyy-MM-dd HH:mm:ss` 格式
7. **分页参数**：pageNo从1开始，pageSize最大值为100

---

**文档维护：** 如有接口变更，请及时更新此文档 **技术支持：** 如有疑问，请联系开发团队
