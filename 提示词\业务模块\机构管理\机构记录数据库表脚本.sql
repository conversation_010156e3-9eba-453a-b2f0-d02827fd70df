-- 机构记录数据库表脚本
-- 创建时间：2024-01-15
-- 说明：包含机构激励/处罚记录、沟通日志、附件等相关表


-- 1. 机构记录表
DROP TABLE IF EXISTS `publicbiz_agency_record`;
CREATE TABLE `publicbiz_agency_record` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `record_id` VARCHAR(32) NOT NULL COMMENT '记录编号，如：REC001',
  `agency_id` BIGINT NOT NULL COMMENT '机构ID',
  `record_type` VARCHAR(50) NOT NULL COMMENT '记录类型：incentive-激励记录，penalty-处罚记录，communication-沟通日志',
  `record_date` DATE COMMENT '记录日期',
  `title` VARCHAR(200) COMMENT '记录标题',
  `description` TEXT COMMENT '记录详细描述',
  `credit_impact` INT DEFAULT 0 COMMENT '信用分影响，正数表示加分，负数表示扣分',
  `amount_impact` DECIMAL(10,2) DEFAULT 0.00 COMMENT '金额影响，正数表示奖励，负数表示罚款',
  `other_impact` VARCHAR(500) COMMENT '其他影响，如暂停接单、降级等',
  `related_info` VARCHAR(500) COMMENT '关联信息，如订单号、阿姨编号等',
  `status` VARCHAR(50) NOT NULL DEFAULT 'pending' COMMENT '处理状态：pending-待处理，processing-处理中，completed-已完成，cancelled-已取消，effective-已生效',
  `follow_up_date` DATE COMMENT '跟进日期',
  `follow_up_item` VARCHAR(500) COMMENT '跟进事项',
  `remarks` TEXT COMMENT '备注说明',
  `effective_time` DATETIME COMMENT '生效时间',
  `recorder_name` VARCHAR(64) COMMENT '记录人姓名',
  `recorder_id` BIGINT COMMENT '记录人ID',
  `record_time` DATETIME COMMENT '记录时间',
  ------沟通日志-------
  `communication_type` VARCHAR(50) COMMENT '沟通方式：call-通话记录，message-消息记录，email-邮件记录（仅当记录类型为沟通日志时有效）',
  `communication_title` VARCHAR(200) COMMENT '沟通标题（仅当记录类型为沟通日志时有效）',
  `communication_content` TEXT COMMENT '沟通内容（仅当记录类型为沟通日志时有效）',
  `participants` VARCHAR(500) COMMENT '参与人，多个参与人用逗号分隔（仅当记录类型为沟通日志时有效）',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_id` (`record_id`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_record_type` (`record_type`),
  KEY `idx_record_date` (`record_date`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='机构记录表';

-- 3. 机构记录附件表
DROP TABLE IF EXISTS `publicbiz_agency_record_attachment`;
CREATE TABLE `publicbiz_agency_record_attachment` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `record_id` BIGINT NOT NULL COMMENT '关联记录ID',
  `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
  `file_type` VARCHAR(50) COMMENT '文件类型，如：pdf、xls、doc、mp3、jpg、png等',
  `file_size` BIGINT COMMENT '文件大小（字节）',
  `file_url` VARCHAR(500) COMMENT '文件访问URL',
  `upload_time` DATETIME COMMENT '上传时间',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_upload_time` (`upload_time`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='机构记录附件表';

-- 4. 机构记录跟进表
DROP TABLE IF EXISTS `publicbiz_agency_record_followup`;
CREATE TABLE `publicbiz_agency_record_followup` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `record_id` BIGINT NOT NULL COMMENT '关联记录ID',
  `title` VARCHAR(200) NOT NULL COMMENT '跟进标题',
  `description` TEXT COMMENT '跟进描述',
  `follow_up_date` DATE COMMENT '跟进日期',
  `operator_id` BIGINT NOT NULL COMMENT '操作人ID',
  `operator_name` VARCHAR(64) NOT NULL COMMENT '操作人姓名',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_follow_up_date` (`follow_up_date`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='机构记录跟进表';

