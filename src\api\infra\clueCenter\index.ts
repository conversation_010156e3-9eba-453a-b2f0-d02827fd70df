import request from '@/config/axios'

export interface AddClueParams {
  customerName: string
  customerPhone: string
  leadSource: string
  businessModule: string
  leadStatus: string
  currentOwner: string
  currentOwnerName?: string // 当前跟进人姓名
  remark?: string
}

export interface AddClueResult {
  id: string
}

export interface ClueListPageParams {
  page?: number
  pageSize?: number
  leadSource?: string
  leadStatus?: string
  businessModule?: string
  keyword?: string
}

export interface ClueListItem {
  id: string
  customerName: string
  customerPhone: string
  leadSource: string
  leadSourceDesc: string
  businessModule: string
  businessModuleDesc: string
  leadStatus: string
  leadStatusDesc: string
  createMethod: string
  createMethodDesc: string
  creator: string
  creatorName: string // 创建人姓名
  currentOwner: string
  currentOwnerName: string // 当前跟进人姓名
  createTime: string
  createTimeFormatted: string
  remark?: string
}

export interface ClueListPageResult {
  total: number
  list: ClueListItem[]
}

// 跟进记录相关接口类型定义
export interface FollowUpRecord {
  id: string
  leadId: string
  followUpContent: string
  creator: string
  creatorName: string // 创建人姓名
  createTime: string
  createTimeFormatted: string
}

export interface FollowUpListParams {
  leadId: string
}

export interface FollowUpListResult extends Array<FollowUpRecord> {}

export interface AddFollowUpParams {
  leadId: string
  followUpContent: string
  creatorName?: string // 创建人姓名
}

export interface AddFollowUpResult {
  id: string
}

// 编辑线索接口类型定义
export interface UpdateClueParams {
  id: string
  customerName: string
  customerPhone: string
  leadSource: string
  businessModule: string
  leadStatus: string
  currentOwner: string
  currentOwnerName?: string // 当前跟进人姓名
  createMethod: string
  remark?: string
}

export interface UpdateClueResult {
  id: string
}

// 删除线索接口类型定义
export interface DeleteClueParams {
  id: string
}

export interface DeleteClueResult {
  id: string
}

// 分配线索接口类型定义
export interface AssignClueParams {
  id: string
  dept1: string
  dept2: string
  dept3: string
  userId: number
  userName: string
}

export interface AssignClueResult {
  id: string
}

export const ClueCenterApi = {
  // 新增线索
  addClue: async (data: AddClueParams): Promise<AddClueResult> => {
    return await request.post({ url: '/publicbiz/leads/create', data })
  },

  // 分页查询线索列表
  getClueListPage: async (params: ClueListPageParams): Promise<ClueListPageResult> => {
    return await request.get({ url: '/publicbiz/leads/page', params })
  },

  // 编辑线索
  updateClue: async (data: UpdateClueParams): Promise<UpdateClueResult> => {
    return await request.put({ url: '/publicbiz/leads/update', data })
  },

  // 删除线索
  deleteClue: async (data: DeleteClueParams): Promise<DeleteClueResult> => {
    return await request.delete({ url: `/publicbiz/leads/delete?id=${data.id}` })
  },

  // 分配线索
  assignClue: async (data: AssignClueParams): Promise<AssignClueResult> => {
    return await request.post({ url: '/publicbiz/leads/assign', data })
  },

  // 查询线索跟进记录
  getFollowUpList: async (params: FollowUpListParams): Promise<FollowUpListResult> => {
    return await request.get({ url: '/publicbiz/leads/follow-up/list-by-lead-id', params })
  },

  // 新增跟进记录
  addFollowUp: async (data: AddFollowUpParams): Promise<AddFollowUpResult> => {
    return await request.post({ url: '/publicbiz/leads/follow-up/create', data })
  }
}
