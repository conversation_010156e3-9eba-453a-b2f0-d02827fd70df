## 新增阿姨

**接口地址**:`/publicbiz/employment/practitioner/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "name": "",
  "phone": "",
  "idCard": "",
  "hometown": "",
  "age": 0,
  "gender": "",
  "avatar": "",
  "serviceType": "",
  "experienceYears": 0,
  "platformStatus": "",
  "rating": 0,
  "agencyId": 0,
  "agencyName": "",
  "status": "",
  "currentStatus": "",
  "currentOrderId": "",
  "totalOrders": 0,
  "totalIncome": 0,
  "customerSatisfaction": 0,
  "qualifications": [
    {
      "id": 0,
      "fileType": "",
      "fileName": "",
      "fileUrl": "",
      "fileSize": 0,
      "fileExtension": "",
      "sortOrder": 0,
      "status": 0
    }
  ]
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| practitionerSaveReqVO | 就业服务-阿姨新增 Request VO | body | true | PractitionerSaveReqVO | PractitionerSaveReqVO |
| &emsp;&emsp;name | 阿姨姓名 |  | true | string |  |
| &emsp;&emsp;phone | 手机号 |  | true | string |  |
| &emsp;&emsp;idCard | 身份证号 |  | true | string |  |
| &emsp;&emsp;hometown | 籍贯 |  | false | string |  |
| &emsp;&emsp;age | 年龄 |  | false | integer(int32) |  |
| &emsp;&emsp;gender | 性别 |  | false | string |  |
| &emsp;&emsp;avatar | 头像URL |  | false | string |  |
| &emsp;&emsp;serviceType | 主要服务类型 |  | true | string |  |
| &emsp;&emsp;experienceYears | 从业年限 |  | true | integer(int32) |  |
| &emsp;&emsp;platformStatus | 平台状态 |  | false | string |  |
| &emsp;&emsp;rating | 评级 |  | true | number |  |
| &emsp;&emsp;agencyId | 所属机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;agencyName | 所属机构名称 |  | false | string |  |
| &emsp;&emsp;status | 状态 |  | false | string |  |
| &emsp;&emsp;currentStatus | 当前状态 |  | false | string |  |
| &emsp;&emsp;currentOrderId | 当前服务订单ID |  | false | string |  |
| &emsp;&emsp;totalOrders | 累计服务单数 |  | false | integer(int32) |  |
| &emsp;&emsp;totalIncome | 累计收入 |  | false | number |  |
| &emsp;&emsp;customerSatisfaction | 客户满意度评分 |  | false | number |  |
| &emsp;&emsp;qualifications | 资质文件列表 |  | false | array | PractitionerQualificationSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;id | 资质文件ID（更新时使用） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;fileType | 文件类型 |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;fileName | 文件名 |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;fileUrl | 文件URL |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;fileSize | 文件大小 |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;fileExtension | 文件扩展名 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 |  | false | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 |  | false | integer(int32) |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```

## 获取阿姨分页

**接口地址**:`/publicbiz/employment/practitioner/page`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| keyword        | 阿姨姓名或手机号关键词 | query    | false    | string   |        |
| serviceType    | 服务类型               | query    | false    | string   |        |
| platformStatus | 平台状态               | query    | false    | string   |        |
| rating         | 评级                   | query    | false    | string   |        |
| agencyId       | 机构ID                 | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                                   |
| ------ | ---- | ---------------------------------------- |
| 200    | OK   | CommonResultPageResultPractitionerRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultPractitionerRespVO | PageResultPractitionerRespVO |
| &emsp;&emsp;list | 数据 | array | PractitionerRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 阿姨ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;name | 阿姨姓名 | string |  |
| &emsp;&emsp;&emsp;&emsp;phone | 手机号 | string |  |
| &emsp;&emsp;&emsp;&emsp;idCard | 身份证号 | string |  |
| &emsp;&emsp;&emsp;&emsp;hometown | 籍贯 | string |  |
| &emsp;&emsp;&emsp;&emsp;age | 年龄 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;gender | 性别 | string |  |
| &emsp;&emsp;&emsp;&emsp;avatar | 头像URL | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceType | 主要服务类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;experienceYears | 从业年限 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;platformStatus | 平台状态 | string |  |
| &emsp;&emsp;&emsp;&emsp;rating | 评级 | number |  |
| &emsp;&emsp;&emsp;&emsp;agencyId | 所属机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;agencyName | 所属机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 | string |  |
| &emsp;&emsp;&emsp;&emsp;currentStatus | 当前状态 | string |  |
| &emsp;&emsp;&emsp;&emsp;currentOrderId | 当前服务订单ID | string |  |
| &emsp;&emsp;&emsp;&emsp;totalOrders | 累计服务单数 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;totalIncome | 累计收入 | number |  |
| &emsp;&emsp;&emsp;&emsp;customerSatisfaction | 客户满意度评分 | number |  |
| &emsp;&emsp;&emsp;&emsp;qualifications | 资质文件列表 | array | PractitionerQualificationRespVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id | 资质文件ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;practitionerId | 阿姨ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileType | 文件类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileName | 文件名 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileUrl | 文件URL | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileSize | 文件大小 | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileExtension | 文件扩展名 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder | 排序 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;status | 状态 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;recentServiceRecords | 最近服务记录 | array | PractitionerServiceRecordRespVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id | 服务记录ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;practitionerId | 阿姨ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;orderId | 订单ID | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;customerId | 客户ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;customerName | 客户姓名 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;serviceType | 服务类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;serviceStartTime | 服务开始时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;serviceEndTime | 服务结束时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;serviceDuration | 服务时长 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;serviceAddress | 服务地址 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;serviceAmount | 服务金额 | number |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;practitionerIncome | 阿姨收入 | number |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;platformIncome | 平台收入 | number |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;orderStatus | 订单状态 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;customerRating | 客户评分 | number |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;customerComment | 客户评价 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;remark | 备注 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;ratingRecords | 评级历史记录 | array | PractitionerRatingRecordRespVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id | 评级记录ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;practitionerId | 阿姨ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;ratingType | 评级类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;oldRating | 原评级 | number |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;newRating | 新评级 | number |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;ratingChange | 评级变化 | number |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;ratingReason | 评级原因 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;evaluatorId | 评价人ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;evaluatorName | 评价人姓名 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;evaluatorType | 评价人类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;relatedOrderId | 关联订单ID | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;remark | 备注 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 0,
				"name": "",
				"phone": "",
				"idCard": "",
				"hometown": "",
				"age": 0,
				"gender": "",
				"avatar": "",
				"serviceType": "",
				"experienceYears": 0,
				"platformStatus": "",
				"rating": 0,
				"agencyId": 0,
				"agencyName": "",
				"status": "",
				"currentStatus": "",
				"currentOrderId": "",
				"totalOrders": 0,
				"totalIncome": 0,
				"customerSatisfaction": 0,
				"qualifications": [
					{
						"id": 0,
						"practitionerId": 0,
						"fileType": "",
						"fileName": "",
						"fileUrl": "",
						"fileSize": 0,
						"fileExtension": "",
						"sortOrder": 0,
						"status": 0,
						"createTime": "",
						"updateTime": ""
					}
				],
				"recentServiceRecords": [
					{
						"id": 0,
						"practitionerId": 0,
						"orderId": "",
						"customerId": 0,
						"customerName": "",
						"serviceType": "",
						"serviceStartTime": "",
						"serviceEndTime": "",
						"serviceDuration": "",
						"serviceAddress": "",
						"serviceAmount": 0,
						"practitionerIncome": 0,
						"platformIncome": 0,
						"orderStatus": "",
						"customerRating": 0,
						"customerComment": "",
						"remark": "",
						"createTime": "",
						"updateTime": ""
					}
				],
				"ratingRecords": [
					{
						"id": 0,
						"practitionerId": 0,
						"ratingType": "",
						"oldRating": 0,
						"newRating": 0,
						"ratingChange": 0,
						"ratingReason": "",
						"evaluatorId": 0,
						"evaluatorName": "",
						"evaluatorType": "",
						"relatedOrderId": "",
						"remark": "",
						"createTime": "",
						"updateTime": ""
					}
				],
				"createTime": "",
				"updateTime": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 更新阿姨

**接口地址**:`/admin-api/publicbiz/employment/practitioner/update`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0,
  "name": "",
  "phone": "",
  "idCard": "",
  "hometown": "",
  "age": 0,
  "gender": "",
  "avatar": "",
  "serviceType": "",
  "experienceYears": 0,
  "platformStatus": "",
  "rating": 0,
  "agencyId": 0,
  "agencyName": "",
  "status": "",
  "currentStatus": "",
  "currentOrderId": "",
  "totalOrders": 0,
  "totalIncome": 0,
  "customerSatisfaction": 0,
  "qualifications": [
    {
      "id": 0,
      "fileType": "",
      "fileName": "",
      "fileUrl": "",
      "fileSize": 0,
      "fileExtension": "",
      "sortOrder": 0,
      "status": 0
    }
  ]
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| practitionerUpdateReqVO | 就业服务-阿姨更新 Request VO | body | true | PractitionerUpdateReqVO | PractitionerUpdateReqVO |
| &emsp;&emsp;id | 阿姨ID |  | true | integer(int64) |  |
| &emsp;&emsp;name | 阿姨姓名 |  | true | string |  |
| &emsp;&emsp;phone | 手机号 |  | true | string |  |
| &emsp;&emsp;idCard | 身份证号 |  | true | string |  |
| &emsp;&emsp;hometown | 籍贯 |  | false | string |  |
| &emsp;&emsp;age | 年龄 |  | false | integer(int32) |  |
| &emsp;&emsp;gender | 性别 |  | false | string |  |
| &emsp;&emsp;avatar | 头像URL |  | false | string |  |
| &emsp;&emsp;serviceType | 主要服务类型 |  | true | string |  |
| &emsp;&emsp;experienceYears | 从业年限 |  | true | integer(int32) |  |
| &emsp;&emsp;platformStatus | 平台状态 |  | false | string |  |
| &emsp;&emsp;rating | 评级 |  | true | number |  |
| &emsp;&emsp;agencyId | 所属机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;agencyName | 所属机构名称 |  | false | string |  |
| &emsp;&emsp;status | 状态 |  | false | string |  |
| &emsp;&emsp;currentStatus | 当前状态 |  | false | string |  |
| &emsp;&emsp;currentOrderId | 当前服务订单ID |  | false | string |  |
| &emsp;&emsp;totalOrders | 累计服务单数 |  | false | integer(int32) |  |
| &emsp;&emsp;totalIncome | 累计收入 |  | false | number |  |
| &emsp;&emsp;customerSatisfaction | 客户满意度评分 |  | false | number |  |
| &emsp;&emsp;qualifications | 资质文件列表 |  | false | array | PractitionerQualificationSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;id | 资质文件ID（更新时使用） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;fileType | 文件类型 |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;fileName | 文件名 |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;fileUrl | 文件URL |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;fileSize | 文件大小 |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;fileExtension | 文件扩展名 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 |  | false | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 |  | false | integer(int32) |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 导出阿姨列表 Excel

**接口地址**:`/publicbiz/employment/practitioner/export-excel`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| keyword        | 阿姨姓名或手机号关键词 | query    | false    | string   |        |
| serviceType    | 服务类型               | query    | false    | string   |        |
| platformStatus | 平台状态               | query    | false    | string   |        |
| rating         | 评级                   | query    | false    | string   |        |
| agencyId       | 机构ID                 | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema |
| ------ | ---- | ------ |
| 200    | OK   |        |

**响应参数**:

暂无

**响应示例**:

```javascript

```

## 更新阿姨平台状态

**接口地址**:`/publicbiz/employment/practitioner/{id}/status`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0,
  "platformStatus": "",
  "reason": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| id |  | path | true | integer(int64) |  |
| practitionerStatusUpdateReqVO | 就业服务-阿姨状态更新 Request VO | body | true | PractitionerStatusUpdateReqVO | PractitionerStatusUpdateReqVO |
| &emsp;&emsp;id | 阿姨ID |  | false | integer(int64) |  |
| &emsp;&emsp;platformStatus | 目标状态 |  | true | string |  |
| &emsp;&emsp;reason | 解约原因 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 更新阿姨评级

**接口地址**:`/publicbiz/employment/practitioner/{id}/rating`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0,
  "newRating": 0,
  "ratingReason": "",
  "ratingType": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| id |  | path | true | integer(int64) |  |
| practitionerRatingUpdateReqVO | 就业服务-阿姨评级更新 Request VO | body | true | PractitionerRatingUpdateReqVO | PractitionerRatingUpdateReqVO |
| &emsp;&emsp;id | 阿姨ID |  | false | integer(int64) |  |
| &emsp;&emsp;newRating | 新评级 |  | true | number |  |
| &emsp;&emsp;ratingReason | 评级原因 |  | false | string |  |
| &emsp;&emsp;ratingType | 评级类型 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 删除阿姨（软删除）

**接口地址**:`/publicbiz/employment/practitioner/{id}`

**请求方式**:`DELETE`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 获取阿姨详情

**接口地址**:`/publicbiz/employment/practitioner/{id}`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema                         |
| ------ | ---- | ------------------------------ |
| 200    | OK   | CommonResultPractitionerRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PractitionerRespVO | PractitionerRespVO |
| &emsp;&emsp;id | 阿姨ID | integer(int64) |  |
| &emsp;&emsp;name | 阿姨姓名 | string |  |
| &emsp;&emsp;phone | 手机号 | string |  |
| &emsp;&emsp;idCard | 身份证号 | string |  |
| &emsp;&emsp;hometown | 籍贯 | string |  |
| &emsp;&emsp;age | 年龄 | integer(int32) |  |
| &emsp;&emsp;gender | 性别 | string |  |
| &emsp;&emsp;avatar | 头像URL | string |  |
| &emsp;&emsp;serviceType | 主要服务类型 | string |  |
| &emsp;&emsp;experienceYears | 从业年限 | integer(int32) |  |
| &emsp;&emsp;platformStatus | 平台状态 | string |  |
| &emsp;&emsp;rating | 评级 | number |  |
| &emsp;&emsp;agencyId | 所属机构ID | integer(int64) |  |
| &emsp;&emsp;agencyName | 所属机构名称 | string |  |
| &emsp;&emsp;status | 状态 | string |  |
| &emsp;&emsp;currentStatus | 当前状态 | string |  |
| &emsp;&emsp;currentOrderId | 当前服务订单ID | string |  |
| &emsp;&emsp;totalOrders | 累计服务单数 | integer(int32) |  |
| &emsp;&emsp;totalIncome | 累计收入 | number |  |
| &emsp;&emsp;customerSatisfaction | 客户满意度评分 | number |  |
| &emsp;&emsp;qualifications | 资质文件列表 | array | PractitionerQualificationRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 资质文件ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;practitionerId | 阿姨ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;fileType | 文件类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;fileName | 文件名 | string |  |
| &emsp;&emsp;&emsp;&emsp;fileUrl | 文件URL | string |  |
| &emsp;&emsp;&emsp;&emsp;fileSize | 文件大小 | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;fileExtension | 文件扩展名 | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;recentServiceRecords | 最近服务记录 | array | PractitionerServiceRecordRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 服务记录ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;practitionerId | 阿姨ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;orderId | 订单ID | string |  |
| &emsp;&emsp;&emsp;&emsp;customerId | 客户ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;customerName | 客户姓名 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceType | 服务类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceStartTime | 服务开始时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;serviceEndTime | 服务结束时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;serviceDuration | 服务时长 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceAddress | 服务地址 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceAmount | 服务金额 | number |  |
| &emsp;&emsp;&emsp;&emsp;practitionerIncome | 阿姨收入 | number |  |
| &emsp;&emsp;&emsp;&emsp;platformIncome | 平台收入 | number |  |
| &emsp;&emsp;&emsp;&emsp;orderStatus | 订单状态 | string |  |
| &emsp;&emsp;&emsp;&emsp;customerRating | 客户评分 | number |  |
| &emsp;&emsp;&emsp;&emsp;customerComment | 客户评价 | string |  |
| &emsp;&emsp;&emsp;&emsp;remark | 备注 | string |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;ratingRecords | 评级历史记录 | array | PractitionerRatingRecordRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 评级记录ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;practitionerId | 阿姨ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;ratingType | 评级类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;oldRating | 原评级 | number |  |
| &emsp;&emsp;&emsp;&emsp;newRating | 新评级 | number |  |
| &emsp;&emsp;&emsp;&emsp;ratingChange | 评级变化 | number |  |
| &emsp;&emsp;&emsp;&emsp;ratingReason | 评级原因 | string |  |
| &emsp;&emsp;&emsp;&emsp;evaluatorId | 评价人ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;evaluatorName | 评价人姓名 | string |  |
| &emsp;&emsp;&emsp;&emsp;evaluatorType | 评价人类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;relatedOrderId | 关联订单ID | string |  |
| &emsp;&emsp;&emsp;&emsp;remark | 备注 | string |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"id": 0,
		"name": "",
		"phone": "",
		"idCard": "",
		"hometown": "",
		"age": 0,
		"gender": "",
		"avatar": "",
		"serviceType": "",
		"experienceYears": 0,
		"platformStatus": "",
		"rating": 0,
		"agencyId": 0,
		"agencyName": "",
		"status": "",
		"currentStatus": "",
		"currentOrderId": "",
		"totalOrders": 0,
		"totalIncome": 0,
		"customerSatisfaction": 0,
		"qualifications": [
			{
				"id": 0,
				"practitionerId": 0,
				"fileType": "",
				"fileName": "",
				"fileUrl": "",
				"fileSize": 0,
				"fileExtension": "",
				"sortOrder": 0,
				"status": 0,
				"createTime": "",
				"updateTime": ""
			}
		],
		"recentServiceRecords": [
			{
				"id": 0,
				"practitionerId": 0,
				"orderId": "",
				"customerId": 0,
				"customerName": "",
				"serviceType": "",
				"serviceStartTime": "",
				"serviceEndTime": "",
				"serviceDuration": "",
				"serviceAddress": "",
				"serviceAmount": 0,
				"practitionerIncome": 0,
				"platformIncome": 0,
				"orderStatus": "",
				"customerRating": 0,
				"customerComment": "",
				"remark": "",
				"createTime": "",
				"updateTime": ""
			}
		],
		"ratingRecords": [
			{
				"id": 0,
				"practitionerId": 0,
				"ratingType": "",
				"oldRating": 0,
				"newRating": 0,
				"ratingChange": 0,
				"ratingReason": "",
				"evaluatorId": 0,
				"evaluatorName": "",
				"evaluatorType": "",
				"relatedOrderId": "",
				"remark": "",
				"createTime": "",
				"updateTime": ""
			}
		],
		"createTime": "",
		"updateTime": ""
	},
	"msg": ""
}
```

## 上传文件

**接口地址**:`/infra/file/upload`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:<p>模式一：后端上传文件</p>

**请求参数**:

**请求参数**:

| 参数名称  | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --------- | -------- | -------- | -------- | -------- | ------ |
| directory | 文件目录 | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema             |
| ------ | ---- | ------------------ |
| 200    | OK   | CommonResultString |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | string         |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": "",
	"msg": ""
}
```
