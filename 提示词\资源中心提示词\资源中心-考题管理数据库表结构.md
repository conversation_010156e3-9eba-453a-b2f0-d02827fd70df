# 资源中心-考题管理数据库表结构

**生成时间：** 2025-07-31 **数据来源：** 基于 `src/views/infra/ResourceCenter/QuestionManagement` 目录下的Vue页面文件分析生成 **字符集：** utf8mb4 **排序规则：** utf8mb4_unicode_ci

## 表结构说明

本文档包含考题管理模块的完整数据库表结构，包括：

1. 考题主表 - 存储考题的基本信息和内容
2. 考题分类表 - 存储考题的分类层级信息
3. 考题选项表 - 存储选择题、匹配题等的选项信息

---

## 业务字段分析

### 从页面分析提取的字段

#### 考题管理主页面筛选字段

- 一级名称 (level1_name) - 职业技能等级认定、专项职业能力考核、职业资格认证
- 二级名称 (level2_name) - 家政服务类、技术工人类、专业技术类、管理人员类
- 三级名称 (level3_name) - 家政服务员、育婴员、电工、健康管理师等
- 题型 (type) - 单选、多选、判断、简答
- 业务模块 (biz) - 家政业务、高校业务、培训业务、认证业务
- 题干内容关键词搜索 (keyword)

#### 考题管理主页面表格字段

- 一级名称 (level1_name)
- 二级名称 (level2_name)
- 三级名称 (level3_name)
- 认定点名称 (certName)
- 题干 (title)
- 题型 (type)
- 业务模块 (biz)
- 参考答案 (answer)
- 创建人 (creator)
- 创建时间 (createTime)

#### 新增考题页面字段

**基础信息字段：**

- 一级名称 (level1_name) - 必填
- 一级代码 (level1_code) - 必填
- 二级名称 (level2_name) - 必填
- 二级代码 (level2_code) - 必填
- 三级名称 (level3_name) - 必填
- 三级代码 (level3_code) - 必填
- 认定点名称 (certName) - 必填
- 认定点代码 (certCode) - 必填

**题目内容字段：**

- 题型 (type) - 必填，单选题、多选题、判断题、简答题、填空题、材料题、排序题、匹配题、文件上传题
- 题干 (title) - 必填，文本域
- 参考答案 (answer) - 必填，文本域

#### 分类管理页面字段

- 一级名称 (level1_name)
- 一级代码 (level1_code)
- 二级名称 (level2_name)
- 二级代码 (level2_code)
- 三级名称 (level3_name)
- 三级代码 (level3_code)
- 认定点名称 (certName)
- 认定点代码 (certCode)
- 业务模块 (biz)
- 创建人 (creator)
- 创建时间 (createTime)

#### 添加分类页面字段

- 所属上级 (parentLevel) - 可选
- 分类名称 (categoryName) - 必填
- 分类代码 (categoryCode) - 必填
- 认定点名称 (certName) - 条件必填
- 认定点代码 (certCode) - 条件必填
- 业务模块 (biz) - 必填

#### 批量导入字段

- Excel文件上传
- 支持的题型：单选、多选、判断、简答、填空、材料题、排序题、匹配题、组合题

## 数据库表结构设计

### 1. 考题主表 (publicbiz_question)

```sql
-- 考题主表：存储考题的基本信息、题干、答案等核心数据
CREATE TABLE `publicbiz_question` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',

  -- 分类信息字段
  `level1_name` VARCHAR(64) NOT NULL COMMENT '一级分类名称，如：职业技能等级认定',
  `level1_code` VARCHAR(32) NOT NULL COMMENT '一级分类代码，如：ZY001',
  `level2_name` VARCHAR(64) NOT NULL COMMENT '二级分类名称，如：家政服务类',
  `level2_code` VARCHAR(32) NOT NULL COMMENT '二级分类代码，如：JZ001',
  `level3_name` VARCHAR(64) NOT NULL COMMENT '三级分类名称，如：家政服务员',
  `level3_code` VARCHAR(32) NOT NULL COMMENT '三级分类代码，如：JZFW001',

  -- 认定点信息
  `cert_name` VARCHAR(128) NOT NULL COMMENT '认定点名称，如：职业道德基础',
  `cert_code` VARCHAR(32) NOT NULL COMMENT '认定点代码，如：KP001',

  -- 题目内容
  `title` TEXT NOT NULL COMMENT '题干内容',
  `type` VARCHAR(32) NOT NULL COMMENT '题型：单选题、多选题、判断题、简答题、填空题、材料题、排序题、匹配题、文件上传题',
  `answer` TEXT NOT NULL COMMENT '参考答案',

  -- 业务分类
  `biz` VARCHAR(64) NOT NULL COMMENT '业务模块：家政业务、高校业务、培训业务、认证业务',
  `biz_name` VARCHAR(64) COMMENT '业务模块名称',

  -- 扩展字段
  `difficulty` TINYINT DEFAULT 1 COMMENT '难度等级：1-简单，2-中等，3-困难',
  `score` DECIMAL(5,2) DEFAULT 0.00 COMMENT '题目分值',
  `time_limit` INT DEFAULT 0 COMMENT '答题时间限制（秒），0表示无限制',
  `explanation` TEXT COMMENT '题目解析',
  `keywords` VARCHAR(255) COMMENT '关键词，用逗号分隔',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',

  -- 公共字段
  `creator` VARCHAR(64) COMMENT '创建人',
  `creator_name` VARCHAR(64) COMMENT '创建人姓名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 索引
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_level1_name` (`level1_name`),
  INDEX `idx_level2_name` (`level2_name`),
  INDEX `idx_level3_name` (`level3_name`),
  INDEX `idx_type` (`type`),
  INDEX `idx_biz` (`biz`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`),
  INDEX `idx_creator` (`creator`),
  FULLTEXT INDEX `idx_title_content` (`title`, `answer`) WITH PARSER ngram
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考题主表';
```

### 2. 考题分类表 (publicbiz_question_category)

```sql
-- 考题分类表：存储考题的分类层级结构和代码信息
CREATE TABLE `publicbiz_question_category` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',

  -- 分类层级信息
  `level1_name` VARCHAR(64) NOT NULL COMMENT '一级分类名称',
  `level1_code` VARCHAR(32) NOT NULL COMMENT '一级分类代码',
  `level2_name` VARCHAR(64) COMMENT '二级分类名称',
  `level2_code` VARCHAR(32) COMMENT '二级分类代码',
  `level3_name` VARCHAR(64) COMMENT '三级分类名称',
  `level3_code` VARCHAR(32) COMMENT '三级分类代码',

  -- 认定点信息
  `cert_name` VARCHAR(128) COMMENT '认定点名称',
  `cert_code` VARCHAR(32) COMMENT '认定点代码',

  -- 业务分类
  `biz` VARCHAR(64) NOT NULL COMMENT '业务模块：家政业务、高校业务、培训业务、认证业务',
  `biz_name` VARCHAR(64) COMMENT '业务模块名称',

  -- 分类属性
  `parent_id` BIGINT DEFAULT 0 COMMENT '父级分类ID，0表示顶级分类',
  `level` TINYINT NOT NULL DEFAULT 1 COMMENT '分类层级：1-一级，2-二级，3-三级',
  `sort_order` INT DEFAULT 0 COMMENT '排序序号',
  `description` TEXT COMMENT '分类描述',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',

  -- 公共字段
  `creator` VARCHAR(64) COMMENT '创建人',
  `creator_name` VARCHAR(64) COMMENT '创建人姓名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 索引
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_parent_id` (`parent_id`),
  INDEX `idx_level` (`level`),
  INDEX `idx_biz` (`biz`),
  INDEX `idx_status` (`status`),
  UNIQUE INDEX `uk_category_code` (`tenant_id`, `level1_code`, `level2_code`, `level3_code`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考题分类表';
```

### 3. 考题选项表 (publicbiz_question_option)

```sql
-- 考题选项表：存储选择题、匹配题、排序题等的选项信息
CREATE TABLE `publicbiz_question_option` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',

  -- 关联信息
  `question_id` BIGINT NOT NULL COMMENT '考题ID，关联publicbiz_question表',

  -- 选项信息
  `option_type` VARCHAR(16) NOT NULL COMMENT '选项类型：choice-选择项，match_left-匹配左列，match_right-匹配右列',
  `option_key` VARCHAR(8) NOT NULL COMMENT '选项标识，如：A、B、C、D或1、2、3、4',
  `option_content` TEXT NOT NULL COMMENT '选项内容',
  `is_correct` TINYINT(1) DEFAULT 0 COMMENT '是否正确答案：0-否，1-是',
  `sort_order` INT DEFAULT 0 COMMENT '排序序号',

  -- 匹配题专用字段
  `match_target` VARCHAR(8) COMMENT '匹配目标，用于匹配题记录对应关系',

  -- 公共字段
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 索引
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_question_id` (`question_id`),
  INDEX `idx_option_type` (`option_type`),
  INDEX `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考题选项表';
```

## 表结构设计说明

### 设计思路

1. **考题主表**：存储考题的核心信息，包括分类、题干、答案等，支持多种题型
2. **考题分类表**：管理三级分类结构，支持层级关系和代码管理
3. **考题选项表**：存储选择题、匹配题等的选项信息，支持多种选项类型
4. **索引设计**：为常用查询字段添加索引，提高查询性能

### 字段设计要点

- 所有表都包含标准的审计字段（deleted、creator、create_time等）
- 使用合适的数据类型和长度，TEXT类型用于存储长文本内容
- 为枚举类型字段添加详细注释说明可选值
- 考虑了业务扩展性，预留了必要的字段空间
- 通过关联字段和外键约束保证数据关联关系
- 支持全文检索，为题干和答案内容建立全文索引

### 支持的业务功能

- ✅ 考题列表查询和多条件筛选
- ✅ 考题新增、编辑、复制、删除
- ✅ 多种题型支持（单选、多选、判断、简答、填空、材料题、排序题、匹配题、文件上传题）
- ✅ 三级分类管理和代码管理
- ✅ 考题选项管理（选择题、匹配题等）
- ✅ 批量导入考题功能
- ✅ 多租户数据隔离
- ✅ 全文搜索功能

### 常用查询示例

#### 1. 查询考题列表（支持多条件筛选）

```sql
SELECT
    id, level1_name, level2_name, level3_name, cert_name, title, type, biz, answer,
    creator, create_time, update_time
FROM publicbiz_question
WHERE deleted = 0
    AND tenant_id = 1
    AND (level1_name = '职业技能等级认定' OR '职业技能等级认定' = '')
    AND (level2_name = '家政服务类' OR '家政服务类' = '')
    AND (level3_name = '家政服务员' OR '家政服务员' = '')
    AND (type = '单选题' OR '单选题' = '')
    AND (biz = '家政业务' OR '家政业务' = '')
    AND (MATCH(title, answer) AGAINST('关键词' IN NATURAL LANGUAGE MODE) OR '关键词' = '')
ORDER BY create_time DESC
LIMIT 0, 10;
```

#### 2. 查询考题详情（包含选项信息）

```sql
-- 查询考题基本信息
SELECT * FROM publicbiz_question
WHERE id = 1 AND deleted = 0 AND tenant_id = 1;

-- 查询考题选项
SELECT * FROM publicbiz_question_option
WHERE question_id = 1 AND deleted = 0 AND tenant_id = 1
ORDER BY option_type ASC, sort_order ASC;
```

#### 3. 查询分类列表

```sql
SELECT * FROM publicbiz_question_category
WHERE deleted = 0 AND tenant_id = 1
    AND (biz = '家政业务' OR '家政业务' = '')
ORDER BY level ASC, sort_order ASC;
```

### 数据完整性约束

1. **关联关系**：通过 `question_id` 等字段维护表间关联关系，使用外键约束保证数据一致性
2. **应用层控制**：删除考题时需要在应用层处理相关的选项数据
3. **非空约束**：关键字段如题干、题型、分类等设为NOT NULL
4. **默认值**：为状态、难度等字段设置合理的默认值
5. **唯一性约束**：分类代码组合建立唯一索引，防止重复数据

### 扩展性考虑

1. **预留字段空间**：varchar字段长度适当冗余，便于后续扩展
2. **索引优化**：为常用查询字段建立索引，支持全文检索
3. **多租户支持**：所有表都包含tenant_id字段
4. **软删除**：使用deleted字段实现软删除，保留数据历史
5. **题型扩展**：题型字段设计为varchar，便于添加新的题型
6. **分类层级**：支持三级分类结构，可根据需要扩展更多层级

### 性能优化建议

1. **分区策略**：可按租户ID或时间对大表进行分区
2. **缓存策略**：分类信息等相对固定的数据可以缓存
3. **读写分离**：考题查询频繁的表可考虑读写分离
