<!--
  页面名称：完成凭证弹窗
  功能描述：显示任务完成凭证的详细信息
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="完成凭证"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="certificate-content">
      <!-- 任务信息 -->
      <div class="task-info">
        <h4>任务信息:</h4>
        <p>{{ taskInfo }}</p>
      </div>

      <!-- 完成凭证图片 -->
      <div class="certificate-image">
        <div v-if="certificateImage" class="image-container">
          <img :src="certificateImage" alt="完成凭证" />
        </div>
        <div v-else class="image-placeholder">
          <el-icon size="48" color="#909399">
            <Picture />
          </el-icon>
          <p>完成凭证</p>
        </div>
      </div>

      <!-- 完成详情 -->
      <div class="completion-details">
        <div class="detail-item">
          <label>完成时间:</label>
          <span>{{ completionTime }}</span>
        </div>
        <div class="detail-item">
          <label>完成人员:</label>
          <span>{{ completionPersonnel }}</span>
        </div>
        <div class="detail-item">
          <label>打卡地点:</label>
          <span>{{ punchLocation }}</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Picture } from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  taskData: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const taskInfo = computed(() => {
  if (!props.taskData) return ''
  return `${props.taskData.taskSequence} - ${props.taskData.plannedContent}`
})

const completionTime = computed(() => {
  return props.taskData?.completionTime || ''
})

const completionPersonnel = computed(() => {
  return props.taskData?.finalPersonnel || props.taskData?.currentPersonnel || ''
})

const punchLocation = computed(() => {
  return props.taskData?.punchLocation || ''
})

const certificateImage = computed(() => {
  return props.taskData?.certificateImage || ''
})

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.certificate-content {
  .task-info {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 14px;
    }

    p {
      margin: 0;
      color: #606266;
      line-height: 1.5;
    }
  }

  .certificate-image {
    margin-bottom: 20px;

    .image-container {
      width: 100%;
      max-height: 300px;
      overflow: hidden;
      border-radius: 8px;
      border: 1px solid #e4e7ed;

      img {
        width: 100%;
        height: auto;
        object-fit: cover;
      }
    }

    .image-placeholder {
      width: 100%;
      height: 200px;
      border: 2px dashed #e4e7ed;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #fafafa;

      p {
        margin: 8px 0 0 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }

  .completion-details {
    .detail-item {
      display: flex;
      margin-bottom: 12px;

      label {
        min-width: 80px;
        color: #606266;
        font-weight: 500;
        margin-right: 8px;
      }

      span {
        color: #303133;
        flex: 1;
        word-break: break-all;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
