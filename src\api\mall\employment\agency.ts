import request from '@/config/axios'

/**
 * 机构管理 API（按接口文档 /publicbiz/agency/...）
 */

// 列表查询参数（对接接口文档命名）
export interface AgencyPageParams {
  pageNum: number
  pageSize: number
  keyword?: string
  cooperationStatus?: 'cooperating' | 'suspended' | 'terminated' | 'pending'
  reviewStatus?: 'pending' | 'approved' | 'rejected'
  district?: string
  agencyNo?: string
}

// 机构详情（保持与文档字段一致，便于直接展示）
export interface AgencyDetailVO {
  id: number
  agencyName: string
  agencyShortName?: string
  agencyNo: string
  agencyType?: string
  legalRepresentative?: string
  unifiedSocialCreditCode?: string
  establishmentDate?: string
  registeredAddress?: string
  operatingAddress?: string
  businessScope?: string
  applicantName?: string
  applicantPhone?: string
  applicationTime?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  province?: string
  city?: string
  district?: string
  cooperationStatus?: 'cooperating' | 'suspended' | 'terminated' | 'pending'
  reviewStatus?: 'pending' | 'approved' | 'rejected'
  qualifications?: Array<{
    fileType: string
    fileName: string
    fileUrl: string
  }>
}

// 审核更新参数
export interface AgencyReviewUpdateParams {
  id: number
  reviewStatus: 'approved' | 'rejected'
  reviewRemark?: string
}

// 分页返回的通用结构（后端常见返回 { list, total }）
export interface PageResult<T> {
  list: T[]
  total: number
  pageNum?: number
  pageSize?: number
}

// 获取机构分页
export function getAgencyPage(params: AgencyPageParams) {
  return request.get<PageResult<AgencyDetailVO> | AgencyDetailVO[]>({
    url: '/publicbiz/agency/page',
    params
  })
}

// 获取机构详情
export function getAgencyDetail(id: number) {
  return request.get<AgencyDetailVO>({
    url: `/publicbiz/agency/get/${id}`
  })
}

// 审核更新
export function updateAgencyReview(data: AgencyReviewUpdateParams) {
  return request.put<void>({
    url: '/publicbiz/agency/update',
    data
  })
}

// 兼容旧方法名（如代码中曾经引用）
export const getAgencyList = getAgencyPage
