import request from '@/config/axios'

/**
 * 分类
 */
export interface CategoryVO {
  /**
   * 分类编号
   */
  id?: number
  /**
   * 父分类编号
   */
  parentId?: number
  /**
   * 分类名称
   */
  name: string
  /**
   * 分类图标
   */
  picUrl: string
  /**
   * 分类排序
   */
  sort: number
  /**
   * 开启状态
   */
  status: number
  /**
   * 创建时间
   */
  createTime?: string
}

// 创建分类
export const createCategory = (data: CategoryVO) => {
  return request.post({ url: '/publicbiz/employment/category/create', data })
}

// 更新分类
export const updateCategory = (data: CategoryVO) => {
  return request.put({ url: '/publicbiz/employment/category/update', data })
}

// 删除分类
export const deleteCategory = (id: number) => {
  return request.delete({ url: `/publicbiz/employment/category/delete?id=${id}` })
}

// 获得分类
export const getCategory = (id: number) => {
  return request.get({ url: `/publicbiz/employment/category/get?id=${id}` })
}

// 获得分类列表
export const getCategoryList = (params: any) => {
  return request.get({ url: '/publicbiz/employment/category/list', params })
}

// 获得分类分页
export const getCategoryPage = (params: any) => {
  return request.get({ url: '/publicbiz/employment/category/page', params })
}

// 获得分类精简列表
export const getCategorySimpleList = () => {
  return request.get({ url: '/publicbiz/employment/category/simple-list' })
}
