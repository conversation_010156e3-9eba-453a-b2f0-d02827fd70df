package cn.bztmaster.cnt.module.publicbiz.enums;

/**
 * 日志记录常量接口
 * 定义各种业务模块的日志记录类型常量
 */
public interface LogRecordConstants {

    // ==================== 高校实践订单相关常量 ====================
    
    /**
     * 高校实践订单模块
     */
    String UNIVERSITY_PRACTICE_ORDER = "university_practice_order";
    
    /**
     * 高校实践订单创建
     */
    String UNIVERSITY_PRACTICE_ORDER_CREATE = "university_practice_order_create";
    
    /**
     * 高校实践订单编辑
     */
    String UNIVERSITY_PRACTICE_ORDER_UPDATE = "university_practice_order_update";
    
    /**
     * 高校实践订单删除
     */
    String UNIVERSITY_PRACTICE_ORDER_DELETE = "university_practice_order_delete";
    
    /**
     * 高校实践订单审批
     */
    String UNIVERSITY_PRACTICE_ORDER_APPROVAL = "university_practice_order_approval";
    
    /**
     * 高校实践订单收款确认
     */
    String UNIVERSITY_PRACTICE_ORDER_COLLECTION_CONFIRM = "university_practice_order_collection_confirm";
    
    /**
     * 高校实践订单收款信息更新
     */
    String UNIVERSITY_PRACTICE_ORDER_COLLECTION_UPDATE = "university_practice_order_collection_update";
    
    /**
     * 高校实践订单状态变更
     */
    String UNIVERSITY_PRACTICE_ORDER_STATUS_CHANGE = "university_practice_order_status_change";
    
    /**
     * 高校实践订单合同管理
     */
    String UNIVERSITY_PRACTICE_ORDER_CONTRACT = "university_practice_order_contract";

    // ==================== 企业培训订单相关常量 ====================
    
    /**
     * 企业培训订单模块
     */
    String ENTERPRISE_TRAINING_ORDER = "enterprise_training_order";
    
    /**
     * 企业培训订单创建
     */
    String ENTERPRISE_TRAINING_ORDER_CREATE = "enterprise_training_order_create";
    
    /**
     * 企业培训订单编辑
     */
    String ENTERPRISE_TRAINING_ORDER_UPDATE = "enterprise_training_order_update";
    
    /**
     * 企业培训订单删除
     */
    String ENTERPRISE_TRAINING_ORDER_DELETE = "enterprise_training_order_delete";

    // ==================== 个人培训订单相关常量 ====================
    
    /**
     * 个人培训订单模块
     */
    String PERSONAL_TRAINING_ORDER = "personal_training_order";
    
    /**
     * 个人培训订单创建
     */
    String PERSONAL_TRAINING_ORDER_CREATE = "personal_training_order_create";
    
    /**
     * 个人培训订单编辑
     */
    String PERSONAL_TRAINING_ORDER_UPDATE = "personal_training_order_update";
    
    /**
     * 个人培训订单删除
     */
    String PERSONAL_TRAINING_ORDER_DELETE = "personal_training_order_delete";

    // ==================== 家政服务订单相关常量 ====================
    
    /**
     * 家政服务订单模块
     */
    String DOMESTIC_SERVICE_ORDER = "domestic_service_order";
    
    /**
     * 家政服务订单创建
     */
    String DOMESTIC_SERVICE_ORDER_CREATE = "domestic_service_order_create";
    
    /**
     * 家政服务订单编辑
     */
    String DOMESTIC_SERVICE_ORDER_UPDATE = "domestic_service_order_update";
    
    /**
     * 家政服务订单删除
     */
    String DOMESTIC_SERVICE_ORDER_DELETE = "domestic_service_order_delete";

    // ==================== 考试认证订单相关常量 ====================
    
    /**
     * 考试认证订单模块
     */
    String CERTIFICATION_ORDER = "certification_order";
    
    /**
     * 考试认证订单创建
     */
    String CERTIFICATION_ORDER_CREATE = "certification_order_create";
    
    /**
     * 考试认证订单编辑
     */
    String CERTIFICATION_ORDER_UPDATE = "certification_order_update";
    
    /**
     * 考试认证订单删除
     */
    String CERTIFICATION_ORDER_DELETE = "certification_order_delete";

    // ==================== 通用操作常量 ====================
    
    /**
     * 通用创建操作
     */
    String COMMON_CREATE = "common_create";
    
    /**
     * 通用编辑操作
     */
    String COMMON_UPDATE = "common_update";
    
    /**
     * 通用删除操作
     */
    String COMMON_DELETE = "common_delete";
    
    /**
     * 通用查询操作
     */
    String COMMON_QUERY = "common_query";
    
    /**
     * 通用审批操作
     */
    String COMMON_APPROVAL = "common_approval";
    
    /**
     * 通用状态变更操作
     */
    String COMMON_STATUS_CHANGE = "common_status_change";
}

