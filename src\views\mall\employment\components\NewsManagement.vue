<!--
  页面名称：资讯管理
  功能描述：展示资讯列表，支持新增、编辑、删除等操作
-->
<template>
  <div class="news-management">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="资讯标题">
          <el-input v-model="searchForm.newsTitle" placeholder="输入资讯标题" clearable />
        </el-form-item>
        <el-form-item label="分类">
          <el-select
            v-model="searchForm.categoryId"
            placeholder="全部"
            clearable
            style="width: 100px"
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 100px">
            <el-option label="全部" value="" />
            <el-option label="已发布" value="published" />
            <el-option label="草稿" value="draft" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容来源">
          <el-select
            v-model="searchForm.contentSource"
            placeholder="全部"
            clearable
            style="width: 100px"
          >
            <el-option label="全部" value="" />
            <el-option label="手动编写" value="manual" />
            <el-option label="关联素材库" value="material" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
        <el-form-item class="add-button-wrapper">
          <el-button type="primary" @click="onAdd">
            <i class="fas fa-plus"></i> 新增资讯
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 资讯列表 -->
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="封面图" width="120">
        <template #default="scope">
          <el-image
            :src="scope.row.coverImageUrl"
            :preview-src-list="[scope.row.coverImageUrl]"
            style="width: 80px; height: 60px"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="newsTitle" label="标题" min-width="200" />
      <el-table-column prop="categoryName" label="分类" width="100" />
      <el-table-column prop="author" label="作者" width="100" />
      <el-table-column prop="contentSource" label="内容来源" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.materialId ? 'warning' : 'primary'" size="small">
            {{ scope.row.materialId ? '关联素材库' : '手动编写' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'published' ? 'success' : 'info'" size="small">
            {{ scope.row.status === 'published' ? '已发布' : '草稿' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="150">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="onEdit(scope.row)">编辑</el-button>
          <el-button
            size="small"
            :type="scope.row.status === 'published' ? 'warning' : 'success'"
            @click="onToggleStatus(scope.row)"
          >
            {{ scope.row.status === 'published' ? '下架' : '发布' }}
          </el-button>
          <el-button size="small" type="danger" @click="onDelete(scope.row)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.pageNo"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px" @close="onDialogClose">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标题" prop="newsTitle">
          <el-input v-model="form.newsTitle" placeholder="请输入资讯标题" />
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择分类" @change="onCategoryChange">
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="封面图" prop="coverImageUrl">
          <div class="image-upload-container">
            <el-upload
              class="image-uploader"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :http-request="handleImageUpload"
              :on-success="onImageSuccess"
            >
              <img v-if="form.coverImageUrl" :src="form.coverImageUrl" class="upload-image" />
              <el-icon v-else class="upload-icon"><Plus /></el-icon>
              <div class="upload-text"></div>
            </el-upload>
            <div class="image-upload-tips">
              <div class="tip-item">
                <i class="el-icon-info"></i>
                <span>建议尺寸750×400px</span>
              </div>
              <div class="tip-item">
                <i class="el-icon-info"></i>
                <span>支持jpg、png、gif格式，不超过2MB</span>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="form.author" placeholder="请输入作者" />
        </el-form-item>
        <el-form-item label="摘要" prop="newsSummary">
          <el-input
            v-model="form.newsSummary"
            type="textarea"
            :rows="3"
            placeholder="请输入资讯摘要"
          />
        </el-form-item>
        <el-form-item label="内容来源" prop="contentSource">
          <el-radio-group v-model="form.contentSource" @change="onContentSourceChange">
            <el-radio label="manual">手动编写</el-radio>
            <el-radio label="material">关联素材库文章</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 素材库文章选择 -->
        <el-form-item
          v-if="form.contentSource === 'material'"
          label="选择素材库文章"
          prop="materialId"
        >
          <!-- 当前关联的文章信息 -->
          <div v-if="form.materialId && getCurrentMaterialArticle()" class="current-article-info">
            <el-alert
              :title="`当前关联: ${getCurrentMaterialArticle()?.title}`"
              type="success"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="current-article-details">
                  <span>作者: {{ getCurrentMaterialArticle()?.author }}</span>
                  <span>来源: {{ getCurrentMaterialArticle()?.source }}</span>
                  <span>发布时间: {{ getCurrentMaterialArticle()?.publishDate }}</span>
                  <span>阅读量: {{ getCurrentMaterialArticle()?.readCount }}</span>
                  <el-button
                    size="small"
                    type="danger"
                    @click="disconnectMaterialArticle"
                    style="margin-left: 15px"
                  >
                    解除关联
                  </el-button>
                </div>
              </template>
            </el-alert>
          </div>

          <div class="material-article-selector">
            <div class="search-bar">
              <el-input
                v-model="materialSearchKeyword"
                placeholder="搜索文章标题..."
                style="width: 300px; margin-right: 10px"
              />
              <el-button type="primary" @click="searchMaterialArticles">搜索</el-button>
            </div>
            <div class="article-list" @scroll="onArticleListScroll" ref="articleListRef">
              <div
                v-if="materialArticles.length === 0 && !materialPagination.loading"
                class="no-articles"
              >
                暂无素材库文章，请先搜索或联系管理员添加
              </div>
              <div
                v-for="article in materialArticles"
                :key="article.id"
                class="article-item"
                :class="{ selected: form.materialId === article.id }"
                @click="selectMaterialArticle(article)"
              >
                <div class="article-info">
                  <div class="article-title">{{ article.title }}</div>
                  <div class="article-meta">
                    <span>作者: {{ article.author }}</span>
                    <span>来源: {{ article.source }}</span>
                    <span>发布时间: {{ article.publishDate }}</span>
                    <span>阅读量: {{ article.readCount }}</span>
                  </div>
                </div>
                <el-button size="small" @click.stop="previewArticle(article)">预览</el-button>
              </div>

              <!-- 加载更多提示 -->
              <div v-if="materialPagination.loading" class="loading-more">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>加载中...</span>
              </div>

              <!-- 没有更多数据提示 -->
              <div
                v-if="!materialPagination.hasMore && materialArticles.length > 0"
                class="no-more"
              >
                没有更多数据了
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="内容" prop="newsContent" v-if="form.contentSource !== 'material'">
          <div class="rich-editor-wrapper" style="width: 100%">
            <!-- 富文本编辑器工具栏 -->
            <div class="editor-toolbar">
              <el-button
                type="text"
                :class="{ active: editorState.bold }"
                @click="toggleBold"
                title="粗体"
              >
                <strong>B</strong>
              </el-button>
              <el-button
                type="text"
                :class="{ active: editorState.italic }"
                @click="toggleItalic"
                title="斜体"
              >
                <em>I</em>
              </el-button>
              <el-button
                type="text"
                :class="{ active: editorState.underline }"
                @click="toggleUnderline"
                title="下划线"
              >
                <u>U</u>
              </el-button>
              <el-divider direction="vertical" />
              <el-button type="text" @click="insertUnorderedList" title="无序列表">
                <i class="fas fa-list-ul"></i>
              </el-button>
              <el-button type="text" @click="insertOrderedList" title="有序列表">
                <i class="fas fa-list-ol"></i>
              </el-button>
              <el-divider direction="vertical" />
              <el-button type="text" @click="insertImage" title="插入图片">
                <i class="fas fa-image"></i>
              </el-button>
            </div>

            <!-- 富文本编辑器内容区域 -->
            <div
              ref="editorContent"
              class="editor-content"
              contenteditable="true"
              @input="onEditorInput"
              @paste="onEditorPaste"
              @keydown="onEditorKeydown"
              v-html="form.newsContent"
            ></div>
          </div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="draft">草稿</el-radio>
            <el-radio label="published">发布</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onDialogClose">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { Plus, Loading } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getNewsList,
  createNews,
  updateNews,
  deleteNews,
  updateNewsStatus,
  getMaterialArticles,
  type News,
  type MaterialArticle
} from '@/api/mall/employment/news'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { useUserStore } from '@/store/modules/user'
import { updateFile } from '@/api/infra/file'
// mock数据引入

/** 表格数据 */
const tableData = ref<News[]>([])

/** 分页信息 */
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

/** 搜索表单数据 */
const searchForm = reactive({
  newsTitle: '',
  categoryId: '',
  status: '',
  contentSource: ''
})

/** 对话框显示状态 */
const dialogVisible = ref(false)

/** 对话框标题 */
const dialogTitle = ref('')

/** 表单数据 */
const form = reactive({
  id: 0,
  newsTitle: '',
  categoryId: 0,
  categoryName: '',
  coverImageUrl: '',
  newsSummary: '',
  newsContent: '',
  author: '',
  status: 'draft' as 'draft' | 'published',
  contentSource: 'manual' as 'manual' | 'material',
  materialId: 0,
  sort: 0
})

/** 素材库文章搜索关键词 */
const materialSearchKeyword = ref('')

/** 分类选项 */
const categoryOptions = ref<{ label: string; value: number }[]>([])

/** 素材库文章列表 */
const materialArticles = ref<MaterialArticle[]>([])

/** 素材库文章分页信息 */
const materialPagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0,
  loading: false,
  hasMore: true
})

/** 用户store */
const userStore = useUserStore()

/** 素材库文章列表引用 */
const articleListRef = ref<HTMLElement>()

/** 富文本编辑器引用 */
const editorContent = ref<HTMLElement>()

/** 富文本编辑器状态 */
const editorState = reactive({
  bold: false,
  italic: false,
  underline: false
})

/** 表单校验规则 */
const rules = {
  newsTitle: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
  coverImageUrl: [{ required: true, message: '请上传封面图', trigger: 'change' }],
  newsSummary: [{ required: true, message: '请输入摘要', trigger: 'blur' }],
  newsContent: [
    {
      required: true,
      message: '请输入内容',
      trigger: 'blur',
      validator: (rule: any, value: any, callback: any) => {
        if (form.contentSource === 'material') {
          // 关联素材库文章时，内容不必填
          callback()
        } else if (!value || value.trim() === '') {
          callback(new Error('请输入内容'))
        } else {
          callback()
        }
      }
    }
  ],
  author: [{ required: true, message: '请输入作者', trigger: 'blur' }],
  contentSource: [{ required: true, message: '请选择内容来源', trigger: 'change' }],
  materialId: [
    {
      required: true,
      message: '请选择素材库文章',
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (form.contentSource === 'material' && !value) {
          callback(new Error('请选择素材库文章'))
        } else {
          callback()
        }
      }
    }
  ],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

/** 加载分类选项 */
const loadCategoryOptions = async () => {
  try {
    const res = await getDictDataPage({ dictType: 'news_category', pageNo: 1, pageSize: 50 })
    const list = res?.list ?? []
    categoryOptions.value = list.map((item: any) => ({
      label: item.label,
      value: Number(item.value)
    }))
  } catch (error) {
    console.error('获取分类选项失败:', error)
    ElMessage.error('获取分类选项失败')
  }
}

/** 表单引用 */
const formRef = ref()

/** 获取资讯列表 */
const fetchList = async () => {
  try {
    // 构建查询参数
    const params = {
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      newsTitle: searchForm.newsTitle?.trim() || undefined,
      categoryId: searchForm.categoryId ? Number(searchForm.categoryId) : undefined,
      status: searchForm.status || undefined,
      contentSource: searchForm.contentSource || undefined
    }

    // 移除空值参数
    Object.keys(params).forEach((key) => {
      if (params[key] === undefined || params[key] === null || params[key] === '') {
        delete params[key]
      }
    })

    // 调用真实接口
    console.log('调用真实接口，参数:', params)
    const res: any = await getNewsList(params)
    console.log('出参:', res)

    // 兼容不同返回形态：res 可能是 {list,total} 或 {data: { list, total }} 或 纯数组
    const rawList = Array.isArray(res)
      ? res
      : res?.list || res?.data?.list || res?.records || res?.data || []

    // 设置分页信息
    pagination.total = (res?.total ?? res?.data?.total ?? 0) as number
    // 不覆盖用户当前选择的分页参数，只更新总数
    // if (res?.page) pagination.pageNo = res.page
    // if (res?.size) pagination.pageSize = res.size

    // 数据映射，确保字段名称一致
    tableData.value = (rawList || []).map((item: any) => {
      return {
        id: item.id,
        newsTitle: item.newsTitle || item.news_title || item.title || '-',
        newsSummary: item.newsSummary || item.news_summary || item.summary || '',
        newsContent: item.newsContent || item.news_content || item.content || '',
        categoryId: item.categoryId || item.category_id || item.categoryId || 0,
        categoryName: item.categoryName || item.category_name || item.category || '-',
        coverImageUrl:
          item.coverImageUrl || item.cover_image_url || item.coverImage || item.cover_image || '',
        materialId: item.materialId || item.material_id || item.materialId || null,
        author: item.author || '',
        status: item.status !== undefined && item.status !== null ? item.status : 'draft',
        viewCount: item.viewCount || item.view_count || item.views || 0,
        likeCount: item.likeCount || item.like_count || item.likes || 0,
        shareCount: item.shareCount || item.share_count || item.shares || 0,
        commentCount: item.commentCount || item.comment_count || item.comments || 0,
        sort: item.sort || item.sort || 0,
        createTime: item.createTime || item.create_time || item.createTime || '',
        updateTime: item.updateTime || item.update_time || item.updateTime || '',
        publishTime: item.publishTime || item.publish_time || item.publishTime || null
      }
    })

    console.log('处理后的数据:', tableData.value)
  } catch (error) {
    console.error('获取资讯列表失败:', error)
    ElMessage.error('获取资讯列表失败')
    // 清空数据
    tableData.value = []
    pagination.total = 0
    throw error
  }
}

/** 新增 */
const onAdd = () => {
  dialogTitle.value = '新增资讯'
  Object.assign(form, {
    id: 0,
    newsTitle: '',
    categoryId: 0,
    categoryName: '',
    coverImageUrl: '',
    newsSummary: '',
    newsContent: '',
    author: '',
    status: 'draft',
    contentSource: 'manual',
    materialId: 0,
    sort: 0
  })
  materialSearchKeyword.value = ''
  materialArticles.value = []
  dialogVisible.value = true

  // 初始化素材库文章列表
  searchMaterialArticles()
}

/** 编辑 */
const onEdit = (row: News) => {
  dialogTitle.value = '编辑资讯'
  Object.assign(form, {
    id: row.id,
    newsTitle: row.newsTitle,
    categoryId: row.categoryId,
    categoryName: row.categoryName,
    coverImageUrl: row.coverImageUrl,
    newsSummary: row.newsSummary,
    newsContent: row.newsContent,
    author: row.author,
    status: row.status,
    contentSource: row.materialId ? 'material' : 'manual',
    materialId: row.materialId || 0,
    sort: row.sort
  })
  materialSearchKeyword.value = ''
  materialArticles.value = []
  dialogVisible.value = true

  // 初始化素材库文章列表
  searchMaterialArticles()
}

/** 分类选择变化 */
const onCategoryChange = (categoryId: number) => {
  // 根据选择的分类ID自动填充分类名称
  const selectedCategory = categoryOptions.value.find((item) => item.value === categoryId)
  if (selectedCategory) {
    form.categoryName = selectedCategory.label
  } else {
    form.categoryName = ''
  }
}

/** 切换内容来源 */
const onContentSourceChange = () => {
  if (form.contentSource === 'material') {
    form.materialId = 0
    // 不清空已填写的内容、摘要和作者，让用户可以保留手动填写的内容

    // 自动加载素材库文章列表
    searchMaterialArticles()
  }

  // 更新富文本编辑器显示
  nextTick(() => {
    if (editorContent.value) {
      editorContent.value.innerHTML = form.newsContent || ''
    }
  })
}

/** 搜索素材库文章 */
const searchMaterialArticles = async () => {
  try {
    // 重置分页信息
    materialPagination.pageNo = 1
    materialPagination.hasMore = true
    materialArticles.value = []

    await loadMaterialArticles()
  } catch (error) {
    console.error('搜索素材库文章失败:', error)
    ElMessage.error('搜索素材库文章失败')
    materialArticles.value = []
  }
}

/** 加载素材库文章 */
const loadMaterialArticles = async (isLoadMore = false) => {
  if (materialPagination.loading || (!isLoadMore && !materialPagination.hasMore)) {
    return
  }

  try {
    materialPagination.loading = true

    const params = {
      title: materialSearchKeyword.value,
      pageNo: materialPagination.pageNo,
      pageSize: materialPagination.pageSize
    }

    const res = await getMaterialArticles(params)

    // 兼容多种返回格式，参照执行列表的处理方式
    const newArticles = Array.isArray(res)
      ? res
      : res?.list || res?.data?.list || res?.records || res?.data || []

    // 获取总数
    const total = res?.total || res?.data?.total || newArticles.length || 0

    if (isLoadMore) {
      // 加载更多时，追加数据
      materialArticles.value = [...materialArticles.value, ...newArticles]
    } else {
      // 搜索时，替换数据
      materialArticles.value = newArticles
    }

    // 更新分页信息
    materialPagination.total = total
    materialPagination.hasMore = materialArticles.value.length < total

    // 如果还有更多数据，页码+1
    if (materialPagination.hasMore) {
      materialPagination.pageNo++
    }
  } catch (error) {
    console.error('加载素材库文章失败:', error)
    ElMessage.error('加载素材库文章失败')
  } finally {
    materialPagination.loading = false
  }
}

/** 加载更多素材库文章 */
const loadMoreMaterialArticles = async () => {
  if (materialPagination.hasMore && !materialPagination.loading) {
    await loadMaterialArticles(true)
  }
}

/** 文章列表滚动事件 */
const onArticleListScroll = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target) return

  const { scrollTop, scrollHeight, clientHeight } = target
  const scrollBottom = scrollTop + clientHeight

  // 当滚动到底部时（距离底部小于50px），自动加载更多
  if (
    scrollHeight - scrollBottom < 50 &&
    materialPagination.hasMore &&
    !materialPagination.loading
  ) {
    loadMoreMaterialArticles()
  }
}

/** 选择素材库文章 */
const selectMaterialArticle = (article: MaterialArticle) => {
  form.materialId = article.id
  form.newsContent = article.content

  // 不再自动填充摘要和作者，保持用户手动填写的内容
  // 摘要和作者字段由用户手动填写，不依赖素材库数据

  form.contentSource = 'material'

  // 更新富文本编辑器显示
  nextTick(() => {
    if (editorContent.value) {
      editorContent.value.innerHTML = form.newsContent || ''
    }
  })

  ElMessage.success('素材库文章已关联')
}

/** 预览素材库文章 */
const previewArticle = (article: MaterialArticle) => {
  ElMessageBox.alert(
    `<div style="white-space: pre-wrap;">${article.content}</div>`,
    '预览素材库文章',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '关闭',
      customClass: 'preview-dialog'
    }
  )
}

/** 获取当前关联的素材库文章 */
const getCurrentMaterialArticle = () => {
  if (!form.materialId) return null
  return materialArticles.value.find((article) => article.id === form.materialId)
}

/** 解除素材库文章关联 */
const disconnectMaterialArticle = () => {
  form.materialId = 0
  form.newsContent = ''
  form.newsSummary = ''
  form.author = ''
  form.contentSource = 'manual'
  ElMessage.success('已解除素材库文章关联')
}

/** 富文本编辑器相关方法 */
const toggleBold = () => {
  document.execCommand('bold', false)
  updateEditorState()
}

const toggleItalic = () => {
  document.execCommand('italic', false)
  updateEditorState()
}

const toggleUnderline = () => {
  document.execCommand('underline', false)
  updateEditorState()
}

const insertUnorderedList = () => {
  document.execCommand('insertUnorderedList', false)
}

const insertOrderedList = () => {
  document.execCommand('insertOrderedList', false)
}

const insertImage = () => {
  const url = prompt('请输入图片URL:')
  if (url) {
    document.execCommand('insertImage', false, url)
  }
}

const updateEditorState = () => {
  if (editorContent.value) {
    editorState.bold = document.queryCommandState('bold')
    editorState.italic = document.queryCommandState('italic')
    editorState.underline = document.queryCommandState('underline')
  }
}

const onEditorInput = () => {
  if (editorContent.value) {
    form.newsContent = editorContent.value.innerHTML
  }
}

const onEditorPaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  document.execCommand('insertText', false, text)
}

const onEditorKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'b':
        event.preventDefault()
        toggleBold()
        break
      case 'i':
        event.preventDefault()
        toggleItalic()
        break
      case 'u':
        event.preventDefault()
        toggleUnderline()
        break
    }
  }
}

/** 切换状态 */
const onToggleStatus = async (row: News) => {
  try {
    const newStatus = row.status === 'published' ? 'offline' : 'published'
    await updateNewsStatus(row.id, newStatus)
    ElMessage.success('状态更新成功')
    fetchList()
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
}

/** 删除 */
const onDelete = async (row: News) => {
  try {
    await ElMessageBox.confirm('确定要删除这个资讯吗？', '提示', {
      type: 'warning'
    })
    await deleteNews(row.id)
    ElMessage.success('删除成功')
    fetchList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

/** 图片上传前校验 */
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

/** 图片上传成功 */
const onImageSuccess = (response: any, file: any) => {
  if (response && response.data) {
    form.coverImageUrl = response.data
    ElMessage.success('图片上传成功')
  }
}

/** 图片上传请求 */
const handleImageUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      form.coverImageUrl = response.data
      ElMessage.success('封面图上传成功')
    }
  } catch (error) {
    console.error('封面图上传失败:', error)
    ElMessage.error('封面图上传失败')
  }
}

/** 对话框关闭 */
const onDialogClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
  // 手动重置表单数据，确保所有字段都被清空
  Object.assign(form, {
    id: 0,
    newsTitle: '',
    categoryId: 0,
    categoryName: '',
    coverImageUrl: '',
    newsSummary: '',
    newsContent: '',
    author: '',
    status: 'draft',
    contentSource: 'manual',
    materialId: 0,
    sort: 0
  })
}

/** 提交表单 */
const onSubmit = async () => {
  try {
    await formRef.value?.validate()

    const submitData = {
      newsTitle: form.newsTitle,
      newsSummary: form.newsSummary,
      newsContent: form.newsContent,
      categoryId: form.categoryId,
      categoryName: form.categoryName,
      coverImageUrl: form.coverImageUrl,
      materialId: form.contentSource === 'material' ? form.materialId : undefined,
      author: form.author,
      status: form.status,
      sort: form.sort
    }

    if (form.id) {
      await updateNews({ ...submitData, id: form.id })
      ElMessage.success('更新成功')
    } else {
      await createNews(submitData)
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
    fetchList()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

/** 分页大小改变 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageNo = 1
  fetchList()
}

/** 当前页改变 */
const handleCurrentChange = (page: number) => {
  pagination.pageNo = page
  fetchList()
}

/** 搜索 */
const handleSearch = () => {
  pagination.pageNo = 1
  fetchList()
}

/** 重置搜索 */
const handleReset = () => {
  Object.assign(searchForm, {
    newsTitle: '',
    categoryId: '',
    status: '',
    contentSource: ''
  })
  pagination.pageNo = 1
  fetchList()
}

onMounted(() => {
  // 加载分类选项
  loadCategoryOptions()
  // 获取资讯列表
  fetchList()
})
</script>

<style scoped lang="scss">
.news-management {
  .search-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-form {
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }

        &.add-button-wrapper {
          margin-left: auto;
        }
      }
    }
  }

  .action-bar {
    margin-bottom: 20px;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
  }

  .image-upload-container {
    display: flex;
    align-items: flex-start;
    gap: 20px;
  }

  .image-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 50%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }
  }

  .upload-image {
    width: 200px;
    height: 120px;
    object-fit: cover;
  }

  .upload-icon {
    font-size: 28px;
    color: #8c939d;
  }

  .upload-text {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }

  .image-upload-tips {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-top: 10px;

    .tip-item {
      display: flex;
      align-items: flex-start;
      gap: 6px;
      font-size: 13px;
      color: #666;
      line-height: 1.4;

      i {
        color: #409eff;
        font-size: 14px;
        margin-top: 1px;
      }
    }
  }

  .field-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    font-style: italic;
  }

  .content-source-tip {
    font-size: 12px;
    color: #409eff;
    margin-top: 5px;
    font-style: italic;
  }

  .rich-editor-wrapper {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    &:focus-within {
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .editor-toolbar {
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      padding: 10px 15px;
      display: flex;
      align-items: center;
      gap: 6px;

      .el-button {
        width: 36px;
        height: 36px;
        padding: 0;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        background: white;
        color: #495057;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background: #e9ecef;
          border-color: #adb5bd;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &.active {
          background: #007bff;
          color: white;
          border-color: #007bff;
          box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }

        strong,
        em,
        u {
          font-weight: inherit;
          font-style: inherit;
          text-decoration: inherit;
        }

        i {
          font-size: 16px;
        }
      }

      .el-divider--vertical {
        margin: 0 8px;
        height: 24px;
        background-color: #e9ecef;
      }
    }

    .editor-content {
      min-height: 200px;
      max-height: 400px;
      padding: 16px;
      outline: none;
      overflow-y: auto;
      line-height: 1.6;
      font-size: 14px;
      color: #212529;
      background: white;
      border: none;
      transition: all 0.2s ease;

      &:focus {
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }

      &.disabled {
        background: #f8f9fa;
        color: #6c757d;
        cursor: not-allowed;
        pointer-events: none;
      }

      &::before {
        content: attr(placeholder);
        color: #6c757d;
        pointer-events: none;
        display: block;
        margin-bottom: 12px;
        font-style: italic;
      }

      &:empty::before {
        content: '请输入资讯内容...';
        color: #6c757d;
        pointer-events: none;
        display: block;
        margin-bottom: 12px;
        font-style: italic;
      }

      p {
        margin: 0 0 12px 0;
        line-height: 1.7;
      }

      ul,
      ol {
        margin: 12px 0;
        padding-left: 24px;
        line-height: 1.7;
      }

      li {
        margin-bottom: 6px;
      }

      img {
        max-width: 100%;
        height: auto;
        margin: 12px 0;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 16px 0 8px 0;
        color: #212529;
        font-weight: 600;
      }

      blockquote {
        margin: 16px 0;
        padding: 12px 16px;
        border-left: 4px solid #007bff;
        background: #f8f9fa;
        color: #495057;
        font-style: italic;
      }

      code {
        background: #f8f9fa;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        color: #e83e8c;
      }

      pre {
        background: #f8f9fa;
        padding: 12px;
        border-radius: 6px;
        overflow-x: auto;
        border: 1px solid #e9ecef;
        margin: 16px 0;
      }
    }
  }

  .current-article-info {
    margin-bottom: 15px;

    .current-article-details {
      margin-top: 8px;

      span {
        margin-right: 15px;
        font-size: 12px;
        color: #666;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .material-article-selector {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
    width: 100%;

    .search-bar {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }

    .article-list {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 5px;

      .no-articles {
        text-align: center;
        color: #999;
        font-size: 14px;
        padding: 20px;
      }

      .article-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 10px;
        cursor: pointer;
        border-radius: 4px;
        margin-bottom: 5px;
        background-color: #f9f9f9;

        &:hover {
          background-color: #f0f0f0;
        }

        &.selected {
          background-color: #e6f7ff;
          border: 1px solid #91d5ff;
        }

        .article-info {
          flex-grow: 1;
          margin-right: 10px;

          .article-title {
            font-weight: bold;
            font-size: 14px;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .article-meta {
            font-size: 12px;
            color: #666;
            margin-top: 3px;

            span {
              margin-right: 15px;

              &:last-child {
                margin-right: 0;
              }
            }
          }
        }
      }

      .loading-more {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 15px;
        color: #909399;
        font-size: 14px;

        .el-icon {
          margin-right: 8px;
          font-size: 16px;
        }
      }

      .no-more {
        text-align: center;
        padding: 15px;
        color: #c0c4cc;
        font-size: 12px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }
}
</style>
