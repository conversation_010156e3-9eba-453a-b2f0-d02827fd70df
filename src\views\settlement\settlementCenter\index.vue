<!--
  页面名称：结算中心首页
  功能描述：展示结算中心主要功能，包含待结算列表、对账单列表、发票管理、订单资金列表等tab页面
-->
<template>
  <div class="settlement-center">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-icon class="header-icon"><Grid /></el-icon>
      <span class="page-title">结算中心</span>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card pending">
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ pendingCount }}</div>
              <div class="stat-label">待结算</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card reconciliation">
            <div class="stat-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ reconciliationCount }}</div>
              <div class="stat-label">对账单</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card invoice">
            <div class="stat-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ invoiceCount }}</div>
              <div class="stat-label">发票管理</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card funds">
            <div class="stat-icon">
              <el-icon><Wallet /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ fundsCount }}</div>
              <div class="stat-label">资金列表</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- Tab导航 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="settlement-tabs">
      <el-tab-pane name="pending" label="待结算列表">
        <template #label>
          <span>待结算列表</span>
          <el-badge :value="pendingCount" :max="99" class="tab-badge" />
        </template>
        <PendingSettlementList />
      </el-tab-pane>

      <el-tab-pane name="reconciliation" label="对账单列表">
        <ReconciliationList />
      </el-tab-pane>

      <el-tab-pane name="invoice" label="发票管理">
        <InvoiceManagement />
      </el-tab-pane>

      <el-tab-pane name="orderFunds" label="订单资金列表">
        <OrderFundsList />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Grid, Clock, Document, Money, Wallet } from '@element-plus/icons-vue'
import PendingSettlementList from './components/PendingSettlementList.vue'
import ReconciliationList from './components/ReconciliationList.vue'
import InvoiceManagement from './components/InvoiceManagement.vue'
import OrderFundsList from './components/OrderFundsList.vue'

/** 当前激活的tab */
const activeTab = ref('pending')

/** 待结算数量 - 使用静态数据 */
const pendingCount = ref(15)

/** 对账单数量 - 使用静态数据 */
const reconciliationCount = ref(25)

/** 发票管理数量 - 使用静态数据 */
const invoiceCount = ref(18)

/** 资金列表数量 - 使用静态数据 */
const fundsCount = ref(42)

/** 处理tab切换 */
const handleTabClick = (tab: any) => {
  console.log('切换到tab:', tab.props.name)
}
</script>

<style scoped lang="scss">
.settlement-center {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .header-icon {
      font-size: 24px;
      margin-right: 12px;
    }

    .page-title {
      font-size: 20px;
      font-weight: 600;
    }
  }

  .stats-section {
    margin-bottom: 20px;

    .stat-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 24px;
        color: white;
      }

      .stat-content {
        flex: 1;

        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: #303133;
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #606266;
        }
      }

      &.pending .stat-icon {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
      }

      &.reconciliation .stat-icon {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
      }

      &.invoice .stat-icon {
        background: linear-gradient(135deg, #45b7d1, #96c93d);
      }

      &.funds .stat-icon {
        background: linear-gradient(135deg, #f093fb, #f5576c);
      }
    }
  }

  .settlement-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    :deep(.el-tabs__header) {
      margin: 0;
      padding: 0 20px;
      border-bottom: 1px solid #e4e7ed;
    }

    :deep(.el-tabs__nav-wrap) {
      padding: 0;
    }

    :deep(.el-tabs__item) {
      height: 50px;
      line-height: 50px;
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      transition: all 0.3s ease;

      &:hover {
        color: #409eff;
      }

      &.is-active {
        color: #409eff;
        font-weight: 600;
      }
    }

    :deep(.el-tabs__active-bar) {
      height: 3px;
      border-radius: 2px;
    }

    :deep(.el-tabs__content) {
      padding: 20px;
    }
  }

  .tab-badge {
    margin-left: 8px;
  }
}
</style>
