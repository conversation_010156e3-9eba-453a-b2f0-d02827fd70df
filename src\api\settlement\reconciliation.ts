import request from '@/config/axios'

/**
 * 获取对账单列表
 */
export function getReconciliationList(params: any) {
  return request.get({
    url: '/settlement/reconciliation/list',
    params
  })
}

/**
 * 确认对账
 */
export function confirmReconciliation(data: any) {
  return request.post({
    url: '/settlement/reconciliation/confirm',
    data
  })
}

/**
 * 导出对账单
 */
export function exportReconciliation(statementNo: string) {
  return request.download({
    url: `/settlement/reconciliation/export/${statementNo}`
  })
}

/**
 * 导出对账单详情
 */
export function exportReconciliationDetail(statementNo: string) {
  return request.download({
    url: `/settlement/reconciliation/detail/export/${statementNo}`
  })
}

/**
 * 对账单查询参数
 */
export interface ReconciliationQuery {
  statementNo?: string
  agencyName?: string
  status?: string
  startDate?: string
  endDate?: string
  page: number
  size: number
}

/**
 * 对账单信息
 */
export interface ReconciliationStatement {
  id: number
  statementNo: string
  statementType: string
  generationTime: string
  agencyId: number
  agencyName: string
  totalAmount: number
  agencyAmount: number
  platformAmount: number
  agencyRatio: number
  platformRatio: number
  orderCount: number
  orderList: string
  reconciliationStatus: string
  confirmationTime?: string
  paymentTime?: string
  cancellationTime?: string
  cancellationReason?: string
  adjustmentReason?: string
  operatorId?: number
  operatorName?: string
  remark?: string
}

/**
 * 确认对账参数
 */
export interface ConfirmReconciliationParams {
  statementNo: string
  enableAdjustment: boolean
  adjustedAgencyAmount?: number
  adjustedPlatformAmount?: number
  adjustmentReason?: string
}
