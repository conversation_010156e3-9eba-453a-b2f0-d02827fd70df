<!--
  页面名称：发票管理
  功能描述：展示发票管理列表，支持搜索筛选、分页、维护开票、查看详情等操作
-->
<template>
  <div class="invoice-management">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="对账单号">
          <el-input
            v-model="searchForm.statementNo"
            placeholder="请输入对账单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="机构名称">
          <el-input
            v-model="searchForm.agencyName"
            placeholder="请输入机构名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="开票状态">
          <el-select
            v-model="searchForm.invoiceStatus"
            placeholder="所有开票状态"
            clearable
            style="width: 150px"
          >
            <el-option label="所有开票状态" value="" />
            <el-option label="未开票" value="not_invoiced" />
            <el-option label="已开票" value="invoiced" />
            <el-option label="已作废" value="voided" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            筛选
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table v-loading="loading" :data="tableData" style="width: 100%" border stripe>
        <el-table-column prop="statementNo" label="对账单号" width="180" />
        <el-table-column prop="agencyName" label="机构名称" min-width="150" />
        <el-table-column prop="reconciliationAmount" label="对账金额" width="120">
          <template #default="{ row }">
            <span class="amount-text">¥{{ formatAmount(row.reconciliationAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceStatus" label="开票状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getInvoiceStatusType(row.invoiceStatus)" size="small">
              {{ getInvoiceStatusText(row.invoiceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceInfo" label="发票信息" min-width="300">
          <template #default="{ row }">
            <div v-if="row.invoiceStatus === 'invoiced'" class="invoice-info">
              <div>发票号: {{ row.invoiceNo }}</div>
              <div>开票日期: {{ row.invoiceDate }}</div>
              <div>{{ row.invoiceType }}</div>
            </div>
            <div v-else-if="row.invoiceStatus === 'voided'" class="invoice-info voided">
              <div>发票号: {{ row.invoiceNo }} (已作废)</div>
              <div>作废日期: {{ row.voidDate }}</div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleMaintainInvoice(row)">
              维护开票
            </el-button>
            <el-button type="info" size="small" @click="handleViewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 维护开票信息弹窗 -->
    <InvoiceMaintainDialog
      v-model:visible="maintainDialogVisible"
      :invoice-data="currentInvoice"
      @success="handleMaintainSuccess"
    />

    <!-- 发票详情弹窗 -->
    <InvoiceDetailDialog v-model:visible="detailDialogVisible" :invoice-data="currentInvoice" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import InvoiceMaintainDialog from './InvoiceMaintainDialog.vue'
import InvoiceDetailDialog from './InvoiceDetailDialog.vue'

/** 搜索表单数据 */
const searchForm = reactive({
  statementNo: '',
  agencyName: '',
  invoiceStatus: ''
})

/** 表格数据 */
const tableData = ref([])

/** 加载状态 */
const loading = ref(false)

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 维护开票弹窗显示状态 */
const maintainDialogVisible = ref(false)

/** 详情弹窗显示状态 */
const detailDialogVisible = ref(false)

/** 当前选中的发票数据 */
const currentInvoice = ref(null)

/** 获取发票列表 */
const fetchList = async () => {
  loading.value = true
  try {
    // 模拟数据
    tableData.value = [
      {
        statementNo: 'ZD20240816001',
        agencyName: '阳光家政',
        reconciliationAmount: 15680.0,
        invoiceStatus: 'not_invoiced',
        invoiceNo: '',
        invoiceDate: '',
        invoiceType: '',
        voidDate: ''
      },
      {
        statementNo: 'ZD20240815001',
        agencyName: '专业保洁',
        reconciliationAmount: 8900.0,
        invoiceStatus: 'invoiced',
        invoiceNo: '12345678',
        invoiceDate: '2024-08-16',
        invoiceType: '增值税专用发票',
        voidDate: ''
      },
      {
        statementNo: 'ZD20240814001',
        agencyName: '整理专家',
        reconciliationAmount: 25600.0,
        invoiceStatus: 'invoiced',
        invoiceNo: '12345679',
        invoiceDate: '2024-08-15',
        invoiceType: '增值税普通发票',
        voidDate: ''
      },
      {
        statementNo: 'ZD20240813001',
        agencyName: '清洗专家',
        reconciliationAmount: 4500.0,
        invoiceStatus: 'voided',
        invoiceNo: '12345670',
        invoiceDate: '2024-08-13',
        invoiceType: '增值税普通发票',
        voidDate: '2024-08-14'
      }
    ]
    pagination.total = 4
  } catch (error) {
    ElMessage.error('获取发票列表失败')
  } finally {
    loading.value = false
  }
}

/** 处理搜索 */
const handleSearch = () => {
  pagination.page = 1
  fetchList()
}

/** 处理重置 */
const handleReset = () => {
  Object.assign(searchForm, {
    statementNo: '',
    agencyName: '',
    invoiceStatus: ''
  })
  pagination.page = 1
  fetchList()
}

/** 处理维护开票 */
const handleMaintainInvoice = (row: any) => {
  currentInvoice.value = row
  maintainDialogVisible.value = true
}

/** 处理查看详情 */
const handleViewDetail = (row: any) => {
  currentInvoice.value = row
  detailDialogVisible.value = true
}

/** 处理维护成功 */
const handleMaintainSuccess = () => {
  maintainDialogVisible.value = false
  fetchList()
  ElMessage.success('开票信息保存成功')
}

/** 处理分页大小变化 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

/** 处理当前页变化 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

/** 获取开票状态类型 */
const getInvoiceStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    not_invoiced: 'warning',
    invoiced: 'success',
    voided: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取开票状态文本 */
const getInvoiceStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    not_invoiced: '未开票',
    invoiced: '已开票',
    voided: '已作废'
  }
  return statusMap[status] || status
}

/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount?.toFixed(2) || '0.00'
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.invoice-management {
  .search-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .search-form {
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 20px;
      }
    }
  }

  .table-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .amount-text {
      color: #f56c6c;
      font-weight: 600;
    }

    .invoice-info {
      font-size: 12px;
      line-height: 1.5;
      color: #606266;

      &.voided {
        color: #f56c6c;
      }
    }

    .pagination-section {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
