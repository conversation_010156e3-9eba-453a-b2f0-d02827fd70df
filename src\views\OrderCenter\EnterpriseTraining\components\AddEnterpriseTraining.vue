<!--
  页面名称：企业培训订单新增/编辑表单
  功能描述：新增/编辑企业培训订单，支持关联商机、关联线索自动填充，电子合同和纸质合同共用一个附件
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="drawerTitle"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="top"
      class="order-form"
    >
      <!-- 订单信息 -->
      <div class="form-section">
        <div class="section-title">订单信息</div>

        <!-- 关联商机 -->
        <el-form-item label="关联商机" prop="businessOpportunity">
          <el-select
            v-model="form.businessOpportunity"
            placeholder="请选择关联商机 (可选)"
            style="width: 100%"
            @change="handleBusinessOpportunityChange"
          >
            <el-option label="请选择关联商机 (可选)" value="" />
            <el-option
              v-for="opportunity in businessOpportunities"
              :key="opportunity.id"
              :label="`${opportunity.name} - ${opportunity.customerName}`"
              :value="opportunity.id"
            />
          </el-select>

          <!-- 商机信息展示 -->
          <div v-if="selectedOpportunity" class="info-box business-opportunity-info">
            <div class="info-header">
              <el-icon class="info-icon"><InfoFilled /></el-icon>
              <span class="info-title">商机信息已自动填充</span>
            </div>
            <div class="info-content">
              <div class="info-item">
                <span class="label">商机描述：</span>
                <span class="value">{{ selectedOpportunity.name }}</span>
              </div>
              <div class="info-item">
                <span class="label">预估金额：</span>
                <span class="value amount">¥{{ selectedOpportunity.totalPrice }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">2024-05-15</span>
              </div>
              <div class="info-item">
                <span class="label">商机状态：</span>
                <el-tag
                  :type="getOpportunityStatusType(selectedOpportunity.businessStage)"
                  size="small"
                >
                  {{ selectedOpportunity.businessStage }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="form-tip">选择关联商机可自动填充部分企业信息</div>
        </el-form-item>

        <!-- 关联线索 -->
        <el-form-item label="关联线索" prop="lead">
          <el-select
            v-model="form.lead"
            placeholder="请选择关联线索 (可选)"
            style="width: 100%"
            @change="handleLeadChange"
          >
            <el-option label="请选择关联线索 (可选)" value="" />
            <el-option
              v-for="lead in leads"
              :key="lead.id"
              :label="`${lead.leadId} - ${lead.customerName} ${lead.customerPhone}`"
              :value="lead.id"
            />
          </el-select>

          <!-- 线索信息展示 -->
          <div v-if="selectedLead" class="info-box lead-info">
            <div class="info-header">
              <el-icon class="info-icon lead-icon"><InfoFilled /></el-icon>
              <span class="info-title">线索信息已自动填充</span>
            </div>
            <div class="info-content">
              <div class="info-item">
                <span class="label">联系人：</span>
                <span class="value">{{ selectedLead.customerName }}</span>
              </div>
              <div class="info-item">
                <span class="label">联系电话：</span>
                <span class="value">{{ selectedLead.customerPhone }}</span>
              </div>
              <div class="info-item">
                <span class="label">线索来源：</span>
                <span class="value">{{ selectedLead.leadSource }}</span>
              </div>
              <div class="info-item">
                <span class="label">线索状态：</span>
                <el-tag :type="getLeadStatusType(selectedLead.leadStatus)" size="small">
                  {{ selectedLead.leadStatus }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="form-tip">选择关联线索可自动填充部分企业信息</div>
        </el-form-item>

        <!-- 企业名称 -->
        <el-form-item label="企业名称 *" prop="companyName">
          <el-input
            v-model="form.companyName"
            placeholder="请输入企业名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <!-- 培训项目 -->
        <el-form-item label="培训项目 *" prop="trainingProject">
          <el-input
            v-model="form.trainingProject"
            placeholder="请输入培训项目"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <!-- 培训周期 -->
        <el-form-item label="培训周期 *" prop="trainingPeriod">
          <el-date-picker
            v-model="form.trainingPeriod"
            type="daterange"
            range-separator="-"
            start-placeholder="年-月-日"
            end-placeholder="年-月-日"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <!-- 培训人数 -->
        <el-form-item label="培训人数 *" prop="traineeCount">
          <el-input-number
            v-model="form.traineeCount"
            :min="1"
            :max="1000"
            placeholder="请输入培训人数"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 订单金额 -->
        <el-form-item label="订单金额 *" prop="orderAmount">
          <el-input
            v-model.number="form.orderAmount"
            placeholder="请输入订单金额"
            type="number"
            min="0"
            style="width: 100%"
          >
            <template #prefix>¥</template>
          </el-input>
        </el-form-item>

        <!-- 支付状态 -->
        <el-form-item label="支付状态 *" prop="paymentStatus">
          <el-select v-model="form.paymentStatus" placeholder="请选择支付状态" style="width: 100%">
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已退款" value="refunded" />
          </el-select>
          <div class="form-tip">选择"已支付"后将显示收款信息填写表单</div>
        </el-form-item>

        <!-- 支付信息 - 当支付状态为"已支付"时显示 -->
        <div v-if="form.paymentStatus === 'paid'" class="form-section payment-section">
          <div class="section-title">
            <el-icon class="section-icon"><Wallet /></el-icon>
            支付信息
          </div>

          <!-- 收款金额 -->
          <el-form-item label="收款金额 *" prop="collectionAmount">
            <el-input
              v-model.number="form.collectionAmount"
              placeholder="请输入实际收款金额"
              type="number"
              min="0"
              style="width: 100%"
            >
              <template #prefix>¥</template>
            </el-input>
            <div class="form-tip">建议收款金额与订单金额保持一致</div>
          </el-form-item>

          <!-- 收款方式 -->
          <el-form-item label="收款方式 *" prop="collectionMethod">
            <el-select
              v-model="form.collectionMethod"
              placeholder="请选择收款方式"
              style="width: 100%"
            >
              <el-option label="现金" value="cash" />
              <el-option label="微信支付" value="wechat" />
              <el-option label="支付宝" value="alipay" />
              <el-option label="银行转账" value="bank_transfer" />
              <el-option label="POS机刷卡" value="pos" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>

          <!-- 收款日期 -->
          <el-form-item label="收款日期 *" prop="collectionDate">
            <el-date-picker
              v-model="form.collectionDate"
              type="date"
              placeholder="年-月-日 --:--"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>

          <!-- 操作人 -->
          <el-form-item label="操作人 *" prop="operatorName">
            <el-input
              v-model="form.operatorName"
              placeholder="请输入操作人姓名"
              maxlength="50"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 收款备注 -->
          <el-form-item label="收款备注" prop="collectionRemark">
            <el-input
              v-model="form.collectionRemark"
              type="textarea"
              :rows="3"
              placeholder="请输入收款备注信息(可选)"
              maxlength="200"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </div>

        <!-- 我方负责人 -->
        <el-form-item label="我方负责人 *" prop="manager">
          <el-input v-model="form.manager" placeholder="请输入我方负责人" maxlength="50" />
        </el-form-item>

        <!-- 合同类型 -->
        <el-form-item label="合同类型" prop="contractType">
          <el-radio-group v-model="form.contractType">
            <el-radio value="electronic">电子合同</el-radio>
            <el-radio value="paper">纸质合同</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 附件管理 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon class="section-icon"><Document /></el-icon>
            附件管理
          </div>

          <!-- 附件上传 -->
          <el-form-item label="上传附件">
            <el-upload
              ref="attachmentUploadRef"
              action="#"
              :auto-upload="false"
              :on-change="handleAttachmentChange"
              :on-remove="handleAttachmentRemove"
              :file-list="attachmentList"
              :limit="5"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx"
              class="attachment-upload"
            >
              <el-button type="primary" size="small">
                <el-icon><Plus /></el-icon>
                选择附件
              </el-button>
              <template #tip>
                <div class="upload-tip"
                  >支持格式: PDF、Word、Excel、JPG、PNG, 单个文件不超过10MB, 最多5个文件</div
                >
              </template>
            </el-upload>
          </el-form-item>

          <!-- 附件列表展示 -->
          <div v-if="attachmentList.length > 0" class="attachment-list">
            <div class="attachment-header">
              <span class="attachment-title">已选择附件 ({{ attachmentList.length }}/5)</span>
              <div v-if="form.contractFileUrl" class="contract-url-info">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
                <el-tag type="success" size="small">合同附件已上传</el-tag>
                <span class="url-text">{{ form.contractFileUrl }}</span>
              </div>
            </div>
            <div class="attachment-items">
              <div v-for="(file, index) in attachmentList" :key="index" class="attachment-item">
                <div class="attachment-info">
                  <el-icon class="file-icon"><Document /></el-icon>
                  <span class="file-name">{{ file.name }}</span>
                  <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
                  <el-tag
                    :type="
                      file.status === 'success'
                        ? 'success'
                        : file.status === 'fail'
                          ? 'danger'
                          : 'warning'
                    "
                    size="small"
                  >
                    {{
                      file.status === 'success'
                        ? '上传成功'
                        : file.status === 'fail'
                          ? '上传失败'
                          : '上传中'
                    }}
                  </el-tag>
                </div>
                <div class="attachment-actions">
                  <el-button type="danger" size="small" @click="removeAttachment(index)">
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled, Document, Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import {
  EnterpriseTrainingOrderApi,
  type CreateEnterpriseTrainingOrderParams,
  type UpdateEnterpriseTrainingOrderParams
} from '@/api/OrderCenter/EnterpriseTraining'
import request from '@/config/axios' // 修复导入路径

// 本地类型定义
interface EnterpriseTrainingOrder {
  id?: number
  orderNumber?: string
  companyName: string
  trainingProject: string
  traineeCount: number
  trainingPeriod: string[]
  orderAmount: number
  orderStatus: string
  paymentStatus: string
  manager: string
  businessOpportunity: string
  lead: string
  contractType: string
  contractFileUrl: string
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  operatorName?: string
  collectionRemark?: string
}

// Props
interface Props {
  visible: boolean
  editData?: EnterpriseTrainingOrder | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: null
})

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const loading = ref(false)
const attachmentUploadRef = ref<any>()
const attachmentList = ref<any[]>([])
const uploadingAttachments = ref(false)

// 表单数据
const form = reactive<EnterpriseTrainingOrder>({
  companyName: '',
  trainingProject: '',
  traineeCount: 1,
  trainingPeriod: ['', ''],
  orderAmount: 0,
  orderStatus: 'draft',
  paymentStatus: 'pending',
  manager: '',
  businessOpportunity: '',
  lead: '',
  contractType: 'electronic',
  contractFileUrl: '',
  collectionAmount: 0,
  collectionMethod: '',
  collectionDate: '',
  operatorName: '',
  collectionRemark: ''
})

// 表单验证规则
const rules = reactive({
  companyName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  trainingProject: [{ required: true, message: '请输入培训项目', trigger: 'blur' }],
  trainingPeriod: [{ required: true, message: '请选择培训周期', trigger: 'change' }],
  orderAmount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { type: 'number' as const, min: 0, message: '订单金额必须大于等于0', trigger: 'blur' }
  ],
  manager: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
  collectionAmount: [
    {
      required: false,
      message: '请输入收款金额',
      trigger: 'blur',
      validator: (rule: any, value: any, callback: any) => {
        if (form.paymentStatus === 'paid' && (!value || value <= 0)) {
          callback(new Error('收款金额必须大于0'))
        } else {
          callback()
        }
      }
    },
    { type: 'number' as const, min: 0, message: '收款金额必须大于等于0', trigger: 'blur' }
  ],
  collectionMethod: [
    {
      required: false,
      message: '请选择收款方式',
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (form.paymentStatus === 'paid' && !value) {
          callback(new Error('请选择收款方式'))
        } else {
          callback()
        }
      }
    }
  ],
  collectionDate: [
    {
      required: false,
      message: '请选择收款日期',
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (form.paymentStatus === 'paid' && !value) {
          callback(new Error('请输入收款日期'))
        } else {
          callback()
        }
      }
    }
  ],
  operatorName: [
    {
      required: false,
      message: '请输入操作员姓名',
      trigger: 'blur',
      validator: (rule: any, value: any, callback: any) => {
        if (form.paymentStatus === 'paid' && !value) {
          callback(new Error('请输入操作员姓名'))
        } else {
          callback()
        }
      }
    }
  ]
})

// 商机信息
const businessOpportunities = ref<any[]>([])
const selectedOpportunity = ref<any>(null)

// 线索信息
const leads = ref<any[]>([])
const selectedLead = ref<any>(null)

// 计算属性
const drawerTitle = computed(() => {
  return props.editData ? '编辑企业培训订单' : '新建企业培训订单'
})

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      nextTick(async () => {
        if (props.editData) {
          console.log('编辑模式，编辑数据:', props.editData)

          try {
            // 编辑模式，先获取最新详情数据
            if (props.editData.id) {
              console.log('开始获取订单详情...')
              const orderDetail = await fetchOrderDetail(props.editData.id)
              if (orderDetail) {
                console.log('获取到订单详情:', orderDetail)
                // 使用最新的详情数据更新编辑数据
                Object.assign(props.editData, orderDetail)
              }
            }

            // 然后获取下拉数据
            await fetchDropdownData()

            // 填充表单数据 - 完整映射所有字段
            Object.assign(form, {
              // 关联信息
              businessOpportunity:
                typeof props.editData.businessOpportunity === 'object' &&
                (props.editData.businessOpportunity as any)?.id
                  ? (props.editData.businessOpportunity as any).id
                  : props.editData.businessOpportunity || '',
              lead:
                typeof props.editData.lead === 'object' && (props.editData.lead as any)?.id
                  ? (props.editData.lead as any).id
                  : props.editData.lead || '',

              // 基本信息
              companyName:
                props.editData.companyName || (props.editData as any).enterpriseName || '',
              trainingProject:
                props.editData.trainingProject || (props.editData as any).projectName || '',

              // 培训周期处理 - 支持多种格式
              trainingPeriod: (() => {
                const period = props.editData.trainingPeriod
                if (period) {
                  if (Array.isArray(period)) {
                    return period
                  } else if (typeof period === 'string') {
                    if ((period as string).includes(' - ')) {
                      return (period as string).split(' - ')
                    } else if ((period as string).includes('~')) {
                      return (period as string).split('~')
                    } else {
                      return [period as string, '']
                    }
                  }
                }
                return ['', '']
              })(),

              // 培训人数
              traineeCount:
                props.editData.traineeCount || (props.editData as any).participantsCount || 1,

              // 订单金额 - 支持多种字段名
              orderAmount:
                props.editData.orderAmount ||
                (props.editData as any).totalFee ||
                (props.editData as any).totalAmount ||
                0,

              // 订单状态
              orderStatus: props.editData.orderStatus || (props.editData as any).status || 'draft',

              // 支付状态
              paymentStatus: props.editData.paymentStatus || 'pending',

              // 负责人
              manager:
                props.editData.manager ||
                (props.editData as any).managerName ||
                (props.editData as any).contactPerson ||
                '',

              // 合同相关
              contractType: props.editData.contractType || 'electronic',
              contractFileUrl:
                props.editData.contractFileUrl || (props.editData as any).contractFile || '',

              // 收款信息
              collectionAmount: props.editData.collectionAmount || 0,
              collectionMethod: props.editData.collectionMethod || '',
              collectionDate: props.editData.collectionDate || '',
              operatorName: props.editData.operatorName || '',
              collectionRemark: props.editData.collectionRemark || '',

              // 其他字段
              orderNumber: props.editData.orderNumber || (props.editData as any).orderNo || '',
              createTime:
                (props.editData as any).createTime || (props.editData as any).createdAt || '',
              updateTime:
                (props.editData as any).updateTime || (props.editData as any).updatedAt || '',

              // 商机相关字段
              opportunityId:
                (props.editData as any).opportunityId ||
                (props.editData.businessOpportunity as any)?.id ||
                '',
              opportunityName:
                (props.editData as any).opportunityName ||
                (props.editData.businessOpportunity as any)?.name ||
                '',
              customerName:
                (props.editData as any).customerName ||
                (props.editData.businessOpportunity as any)?.customerName ||
                '',
              businessType:
                (props.editData as any).businessType ||
                (props.editData.businessOpportunity as any)?.businessType ||
                '',
              businessStage:
                (props.editData as any).businessStage ||
                (props.editData.businessOpportunity as any)?.businessStage ||
                '',

              // 线索相关字段
              leadId: (props.editData as any).leadId || (props.editData.lead as any)?.id || '',
              leadName:
                (props.editData as any).leadName || (props.editData.lead as any)?.name || '',
              contactPhone:
                (props.editData as any).contactPhone || (props.editData.lead as any)?.phone || '',
              leadSource:
                (props.editData as any).leadSource || (props.editData.lead as any)?.source || '',
              leadStatus:
                (props.editData as any).leadStatus || (props.editData.lead as any)?.status || ''
            })

            console.log('表单数据填充完成:', form)

            // 加载关联信息（此时下拉数据已经获取完成）
            if (props.editData?.businessOpportunity) {
              console.log('处理商机关联信息:', props.editData.businessOpportunity)
              console.log('当前商机列表:', businessOpportunities.value)

              // 处理商机信息 - 统一使用下拉数据格式
              let opportunity
              if (
                typeof props.editData.businessOpportunity === 'object' &&
                (props.editData.businessOpportunity as any)?.id
              ) {
                // 如果是对象格式，从下拉列表中查找对应的完整数据
                const opportunityId = (props.editData.businessOpportunity as any).id
                opportunity = businessOpportunities.value.find(
                  (item) => item.id.toString() === opportunityId.toString()
                )
                // 设置表单值为ID
                form.businessOpportunity = opportunityId
              } else {
                // 如果是ID格式，从下拉列表中查找
                opportunity = businessOpportunities.value.find(
                  (item) => item.id.toString() === props.editData!.businessOpportunity.toString()
                )
              }

              if (opportunity) {
                console.log('找到对应商机:', opportunity)
                selectedOpportunity.value = opportunity
              } else {
                console.warn('未找到对应的商机信息，ID:', props.editData!.businessOpportunity)
                selectedOpportunity.value = null
                // 清空表单中的商机选择
                form.businessOpportunity = ''
              }
            }

            if (props.editData?.lead) {
              console.log('处理线索关联信息:', props.editData.lead)
              console.log('当前线索列表:', leads.value)

              // 处理线索信息 - 统一使用下拉数据格式
              let lead
              if (typeof props.editData.lead === 'object' && (props.editData.lead as any)?.id) {
                // 如果是对象格式，从下拉列表中查找对应的完整数据
                const leadId = (props.editData.lead as any).id
                lead = leads.value.find((item) => item.id.toString() === leadId.toString())
                // 设置表单值为ID
                form.lead = leadId
              } else {
                // 如果是ID格式，从下拉列表中查找
                lead = leads.value.find(
                  (item) => item.id.toString() === props.editData!.lead.toString()
                )
              }

              if (lead) {
                console.log('找到对应线索:', lead)
                selectedLead.value = lead
              } else {
                console.warn('未找到对应的线索信息，ID:', props.editData!.lead)
                selectedLead.value = null
                // 清空表单中的线索选择
                form.lead = ''
              }
            }

            // 如果有合同文件URL，处理合同附件的显示
            if (props.editData.contractFileUrl) {
              console.log('合同文件URL:', props.editData.contractFileUrl)
              // 将合同文件URL添加到附件列表中显示
              const contractFile = {
                uid: Date.now(),
                name: '合同附件',
                status: 'success',
                url: props.editData.contractFileUrl
              }
              attachmentList.value = [contractFile]
            }
          } catch (error) {
            console.error('编辑模式数据加载失败:', error)
            ElMessage.error('加载订单详情失败，请重试')
          }
        } else {
          // 新增模式，重置表单
          resetForm()
        }
      })
    }
  }
)

// 监听支付状态变化
watch(
  () => form.paymentStatus,
  (newStatus) => {
    if (newStatus === 'paid') {
      // 当支付状态变为"已支付"时，设置默认值
      if (!form.collectionAmount) {
        form.collectionAmount = form.orderAmount
      }
      if (!form.collectionDate) {
        form.collectionDate = new Date().toISOString().slice(0, 10)
      }
      if (!form.operatorName) {
        form.operatorName = form.manager
      }
    } else {
      // 当支付状态变为其他状态时，清空支付信息
      form.collectionAmount = 0
      form.collectionMethod = ''
      form.collectionDate = ''
      form.operatorName = ''
      form.collectionRemark = ''
    }
  }
)

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    companyName: '',
    trainingProject: '',
    traineeCount: 1,
    trainingPeriod: ['', ''],
    orderAmount: 0,
    orderStatus: 'draft',
    paymentStatus: 'pending',
    manager: '',
    businessOpportunity: '',
    lead: '',
    contractType: '',
    contractFileUrl: '',
    collectionAmount: 0,
    collectionMethod: '',
    collectionDate: '',
    operatorName: '',
    collectionRemark: ''
  })
  attachmentList.value = []
  selectedOpportunity.value = null
  selectedLead.value = null
  formRef.value?.clearValidate()
}

// 商机选择变化处理
const handleBusinessOpportunityChange = async (value: string) => {
  console.log('商机选择变化:', value)
  console.log('当前商机列表:', businessOpportunities.value)

  if (value) {
    try {
      // 修复ID类型匹配问题
      const opportunity = businessOpportunities.value.find(
        (item) => item.id.toString() === value.toString()
      )
      console.log('找到的商机:', opportunity)

      if (opportunity) {
        selectedOpportunity.value = opportunity
        // 自动填充企业信息 - 使用Object.assign确保响应式更新
        Object.assign(form, {
          companyName: opportunity.customerName || '',
          trainingProject: opportunity.name || ''
        })

        console.log('自动填充后的表单数据:', {
          companyName: form.companyName,
          trainingProject: form.trainingProject
        })

        // 强制触发响应式更新
        await nextTick()

        // 再次确认数据已更新
        console.log('nextTick后的表单数据:', {
          companyName: form.companyName,
          trainingProject: form.trainingProject
        })

        ElMessage.success('商机信息已自动填充')
      } else {
        console.warn('未找到对应的商机信息')
        selectedOpportunity.value = null
      }
    } catch (error) {
      console.error('获取商机信息失败:', error)
      ElMessage.error('获取商机信息失败')
      selectedOpportunity.value = null
    }
  } else {
    selectedOpportunity.value = null
    console.log('清空商机选择')
  }
}

// 线索选择变化处理
const handleLeadChange = async (value: string) => {
  console.log('线索选择变化:', value)
  console.log('当前线索列表:', leads.value)

  if (value) {
    try {
      // 修复ID类型匹配问题
      const lead = leads.value.find((item) => item.id.toString() === value.toString())
      console.log('找到的线索:', lead)

      if (lead) {
        selectedLead.value = lead
        // 自动填充企业信息 - 使用Object.assign确保响应式更新
        Object.assign(form, {
          companyName: lead.customerName || ''
        })

        console.log('自动填充后的表单数据:', {
          companyName: form.companyName
        })

        // 强制触发响应式更新
        await nextTick()

        // 再次确认数据已更新
        console.log('nextTick后的表单数据:', {
          companyName: form.companyName
        })

        ElMessage.success('线索信息已自动填充')
      } else {
        console.warn('未找到对应的线索信息')
        selectedLead.value = null
      }
    } catch (error) {
      console.error('获取线索信息失败:', error)
      ElMessage.error('获取线索信息失败')
      selectedLead.value = null
    }
  } else {
    selectedLead.value = null
    console.log('清空线索选择')
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 附件上传处理
const handleAttachmentChange = async (file: any, fileList: any[]) => {
  console.log('附件选择变化:', file, fileList)
  attachmentList.value = fileList

  // 如果选择了新文件，自动上传
  if (file && file.raw && file.status === 'ready') {
    try {
      await uploadSingleAttachment(file)
    } catch (error) {
      console.error('文件上传失败:', error)
      // 从列表中移除上传失败的文件
      const index = attachmentList.value.findIndex((f) => f.uid === file.uid)
      if (index > -1) {
        attachmentList.value.splice(index, 1)
      }
    }
  }
}

// 附件移除处理
const handleAttachmentRemove = (file: any, fileList: any[]) => {
  console.log('附件移除:', file, fileList)
  attachmentList.value = fileList

  // 如果移除的是合同附件，清空合同文件URL
  if (file && file.url === form.contractFileUrl) {
    form.contractFileUrl = ''
    console.log('已清空合同文件URL')
  }
}

// 移除指定索引的附件
const removeAttachment = (index: number) => {
  const removedFile = attachmentList.value[index]
  attachmentList.value.splice(index, 1)

  // 如果移除的是合同附件，清空合同文件URL
  if (removedFile && removedFile.url === form.contractFileUrl) {
    form.contractFileUrl = ''
    console.log('已清空合同文件URL')
  }
}

// 上传单个附件到服务器 - 使用高校实践订单的接口地址
const uploadSingleAttachment = async (file: any) => {
  if (!file.raw) {
    throw new Error(`文件 ${file.name} 数据不完整`)
  }

  const formData = new FormData()
  formData.append('file', file.raw)
  formData.append('directory', 'enterprise-training-order')

  try {
    // 使用request.postOriginal直接调用文件上传接口
    const res = await request.postOriginal({
      url: '/infra/file/upload',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })

    console.log('文件上传响应:', res)

    // 根据接口返回格式：{ "code": 0, "data": "https://...", "msg": "" }
    const fileUrl = res.data

    if (res.data && fileUrl && typeof fileUrl === 'string' && /^https?:\/\//.test(fileUrl)) {
      // 上传成功，更新文件状态和URL
      file.url = fileUrl
      file.status = 'success'

      // 保存合同文件URL到表单数据中
      form.contractFileUrl = fileUrl

      ElMessage.success(`附件 ${file.name} 上传成功，合同文件URL已保存`)
      console.log('合同文件URL已保存:', fileUrl)
      return { success: true, fileUrl, fileName: file.name }
    } else {
      throw new Error('文件上传失败：接口返回数据格式错误')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    file.status = 'fail'
    throw error
  }
}

// 获取订单详情
const fetchOrderDetail = async (id: number) => {
  try {
    const result = await EnterpriseTrainingOrderApi.getOrderDetail(id, 1) // 这里应该从用户信息或store中获取租户ID
    console.log('获取到订单详情:', result)
    return result
  } catch (error) {
    console.error('获取订单详情失败:', error)
    return null
  }
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 准备提交数据
    const submitData = {
      ...form,
      orderAmount: Number(form.orderAmount),
      trainingPeriod: form.trainingPeriod.join(' - '),
      orderStatus: 'pending_approval' // 新增订单默认为待审批状态
    }

    // 只有当支付状态为"已支付"时才添加收款信息
    if (form.paymentStatus === 'paid') {
      submitData.collectionAmount = Number(form.collectionAmount)
      submitData.collectionMethod = form.collectionMethod
      submitData.collectionDate = form.collectionDate
      submitData.operatorName = form.operatorName
      submitData.collectionRemark = form.collectionRemark

      console.log('收款信息已添加到提交数据:', {
        collectionAmount: submitData.collectionAmount,
        collectionMethod: submitData.collectionMethod,
        collectionDate: submitData.collectionDate,
        operatorName: submitData.operatorName,
        collectionRemark: submitData.collectionRemark
      })
    } else {
      // 如果支付状态不是"已支付"，清空收款信息
      submitData.collectionAmount = undefined
      submitData.collectionMethod = undefined
      submitData.collectionDate = undefined
      submitData.operatorName = undefined
      submitData.collectionRemark = undefined

      console.log('支付状态不是"已支付"，清空收款信息')
    }

    console.log('最终提交数据:', submitData)

    if (props.editData?.id) {
      // 编辑模式 - 调用更新API
      console.log('=== 编辑模式开始 ===')
      console.log('编辑数据ID:', props.editData.id)
      console.log('表单数据:', form)

      const updateParams: UpdateEnterpriseTrainingOrderParams = {
        id: props.editData.id,
        tenantId: 1, // 这里应该从用户信息或store中获取
        enterpriseName: submitData.companyName,
        trainingProject: submitData.trainingProject,
        participantsCount: submitData.traineeCount,
        trainingDuration: submitData.trainingPeriod,
        totalFee: submitData.orderAmount, // 总费用
        managerId: 1, // 这里应该从用户信息或store中获取
        managerName: submitData.manager,
        contractFileUrl: form.contractFileUrl || '', // 附件URL
        // 收款信息
        collectionAmount: submitData.collectionAmount,
        collectionMethod: submitData.collectionMethod,
        collectionDate: submitData.collectionDate,
        operatorName: submitData.operatorName,
        collectionRemark: submitData.collectionRemark
      }

      console.log('更新参数:', updateParams)
      console.log('开始调用更新API...')

      try {
        const result = await EnterpriseTrainingOrderApi.updateOrder(updateParams)
        console.log('更新API调用成功，结果:', result)
        ElMessage.success('编辑成功')
      } catch (apiError: any) {
        console.error('更新API调用失败:', apiError)
        ElMessage.error(`编辑失败: ${apiError.message || '未知错误'}`)
        throw apiError // 重新抛出错误，让外层catch处理
      }
    } else {
      // 新增模式 - 调用创建API
      const createParams: CreateEnterpriseTrainingOrderParams = {
        tenantId: 1, // 这里应该从用户信息或store中获取
        opportunityId: submitData.businessOpportunity || undefined,
        leadId: submitData.lead || undefined,
        enterpriseName: submitData.companyName,
        enterpriseContact: '临时联系人', // 临时赋值
        enterprisePhone: '010-12345678', // 临时赋值
        enterpriseEmail: '<EMAIL>', // 临时赋值
        enterpriseAddress: '北京市朝阳区临时地址', // 临时赋值
        trainingProject: submitData.trainingProject,
        trainingDescription: '企业培训项目，具体内容待完善', // 临时赋值
        participantsCount: submitData.traineeCount,
        trainingDuration: submitData.trainingPeriod,
        trainingLocation: '培训地点待定', // 临时赋值
        trainingType: 'management_training', // 临时赋值，使用接口文档中的枚举值
        perPersonFee: Number((submitData.orderAmount / submitData.traineeCount).toFixed(2)), // 人均费用，保留两位小数
        totalFee: submitData.orderAmount,
        materialFee: 0, // 材料费，默认0
        certificationFee: 0, // 认证费，默认0
        managerId: 1, // 这里应该从用户信息或store中获取
        managerName: submitData.manager,
        managerPhone: '13800138000', // 临时赋值
        remark: '企业培训订单，信息待完善', // 临时赋值
        contractFileUrl: form.contractFileUrl || '', // 附件URL
        // 收款信息
        collectionAmount: submitData.collectionAmount,
        collectionMethod: submitData.collectionMethod,
        collectionDate: submitData.collectionDate,
        operatorName: submitData.operatorName,
        collectionRemark: submitData.collectionRemark
      }

      console.log('创建参数:', createParams)
      console.log('开始调用创建API...')

      try {
        const result = await EnterpriseTrainingOrderApi.createOrder(createParams)
        console.log('创建API调用成功，结果:', result)
        ElMessage.success('新增成功')
      } catch (apiError: any) {
        console.error('创建API调用失败:', apiError)
        ElMessage.error(`新增失败: ${apiError.message || '未知错误'}`)
        throw apiError // 重新抛出错误，让外层catch处理
      }
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error(props.editData ? '编辑失败' : '新增失败')
  } finally {
    submitting.value = false
  }
}

// 获取商机状态类型
const getOpportunityStatusType = (status: string) => {
  switch (status) {
    case '跟进中':
      return 'info'
    case '已成交':
      return 'success'
    case '已失效':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取线索状态类型
const getLeadStatusType = (status: string) => {
  switch (status) {
    case '已转化':
      return 'success'
    case '已失效':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取下拉数据
const fetchDropdownData = async () => {
  console.log('=== fetchDropdownData 开始 ===')
  console.log('当前商机列表:', businessOpportunities.value)
  console.log('当前线索列表:', leads.value)

  try {
    loading.value = true
    console.log('开始获取下拉数据...')

    // 尝试调用API获取数据
    try {
      console.log('开始调用 EnterpriseTrainingOrderApi.getDropdownData...')
      const result = await EnterpriseTrainingOrderApi.getDropdownData({
        orderType: 'enterprise_training',
        businessLine: '企业培训'
      })

      console.log('API返回结果:', result)
      console.log('商机选项:', result.businessOptions)
      console.log('线索选项:', result.leadOptions)

      // 如果API返回了商机数据，则使用API数据
      if (Array.isArray(result.businessOptions) && result.businessOptions.length > 0) {
        businessOpportunities.value = result.businessOptions
        console.log('使用API返回的商机数据，数量:', result.businessOptions.length)
        console.log('商机数据详情:', result.businessOptions)
      } else {
        // 使用静态数据作为备用
        businessOpportunities.value = [
          {
            id: 1,
            name: 'ABC科技数字化转型项目',
            customerName: 'ABC科技有限公司',
            businessType: '企业培训',
            totalPrice: 150000,
            businessStage: '跟进中',
            ownerUserName: '张三'
          },
          {
            id: 2,
            name: 'XYZ制造精益生产项目',
            customerName: 'XYZ制造集团',
            businessType: '企业培训',
            totalPrice: 180000,
            businessStage: '跟进中',
            ownerUserName: '李四'
          }
        ]
        console.log('使用静态商机数据')
      }

      // 如果API返回了线索数据，则使用API数据；否则使用静态数据
      if (Array.isArray(result.leadOptions) && result.leadOptions.length > 0) {
        leads.value = result.leadOptions
        console.log('使用API返回的线索数据')
      } else {
        // 使用静态数据作为备用
        leads.value = [
          {
            id: 1,
            leadId: 'LEAD001',
            customerName: '陈经理',
            customerPhone: '135****7890',
            businessModule: '企业培训',
            leadSource: '企业咨询',
            leadStatus: '已转化'
          },
          {
            id: 2,
            leadId: 'LEAD002',
            customerName: '李经理',
            customerPhone: '138****5678',
            businessModule: '企业培训',
            leadSource: '企业咨询',
            leadStatus: '已转化'
          }
        ]
        console.log('使用静态线索数据')
      }
    } catch (apiError) {
      console.warn('API调用失败，使用静态数据:', apiError)
      // 使用静态数据作为备用
      businessOpportunities.value = [
        {
          id: 1,
          name: 'ABC科技数字化转型项目',
          customerName: 'ABC科技有限公司',
          businessType: '企业培训',
          totalPrice: 150000,
          businessStage: '跟进中',
          ownerUserName: '张三'
        },
        {
          id: 2,
          name: 'XYZ制造精益生产项目',
          customerName: 'XYZ制造集团',
          businessType: '企业培训',
          totalPrice: 180000,
          businessStage: '跟进中',
          ownerUserName: '李四'
        }
      ]
      leads.value = [
        {
          id: 1,
          leadId: 'LEAD001',
          customerName: '陈经理',
          customerPhone: '135****7890',
          businessModule: '企业培训',
          leadSource: '企业咨询',
          leadStatus: '已转化'
        },
        {
          id: 2,
          leadId: 'LEAD002',
          customerName: '李经理',
          customerPhone: '138****5678',
          businessModule: '企业培训',
          leadSource: '企业咨询',
          leadStatus: '已转化'
        }
      ]
    }

    console.log('最终商机列表:', businessOpportunities.value)
    console.log('最终线索列表:', leads.value)
  } catch (error) {
    console.error('获取下拉数据失败:', error)
    ElMessage.error('获取下拉数据失败')
  } finally {
    loading.value = false
  }
}

// 监听抽屉显示状态，每次打开时获取下拉数据
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      console.log('抽屉打开，获取下拉数据')
      fetchDropdownData()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.order-form {
  padding: 0 20px;
}

.form-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    display: flex;
    align-items: center;

    .section-icon {
      margin-right: 8px;
      font-size: 18px;
      color: #409eff;
    }
  }
}

.info-box {
  margin-top: 12px;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid;
  width: 100%;
  box-sizing: border-box;

  .info-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
    width: 100%;

    .info-icon {
      margin-right: 6px;
      font-size: 16px;
      flex-shrink: 0;
    }
  }

  .info-content {
    width: 100%;

    .info-item {
      display: flex;
      margin-bottom: 4px;
      font-size: 13px;
      line-height: 1.4;
      width: 100%;

      .label {
        color: #606266;
        min-width: 70px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .value {
        color: #303133;
        flex: 1;
        word-break: break-all;
      }

      .amount {
        font-weight: bold;
        color: #f56c6c;
      }

      .status-converted {
        color: #f56c6c;
        font-weight: 500;
      }
    }
  }

  &.business-opportunity-info {
    background: #f0f9ff;
    border-left-color: #409eff;
    color: #409eff;

    .info-header {
      color: #409eff;
    }
  }

  &.lead-info {
    background: #fdf4ff;
    border-left-color: #a855f7;
    color: #a855f7;

    .info-header {
      color: #a855f7;
    }
  }

  &.payment-section {
    background: #f0f9eb;
    border-left-color: #67c23a;
    color: #67c23a;

    .section-title {
      color: #67c23a;
      .section-icon {
        color: #67c23a;
      }
    }
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.file-status {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.4;
}

.file-upload-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
  width: 100%;

  &:hover {
    border-color: #c0c4cc;
  }

  .file-upload-row {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .el-upload {
    flex-shrink: 0;
  }

  .file-status-text {
    font-size: 12px;
    color: #909399;
    flex: 1;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;

  &:hover {
    box-shadow: 0 0 0 1px #c0c4cc inset;
  }

  &.is-focus {
    box-shadow: 0 0 0 1px #409eff inset;
  }
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload__tip) {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}

// 附件管理样式
.attachment-upload {
  width: 100%;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.4;
}

.attachment-list {
  margin-top: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;

  .attachment-header {
    background: #f5f7fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .attachment-title {
      font-weight: 500;
      color: #303133;
    }

    .contract-url-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .url-text {
        font-size: 12px;
        color: #606266;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .attachment-items {
    .attachment-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:last-child {
        border-bottom: none;
      }

      .attachment-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;

        .file-icon {
          color: #909399;
          font-size: 16px;
        }

        .file-name {
          font-size: 14px;
          color: #303133;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          font-size: 12px;
          color: #909399;
          min-width: 60px;
        }
      }

      .attachment-actions {
        flex-shrink: 0;
      }
    }
  }
}
</style>
