<!--
  页面名称：高校实践订单查看详情
  功能描述：展示高校实践订单详情，支持查看合同信息、审批流程、发起电子合同、上传纸质合同、发起审批等功能
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="高校实践订单详情"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <div class="order-detail-container">
      <!-- 项目详情区域 -->
      <div class="detail-section">
        <div class="project-header">
          <div class="project-title">{{ orderData?.projectName || '项目名称' }}</div>
          <el-tag :type="getOrderStatusType(orderData?.orderStatus || '')" size="large">
            {{ getOrderStatusText(orderData?.orderStatus || '') }}
          </el-tag>
        </div>

        <div class="detail-list">
          <div class="detail-item">
            <span class="label">订单号：</span>
            <span class="value">{{ orderData?.orderNo || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合作高校：</span>
            <span class="value">{{ orderData?.universityName || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合作企业：</span>
            <span class="value">{{ orderData?.enterpriseName || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">项目周期：</span>
            <span class="value">{{
              formatDateRange(orderData?.startDate, orderData?.endDate) || '-'
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">订单金额：</span>
            <span class="value amount">¥{{ formatAmount(orderData?.totalAmount) || '0' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">项目负责人：</span>
            <span class="value">{{ orderData?.managerName || '-' }}</span>
          </div>
          <div class="detail-item" v-if="orderData?.opportunityName">
            <span class="label">关联商机：</span>
            <span class="value link">{{ orderData.opportunityName }}</span>
          </div>
          <div class="detail-item" v-if="orderData?.leadName">
            <span class="label">关联线索：</span>
            <span class="value link">{{ orderData.leadName }}</span>
          </div>
          <div class="detail-item full-width" v-if="orderData?.projectDescription">
            <span class="label">项目描述：</span>
            <span class="value">{{ orderData.projectDescription }}</span>
          </div>
        </div>
      </div>

      <!-- 合同信息区域 -->
      <div class="detail-section">
        <div class="section-title">
          <el-icon><Edit /></el-icon>
          合同信息
        </div>

        <div class="detail-list">
          <div class="detail-item">
            <span class="label">合同类型：</span>
            <span class="value">{{ getContractTypeText(orderData?.contractType || '') }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同编号：</span>
            <span class="value">{{ orderData?.orderNo || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同名称：</span>
            <span class="value">{{ orderData?.projectName || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同周期：</span>
            <span class="value">{{
              formatDateRange(orderData?.startDate, orderData?.endDate) || '-'
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">签署日期：</span>
            <span class="value">{{ formatDate(orderData?.createTime) || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同金额：</span>
            <span class="value amount">¥{{ formatAmount(orderData?.totalAmount) || '0' }}</span>
          </div>
          <div class="detail-item" v-if="orderData?.contractFileUrl">
            <span class="label">合同附件：</span>
            <div class="file-item">
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ getFileNameFromUrl(orderData.contractFileUrl) }}</span>
              <el-button size="small" type="info" @click="handleDownload">下载</el-button>
            </div>
          </div>
          <div class="detail-item" v-if="!orderData?.contractFileUrl">
            <span class="label">合同附件：</span>
            <span class="value">暂无合同附件</span>
          </div>
          <div class="detail-item">
            <span class="label">合同状态：</span>
            <span class="value">{{ getContractStatusText(orderData?.contractStatus || '') }}</span>
          </div>
          <div class="detail-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDateTime(orderData?.createTime) || '-' }}</span>
          </div>
        </div>

        <!-- 合同操作按钮 -->
        <div class="contract-actions" v-if="!orderData?.contractFileUrl">
          <el-button size="small" type="primary" @click="showElectronicContractDialog">
            <el-icon><Document /></el-icon>
            发起电子合同
          </el-button>
          <el-button size="small" type="info" @click="showPaperContractDialog">
            <el-icon><Upload /></el-icon>
            上传纸质合同
          </el-button>
        </div>
        <div class="contract-actions" v-else>
          <el-button size="small" type="success" @click="showPaperContractDialog">
            <el-icon><Upload /></el-icon>
            重新上传合同
          </el-button>
        </div>
      </div>

      <!-- 三方协议签署状态 -->
      <div class="detail-section">
        <div class="section-title">三方协议签署状态</div>

        <div class="sign-status-list">
          <div class="sign-status-item">
            <el-icon class="status-icon"><Platform /></el-icon>
            <span class="party-name">平台方</span>
            <el-tag type="success" size="small">已签署</el-tag>
          </div>
          <div class="sign-status-item">
            <el-icon class="status-icon"><School /></el-icon>
            <span class="party-name">高校方</span>
            <el-tag type="success" size="small">已签署</el-tag>
          </div>
          <div class="sign-status-item">
            <el-icon class="status-icon"><OfficeBuilding /></el-icon>
            <span class="party-name">企业方</span>
            <el-tag type="warning" size="small">待签署</el-tag>
          </div>
        </div>
      </div>

      <!-- 纸质合同附件 -->
      <div
        class="detail-section"
        v-if="orderData?.contractFileUrl && orderData?.contractType === 'paper'"
      >
        <div class="section-title">纸质合同附件</div>

        <div class="contract-attachment">
          <div class="file-item">
            <el-icon><Document /></el-icon>
            <span class="file-name">{{ getFileNameFromUrl(orderData.contractFileUrl) }}</span>
            <el-button size="small" type="info" @click="handleDownload">下载</el-button>
          </div>
        </div>
      </div>

      <!-- 收款信息 -->
      <div class="detail-section" v-if="shouldShowReceiptInfo">
        <div class="section-title">
          <el-icon><Money /></el-icon>
          收款信息
        </div>

        <div class="receipt-info">
          <div class="receipt-item">
            <span class="receipt-label">收款金额：</span>
            <span class="receipt-value amount"
              >¥{{ formatAmount(orderData?.collectionAmount) || '0' }}</span
            >
          </div>
          <div class="receipt-item">
            <span class="receipt-label">收款方式：</span>
            <span class="receipt-value">{{
              getCollectionMethodText(orderData?.collectionMethod || '')
            }}</span>
          </div>
          <div class="receipt-item">
            <span class="receipt-label">收款日期：</span>
            <span class="receipt-value">{{ formatDate(orderData?.collectionDate) || '-' }}</span>
          </div>
          <div class="receipt-item">
            <span class="receipt-label">操作人：</span>
            <span class="receipt-value">{{ orderData?.operatorName || '-' }}</span>
          </div>
          <div class="receipt-item">
            <span class="receipt-label">收款备注：</span>
            <span class="receipt-value">{{ orderData?.collectionRemark || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 审批流程 -->
      <div class="detail-section">
        <div class="section-title">审批流程</div>

        <div class="approval-timeline" v-if="approvalList.length > 0">
          <div class="timeline-item" v-for="item in approvalList" :key="item.id">
            <div class="timeline-dot" :class="getApprovalStatusClass(item.status)"></div>
            <div class="timeline-content">
              <div class="timeline-time">{{ formatDateTime(item.createTime) }}</div>
              <div class="timeline-action">
                <strong>{{ item.operatorName }}({{ item.operatorRole }})</strong> {{ item.action }}
              </div>
              <div class="timeline-note" v-if="item.comments">备注: {{ item.comments }}</div>
              <div class="timeline-status">
                <el-tag :type="getApprovalStatusType(item.status)" size="small">
                  {{ getApprovalStatusText(item.status) }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="!approvalLoading" class="no-approval"> 无审批记录 </div>
        <div v-else class="loading-approval">
          <el-icon class="is-loading"><Loading /></el-icon>
          正在加载审批记录...
        </div>

        <!-- 发起审批按钮 -->
        <div class="approval-actions" v-if="shouldShowApprovalButton">
          <el-button size="small" type="primary" @click="showApprovalDialog"> 发起审批 </el-button>
        </div>

        <!-- 确认收款按钮 -->
        <div class="collection-actions" v-if="shouldShowCollectionButton">
          <el-button size="small" type="success" @click="showCollectionDialog">
            <el-icon><Check /></el-icon>
            确认收款
          </el-button>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 发起电子合同弹窗 -->
  <ElectronicContractDialog
    v-model:visible="electronicContractVisible"
    :order-data="orderData || undefined"
    @success="handleElectronicContractSuccess"
  />

  <!-- 上传纸质合同弹窗 -->
  <PaperContractDialog
    v-model:visible="paperContractVisible"
    :order-data="orderData || undefined"
    @success="handlePaperContractSuccess"
  />

  <!-- 发起审批弹窗 -->
  <OrderApprovalDialog
    :key="approvalKey"
    v-model:visible="approvalVisible"
    :order-data="orderData || undefined"
    @success="handleApprovalSuccess"
  />

  <!-- 确认收款弹窗 -->
  <CollectionDialog
    v-model:visible="collectionVisible"
    :order-data="orderData || undefined"
    @success="handleCollectionSuccess"
  />
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  Upload,
  Platform,
  School,
  OfficeBuilding,
  Money,
  Opportunity,
  Edit,
  Loading,
  Check
} from '@element-plus/icons-vue'
import type {
  UniversityPracticeOrder,
  ApprovalRecordItem
} from '@/api/OrderCenter/UniversityPracticeCenter'
import { UniversityPracticeOrderApi } from '@/api/OrderCenter/UniversityPracticeCenter'
import ElectronicContractDialog from './ElectronicContractDialog.vue'
import PaperContractDialog from './PaperContractDialog.vue'
import OrderApprovalDialog from './OrderApprovalDialog.vue'
import CollectionDialog from './CollectionDialog.vue'

// Props
interface Props {
  visible: boolean
  orderData?: UniversityPracticeOrder | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: undefined
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  edit: [data: any]
  'contract-updated': [data: any]
}>()

// 弹窗显示状态
const electronicContractVisible = ref(false)
const paperContractVisible = ref(false)
const approvalVisible = ref(false)
const collectionVisible = ref(false)

// 审批弹窗的key，用于强制重新渲染
const approvalKey = ref(Date.now())

// 合同信息 - 现在从orderData中获取
const contractInfo = computed(() => ({
  type: getContractTypeText(props.orderData?.contractType || ''),
  number: props.orderData?.orderNo || '',
  name: props.orderData?.projectName || '',
  period: formatDateRange(props.orderData?.startDate, props.orderData?.endDate) || '',
  signDate: formatDate(props.orderData?.createTime) || '',
  amount: formatAmount(props.orderData?.totalAmount) || '0'
}))

// 判断是否显示收款信息
const shouldShowReceiptInfo = computed(() => {
  // 检查是否有收款相关信息
  const hasCollectionInfo = Boolean(
    props.orderData?.collectionAmount ||
      props.orderData?.collectionMethod ||
      props.orderData?.collectionDate ||
      props.orderData?.operatorName ||
      props.orderData?.collectionRemark
  )

  console.log('收款信息显示判断:', {
    orderStatus: props.orderData?.orderStatus,
    paymentStatus: props.orderData?.paymentStatus,
    hasCollectionInfo,
    collectionAmount: props.orderData?.collectionAmount,
    collectionMethod: props.orderData?.collectionMethod,
    collectionDate: props.orderData?.collectionDate,
    operatorName: props.orderData?.operatorName,
    collectionRemark: props.orderData?.collectionRemark
  })

  // 如果没有收款信息，则隐藏收款信息区域
  if (!hasCollectionInfo) {
    console.log('隐藏收款信息区域：无收款信息')
    return false
  }

  // 有收款信息时显示
  console.log('显示收款信息区域')
  return true
})

// 判断是否显示确认收款按钮
const shouldShowCollectionButton = computed(() => {
  // 基于审批记录的最后一条状态来判断
  if (approvalList.value.length === 0) {
    return false
  }

  // 获取最后一条审批记录
  const lastApproval = approvalList.value[0] // 最新的审批记录在数组开头
  console.log('最后一条审批记录:', lastApproval)

  // 如果最后一条审批记录是"审批通过"，则显示收款按钮
  if (lastApproval.status !== 'approved') {
    return false
  }

  // 检查是否已收全款
  const totalAmount = props.orderData?.totalAmount || 0
  const paidAmount = props.orderData?.paidAmount || 0

  // 如果已收全款，则隐藏确认收款按钮
  if (paidAmount >= totalAmount && totalAmount > 0) {
    return false
  }

  return true
})

// 判断是否显示发起审批按钮
const shouldShowApprovalButton = computed(() => {
  // 基于审批记录的最后一条状态来判断
  if (approvalList.value.length === 0) {
    // 如果没有审批记录，则根据订单状态判断
    return (
      props.orderData?.orderStatus === 'pending_approval' ||
      props.orderData?.orderStatus === 'draft' ||
      props.orderData?.orderStatus === 'rejected'
    )
  }

  // 获取最后一条审批记录
  const lastApproval = approvalList.value[0] // 最新的审批记录在数组开头

  // 如果最后一条审批记录是"审批通过"，则隐藏审批按钮
  if (lastApproval.status === 'approved') {
    return false
  }

  // 如果最后一条审批记录是"审批驳回"，则显示审批按钮
  if (lastApproval.status === 'rejected') {
    return true
  }

  // 其他情况（如待审批、审批中等），显示审批按钮
  return true
})

// 审批流程列表
const approvalList = ref<ApprovalRecordItem[]>([])

// 审批记录加载状态
const approvalLoading = ref(false)

// 工具函数
const formatAmount = (amount: number | undefined | null) => {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return '0'
  }
  return amount.toLocaleString('zh-CN')
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return ''
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (error) {
    return ''
  }
}

const formatDateTime = (dateString: string | undefined) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return ''
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}`
  } catch (error) {
    return ''
  }
}

const formatDateRange = (startDate: string | undefined, endDate: string | undefined) => {
  if (!startDate || !endDate) return ''
  return `${formatDate(startDate)} - ${formatDate(endDate)}`
}

const getFileNameFromUrl = (url: string | undefined) => {
  if (!url) return '合同附件'
  try {
    const fileName = url.split('/').pop() || '合同附件'
    return decodeURIComponent(fileName)
  } catch (error) {
    return '合同附件'
  }
}

const getOrderStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'warning',
    approved: 'success',
    rejected: 'danger',
    pending_payment: 'warning',
    executing: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    approved: '已批准',
    rejected: '已拒绝',
    pending_payment: '待支付',
    executing: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getContractTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    electronic: '电子合同',
    paper: '纸质合同',
    both: '电子+纸质'
  }
  return typeMap[type] || type
}

const getContractStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    unsigned: '未签署',
    signed: '已签署',
    rejected: '已拒绝'
  }
  return statusMap[status] || status
}

const getCollectionMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    cash: '现金',
    wechat: '微信支付',
    alipay: '支付宝',
    bank_transfer: '银行转账',
    pos: 'POS机刷卡',
    other: '其他'
  }
  return methodMap[method] || method
}

// 获取审批状态类型
const getApprovalStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    pending: 'warning',
    approving: 'warning',
    approved: 'success',
    rejected: 'danger',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取审批状态文本
const getApprovalStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待审批',
    approving: '审批中',
    approved: '已通过',
    rejected: '已拒绝',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取审批状态样式类
const getApprovalStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    pending: 'status-pending',
    approved: 'status-approved',
    rejected: 'status-rejected'
  }
  return classMap[status] || ''
}

// 根据订单状态初始化审批列表
const initApprovalList = async () => {
  if (!props.orderData?.id || !props.orderData?.orderNo) {
    console.log('订单数据不完整，无法获取审批记录')
    return
  }

  try {
    approvalLoading.value = true
    console.log('开始获取审批记录...')

    // 调用API获取审批记录
    const result = await UniversityPracticeOrderApi.getApprovalRecords({
      orderId: props.orderData.id,
      orderNo: props.orderData.orderNo
    })

    approvalList.value = result.list
    console.log('获取审批记录成功:', result.list)
  } catch (error) {
    console.error('获取审批记录失败:', error)
    // 如果API调用失败，显示默认的审批记录
    if (
      props.orderData?.orderStatus === 'approved' ||
      props.orderData?.orderStatus === 'executing'
    ) {
      approvalList.value = [
        {
          id: 1,
          approvalId: 'AP001',
          approvalNo: 'AP001',
          approvalType: 'order_approval',
          status: 'approved',
          operatorId: 1001,
          operatorName: '张三',
          operatorRole: '部门经理',
          action: '批准了订单',
          comments: '已确认合作意向,批准立项',
          createTime: '2024-06-20 10:05:12',
          updateTime: '2024-06-20 10:05:12'
        }
      ]
    } else {
      approvalList.value = []
    }
  } finally {
    approvalLoading.value = false
  }
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 编辑
const handleEdit = () => {
  emit('edit', props.orderData)
  handleClose()
}

// 下载合同附件
const handleDownload = async () => {
  if (!props.orderData?.id || !props.orderData?.orderNo || !props.orderData?.contractFileUrl) {
    ElMessage.warning('合同附件信息不完整，无法下载')
    return
  }

  try {
    ElMessage.info('正在下载合同附件...')

    const blob = await UniversityPracticeOrderApi.downloadContract({
      orderId: props.orderData.id,
      orderNo: props.orderData.orderNo,
      fileUrl: props.orderData.contractFileUrl
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = getFileNameFromUrl(props.orderData.contractFileUrl)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('合同附件下载成功')
  } catch (error) {
    console.error('下载合同附件失败:', error)
    ElMessage.error('下载合同附件失败，请重试')
  }
}

// 显示发起电子合同弹窗
const showElectronicContractDialog = () => {
  electronicContractVisible.value = true
}

// 显示上传纸质合同弹窗
const showPaperContractDialog = () => {
  paperContractVisible.value = true
}

// 显示发起审批弹窗
const showApprovalDialog = () => {
  approvalVisible.value = true
}

// 显示确认收款弹窗
const showCollectionDialog = () => {
  collectionVisible.value = true
}

// 电子合同创建成功回调
const handleElectronicContractSuccess = () => {
  // TODO: 刷新合同信息
  console.log('电子合同创建成功')
}

// 纸质合同上传成功回调
const handlePaperContractSuccess = (contractData: any) => {
  // 通过emit事件通知父组件更新合同信息
  emit('contract-updated', {
    contractStatus: 'signed',
    contractFileUrl: contractData.fileUrl,
    contractType: 'paper',
    contractData: contractData
  })

  // 显示成功提示
  ElMessage.success('纸质合同上传成功，合同信息已更新')
  console.log('纸质合同上传成功', contractData)
}

// 审批发起成功回调
const handleApprovalSuccess = (approvalData: any) => {
  console.log('审批操作成功:', approvalData)

  // 根据审批结果显示不同的成功消息
  if (approvalData.approvalResult === 'approve') {
    ElMessage.success('审批通过成功')
  } else {
    ElMessage.success('审批驳回成功')
  }

  // 添加新的审批记录
  const newApprovalRecord: ApprovalRecordItem = {
    id: Date.now(),
    approvalId: 'AP' + Date.now(),
    approvalNo: 'AP' + Date.now(),
    approvalType: 'order_approval',
    status: approvalData.approvalResult === 'approve' ? 'approved' : 'rejected',
    operatorId: 0,
    operatorName: '当前用户',
    operatorRole: '审批人',
    action: approvalData.approvalResult === 'approve' ? '审批通过' : '审批驳回',
    comments: approvalData.rejectReason || approvalData.comments || '',
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  }

  approvalList.value.unshift(newApprovalRecord)
  console.log('审批操作成功，新增审批记录:', newApprovalRecord)

  // 关闭审批弹窗
  approvalVisible.value = false

  // 通知父组件更新订单数据
  emit('contract-updated', {
    orderStatus: approvalData.approvalResult === 'approve' ? 'approved' : 'rejected',
    approvalResult: approvalData.approvalResult,
    rejectReason: approvalData.rejectReason,
    comments: approvalData.comments
  })
}

// 收款确认成功回调
const handleCollectionSuccess = (collectionData: any) => {
  console.log('收款确认成功:', collectionData)
  ElMessage.success('收款确认成功')

  // 关闭收款弹窗
  collectionVisible.value = false

  // 通知父组件更新订单数据
  emit('contract-updated', {
    paymentStatus: 'paid',
    collectionAmount: collectionData.collectionAmount,
    collectionMethod: collectionData.collectionMethod,
    collectionDate: collectionData.collectionDate,
    operatorName: collectionData.operatorName,
    collectionRemark: collectionData.collectionRemark
  })
}

// 监听订单数据变化，初始化审批列表
watch(
  () => props.orderData,
  (newVal) => {
    if (newVal) {
      initApprovalList()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.order-detail-container {
  padding: 20px;
  height: calc(100vh - 200px);
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.detail-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:last-child {
    margin-bottom: 0;
  }
}

.project-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

  .project-title {
    font-size: 18px;
    font-weight: bold;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.detail-list {
  .detail-item {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;

    &.full-width {
      flex-direction: column;
      align-items: flex-start;
    }

    .label {
      font-weight: 500;
      color: #606266;
      min-width: 100px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 16px;
      }

      &.link {
        color: #409eff;
        cursor: pointer;
        text-decoration: underline;
      }
    }
  }
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  .file-name {
    flex: 1;
    color: #303133;
  }
}

.business-opportunity {
  .opportunity-link {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .opportunity-desc {
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
  }
}

.contract-actions-section {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.enterprise-status {
  display: flex;
  align-items: center;
  gap: 12px;

  .status-label {
    font-weight: 500;
    color: #303133;
  }
}

.contract-attachment {
  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e4e7ed;

    .file-name {
      flex: 1;
      color: #303133;
      font-weight: 500;
    }
  }
}

.contract-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  justify-content: flex-start;
}

.sign-status-list {
  .sign-status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .status-icon {
      font-size: 20px;
      color: #909399;
    }

    .party-name {
      flex: 1;
      font-weight: 500;
      color: #303133;
    }
  }
}

.receipt-info {
  background: #f0f9ff;
  border: 1px solid #67c23a;
  border-radius: 8px;
  padding: 16px;

  .receipt-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    .receipt-label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      flex-shrink: 0;
    }

    .receipt-value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
}

.approval-timeline {
  .timeline-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;

    .timeline-dot {
      width: 8px;
      height: 8px;
      background: #409eff;
      border-radius: 50%;
      margin-top: 6px;
      flex-shrink: 0;

      &.status-pending {
        background: #e6a23c;
      }

      &.status-approving {
        background: #409eff;
      }

      &.status-approved {
        background: #67c23a;
      }

      &.status-rejected {
        background: #f56c6c;
      }

      &.status-cancelled {
        background: #909399;
      }
    }

    .timeline-content {
      flex: 1;

      .timeline-time {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }

      .timeline-action {
        font-size: 14px;
        color: #303133;
        margin-bottom: 4px;
      }

      .timeline-note {
        font-size: 12px;
        color: #606266;
        background: #f8f9fa;
        padding: 8px;
        border-radius: 4px;
        margin-bottom: 8px;
      }

      .timeline-status {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }
  }

  .no-approval {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 20px 0;
  }

  .loading-approval {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

.approval-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.collection-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-drawer__header) {
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 0;
}

:deep(.el-drawer__body) {
  padding: 0;
}
</style>
