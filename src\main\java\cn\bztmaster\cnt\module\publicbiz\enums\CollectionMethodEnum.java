package cn.bztmaster.cnt.module.publicbiz.enums;

/**
 * 收款方式枚举
 * 定义系统支持的各种收款方式
 */
public enum CollectionMethodEnum {

    /**
     * 现金收款
     */
    CASH("cash", "现金"),

    /**
     * 微信支付收款
     */
    WECHAT("wechat", "微信支付"),

    /**
     * 支付宝收款
     */
    ALIPAY("alipay", "支付宝"),

    /**
     * 银行转账收款
     */
    BANK_TRANSFER("bank_transfer", "银行转账"),

    /**
     * POS机刷卡收款
     */
    POS("pos", "POS机刷卡"),

    /**
     * 其他收款方式
     */
    OTHER("other", "其他");

    /**
     * 收款方式编码
     */
    private final String code;

    /**
     * 收款方式名称
     */
    private final String name;

    CollectionMethodEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取收款方式编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取收款方式名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据编码获取枚举值
     */
    public static CollectionMethodEnum getByCode(String code) {
        for (CollectionMethodEnum method : values()) {
            if (method.getCode().equals(code)) {
                return method;
            }
        }
        return null;
    }

    /**
     * 根据编码获取名称
     */
    public static String getNameByCode(String code) {
        CollectionMethodEnum method = getByCode(code);
        return method != null ? method.getName() : code;
    }

    /**
     * 判断编码是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}

