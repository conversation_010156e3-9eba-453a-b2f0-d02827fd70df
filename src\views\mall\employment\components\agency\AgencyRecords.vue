<!--
  页面名称：机构激励/处罚记录
  功能描述：展示机构的激励和处罚记录列表
-->
<template>
  <div class="agency-records">
    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-button-group>
        <el-button
          v-for="type in recordTypes"
          :key="type.value"
          :type="currentType === type.value ? 'primary' : ''"
          @click="changeType(type.value)"
        >
          {{ type.label }}
        </el-button>
      </el-button-group>
      <div class="date-filter">
        <label>记录日期</label>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 300px"
        />
        <el-button type="primary" @click="onSearch">查询</el-button>
      </div>
      <el-button type="primary" @click="onAdd">新增记录</el-button>
    </div>

    <!-- 记录列表 -->
    <div class="records-list">
      <div v-for="record in tableData" :key="record.id" class="record-item" :class="record.type">
        <div class="record-content">
          <div class="record-header">
            <span class="record-title" :class="record.type">
              <i :class="getRecordIcon(record.type)"></i>
              {{ record.title }}
            </span>
            <span class="record-date">{{ formatDate(record.date) }}</span>
          </div>
          <div class="record-body">
            <p
              ><strong>影响:</strong>
              <span :class="getImpactClass(record.impact)">{{ record.impact }}</span></p
            >
            <p><strong>事由:</strong> {{ record.reason }}</p>
            <p v-if="record.status"
              ><strong>状态:</strong>
              <el-tag :type="getStatusTag(record.status)" size="small">{{
                record.statusText
              }}</el-tag></p
            >
            <p v-if="record.attachments"
              ><strong>附件:</strong> <a href="#">{{ record.attachments }}</a></p
            >
          </div>
          <div class="record-footer">
            <span>记录人: {{ record.recorder }}</span>
            <el-button size="small" @click="viewRecordDetail(record)">查看详情</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getRecordList } from '@/api/mall/employment/record'

/** 机构数据 */
const props = defineProps<{
  agency: any
}>()

/** 记录类型选项 */
const recordTypes = [
  { label: '全部', value: 'all' },
  { label: '激励记录', value: 'incentive' },
  { label: '处罚记录', value: 'punishment' }
]

/** 当前选中的类型 */
const currentType = ref('all')

/** 日期范围 */
const dateRange = ref([])

/** 表格数据 */
const tableData = ref([])

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 获取记录列表 */
const fetchList = async () => {
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      agencyId: props.agency?.id,
      type: currentType.value === 'all' ? '' : currentType.value,
      startDate: dateRange.value?.[0],
      endDate: dateRange.value?.[1]
    }
    const res = await getRecordList(params)
    tableData.value = res.data.list || []
    pagination.total = res.data.total || 0
  } catch (error) {
    console.error('获取记录列表失败:', error)
  }
}

/** 切换记录类型 */
const changeType = (type: string) => {
  currentType.value = type
  pagination.page = 1
  fetchList()
}

/** 搜索 */
const onSearch = () => {
  pagination.page = 1
  fetchList()
}

/** 新增记录 */
const onAdd = () => {
  console.log('新增记录')
}

/** 查看记录详情 */
const viewRecordDetail = (record: any) => {
  console.log('查看记录详情:', record)
}

/** 获取记录图标 */
const getRecordIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    incentive: 'fas fa-award',
    punishment: 'fas fa-gavel'
  }
  return iconMap[type] || 'fas fa-file-alt'
}

/** 获取影响样式类 */
const getImpactClass = (impact: string) => {
  if (impact.includes('+')) {
    return 'text-success'
  } else if (impact.includes('-')) {
    return 'text-danger'
  }
  return ''
}

/** 获取状态标签 */
const getStatusTag = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

/** 分页大小改变 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

/** 当前页改变 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

onMounted(() => {
  if (props.agency) {
    fetchList()
  }
})
</script>

<style scoped lang="scss">
.agency-records {
  .filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
  }

  .date-filter {
    display: flex;
    align-items: center;
    gap: 10px;

    label {
      font-size: 14px;
      color: #6c757d;
    }
  }

  .records-list {
    margin-bottom: 20px;
  }

  .record-item {
    margin-bottom: 20px;
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 20px;
      top: 40px;
      width: 2px;
      height: calc(100% + 10px);
      background: #e9ecef;
    }

    &.incentive:not(:last-child)::after {
      background: #28a745;
    }
  }

  .record-content {
    background: white;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-left: 40px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: -6px;
      top: 5px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #e9ecef;
      border: 3px solid white;
    }
  }

  .record-item.incentive .record-content::before {
    border-color: #28a745;
  }

  .record-item.punishment .record-content::before {
    border-color: #dc3545;
  }

  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .record-title {
    font-size: 16px;
    font-weight: 600;

    &.incentive {
      color: #28a745;
    }

    &.punishment {
      color: #dc3545;
    }

    i {
      margin-right: 8px;
    }
  }

  .record-date {
    font-size: 13px;
    color: #6c757d;
  }

  .record-body {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;

    p {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .record-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
    font-size: 12px;
    color: #6c757d;
  }

  .text-success {
    color: #28a745;
  }

  .text-danger {
    color: #dc3545;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
  }
}
</style>
