# 机构激励/处罚记录管理API接口文档

## 1. 接口概述

### 1.1 基础信息
- **基础路径**: `/publicbiz/agencyRecord`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bear<PERSON>ken

### 1.2 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:00:00Z"
}
```

### 1.3 分页响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20
  },
  "timestamp": "2024-01-15T10:00:00Z"
}
```

## 2. 接口列表

### 2.1 分页查询激励/处罚记录

**接口地址**: `POST /publicbiz/agencyRecord/page`

**功能说明**: 分页查询机构激励/处罚记录列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 是 | 页码，从1开始 |
| pageSize | Integer | 是 | 每页记录数，最大100 |
| recordType | String | 否 | 记录类型：incentive-激励记录，penalty-处罚记录, communication-沟通日志|
| startDate | String | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | String | 否 | 结束日期，格式：YYYY-MM-DD |

**请求示例**:
```json
{
  "pageNum": 1,
  "pageSize": 20,
  "recordType": "incentive",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31"
}
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | Array | 记录列表 |
| data[].id | Long | 记录ID |
| data[].recordId | String | 记录编号 |
| data[].agencyId | Long | 机构ID |
| data[].recordType | String | 记录类型 |
| data[].recordDate | String | 记录日期 |
| data[].title | String | 记录标题 |
| data[].description | String | 记录描述 |
| data[].creditImpact | Integer | 信用分影响 |
| data[].amountImpact | BigDecimal | 金额影响 |
| data[].status | String | 处理状态 |
| data[].communicationType | String | 沟通方式：call-通话记录，message-消息记录，email-邮件记录|
| data[].communicationTitle | String | 沟通标题（仅当记录类型为沟通日志时有效） |
| data[].communicationContent | String | 沟通内容（仅当记录类型为沟通日志时有效） |
| data[].participants | String | 参与人，多个参与人用逗号分隔（仅当记录类型为沟通日志时有效） |
| data[].recorderName | String | 记录人姓名 |
| data[].createTime | String | 创建时间 |
| total | Integer | 总记录数 |
| pageNum | Integer | 当前页码 |
| pageSize | Integer | 每页记录数 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "recordId": "REC001",
      "agencyId": 1001,
      "recordType": "incentive",
      "recordDate": "2024-01-15",
      "title": "平台奖励 - 月度优秀机构",
      "description": "因其卓越的服务质量和客户满意度,被评为1月度优秀合作机构。",
      "creditImpact": 10,
      "amountImpact": 0.00,
      "status": "effective",
      "communicationType": null,
      "communicationTitle": null,
      "communicationContent": null,
      "participants": null,
      "recorderName": "系统",
      "createTime": "2024-01-15T10:00:00Z"
    },
    {
      "id": 2,
      "recordId": "REC002",
      "agencyId": 1002,
      "recordType": "communication",
      "recordDate": "2024-01-16",
      "title": "客户投诉处理",
      "description": "处理客户关于服务质量的投诉",
      "creditImpact": 0,
      "amountImpact": 0.00,
      "status": "completed",
      "communicationType": "call",
      "communicationTitle": "电话沟通客户投诉",
      "communicationContent": "已与客户电话沟通，了解具体投诉内容，客户表示对阿姨服务态度不满意。",
      "participants": "张三,李四,王五",
      "recorderName": "客服专员",
      "createTime": "2024-01-16T14:30:00Z"
    }
  ],
  "total": 2,
  "pageNum": 1,
  "pageSize": 20,
  "timestamp": "2024-01-15T10:00:00Z"
}
```

### 2.2 创建激励/处罚记录

**接口地址**: `POST /publicbiz/agencyRecord/create`

**功能说明**: 创建新的机构激励/处罚记录保存成功后添加附件保存

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| agencyId | Long | 否 | 机构ID |
| recordType | String | 是 | 记录类型：incentive-激励记录，penalty-处罚记录 |
| recordDate | String | 是 | 记录日期，格式：YYYY-MM-DD |
| title | String | 是 | 记录标题，最大200字符 |
| description | String | 否 | 记录详细描述 |
| creditImpact | Integer | 否 | 信用分影响，正数表示加分，负数表示扣分 |
| amountImpact | BigDecimal | 否 | 金额影响，正数表示奖励，负数表示罚款 |
| otherImpact | String | 否 | 其他影响，如暂停接单、降级等 |
| relatedInfo | String | 否 | 关联信息，如订单号、阿姨编号等 |
| status | String | 否 | 处理状态，默认：pending |
| followUpDate | String | 否 | 跟进日期，格式：YYYY-MM-DD |
| followUpItem | String | 否 | 跟进事项 |
| remarks | String | 否 | 备注说明 |
| attachments | Array | 否 | 附件数组 |
| attachments[].fileName | String | 是 | 文件名 |
| attachments[].fileType | String | 是 | 文件类型，如：pdf、xls、doc、mp3、jpg、png等 |
| attachments[].fileSize | Long | 是 | 文件大小（字节） |
| attachments[].fileUrl | String | 是 | 文件访问URL |

**请求示例**:
```json
{
  "agencyId": 1001,
  "recordType": "incentive",
  "recordDate": "2024-01-15",
  "title": "客户好评 - 超出预期",
  "description": "关联订单 20240110021, 客户致电表扬阿姨工作细致, 服务超出预期。",
  "creditImpact": 5,
  "amountImpact": 50.00,
  "relatedInfo": "订单号：20240110021",
  "status": "pending",
  "remarks": "客户主动表扬，建议给予奖励",
  "attachments": [
    {
      "fileName": "客户表扬录音.mp3",
      "fileType": "mp3",
      "fileSize": 1536000,
      "fileUrl": "https://example.com/files/客户表扬录音.mp3"
    },
    {
      "fileName": "服务评价截图.jpg",
      "fileType": "jpg",
      "fileSize": 256000,
      "fileUrl": "https://example.com/files/服务评价截图.jpg"
    }
  ]
}
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 新创建的记录ID |
| recordId | String | 系统生成的记录编号 |

**响应示例**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 2,
    "recordId": "REC002"
  },
  "timestamp": "2024-01-15T10:00:00Z"
}
```

### 2.3 更新激励/处罚记录

**接口地址**: `POST /publicbiz/agencyRecord/update`

**功能说明**: 更新现有的机构激励/处罚记录成功后,根据记录ID删除现有附件信息并重新添加保存附件

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 记录ID |
| recordType | String | 否 | 记录类型 |
| recordDate | String | 否 | 记录日期 |
| title | String | 否 | 记录标题 |
| description | String | 否 | 记录详细描述 |
| creditImpact | Integer | 否 | 信用分影响 |
| amountImpact | BigDecimal | 否 | 金额影响 |
| otherImpact | String | 否 | 其他影响 |
| relatedInfo | String | 否 | 关联信息 |
| status | String | 否 | 处理状态 |
| followUpDate | String | 否 | 跟进日期 |
| followUpItem | String | 否 | 跟进事项 |
| remarks | String | 否 | 备注说明 |
| attachments | Array | 否 | 附件数组 |
| attachments[].id | Long | 否 | 附件ID，更新时如不填写则创建新附件，填写则更新现有附件 |
| attachments[].fileName | String | 是 | 文件名 |
| attachments[].fileType | String | 是 | 文件类型，如：pdf、xls、doc、mp3、jpg、png等 |
| attachments[].fileSize | Long | 是 | 文件大小（字节） |
| attachments[].fileUrl | String | 是 | 文件访问URL |

**请求示例**:
```json
{
  "id": 1,
  "status": "effective",
  "effectiveTime": "2024-01-15T18:00:00Z",
  "remarks": "奖励已生效，信用分已更新",
  "attachments": [
    {
      "id": 1,
      "fileName": "生效确认书.pdf",
      "fileType": "pdf",
      "fileSize": 512000,
      "fileUrl": "https://example.com/files/生效确认书.pdf"
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": null,
  "timestamp": "2024-01-15T10:00:00Z"
}
```

### 2.4 获取记录详情

**接口地址**: `GET /publicbiz/agencyRecord/detail/{id}`

**功能说明**: 根据ID获取激励/处罚记录的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 记录ID |

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 记录ID |
| recordId | String | 记录编号 |
| agencyId | Long | 机构ID |
| recordType | String | 记录类型 |
| recordDate | String | 记录日期 |
| title | String | 记录标题 |
| description | String | 记录详细描述 |
| creditImpact | Integer | 信用分影响 |
| amountImpact | BigDecimal | 金额影响 |
| otherImpact | String | 其他影响 |
| relatedInfo | String | 关联信息 |
| status | String | 处理状态 |
| followUpDate | String | 跟进日期 |
| followUpItem | String | 跟进事项 |
| remarks | String | 备注说明 |
| effectiveTime | String | 生效时间 |
| recorderName | String | 记录人姓名 |
| recorderId | Long | 记录人ID |
| recordTime | String | 记录时间 |
| creator | String | 创建人 |
| createTime | String | 创建时间 |
| updater | String | 更新人 |
| updateTime | String | 更新时间 |
| attachments | Array | 附件数组 |
| attachments[].id | Long | 附件ID |
| attachments[].fileName | String | 文件名 |
| attachments[].fileType | String | 文件类型，如：pdf、xls、doc、mp3、jpg、png等 |
| attachments[].fileSize | Long | 文件大小（字节） |
| attachments[].fileUrl | String | 文件访问URL |
| attachments[].uploadTime | String | 上传时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "recordId": "REC001",
    "agencyId": 1001,
    "recordType": "incentive",
    "recordDate": "2024-01-15",
    "title": "平台奖励 - 月度优秀机构",
    "description": "因其卓越的服务质量和客户满意度,被评为1月度优秀合作机构。",
    "creditImpact": 10,
    "amountImpact": 0.00,
    "otherImpact": null,
    "relatedInfo": null,
    "status": "effective",
    "followUpDate": null,
    "followUpItem": null,
    "remarks": "月度优秀机构奖励",
    "effectiveTime": "2024-01-15T18:00:00Z",
    "recorderName": "系统",
    "recorderId": 1,
    "recordTime": "2024-01-15T10:00:00Z",
    "creator": "系统",
    "createTime": "2024-01-15T10:00:00Z",
    "updater": "系统",
    "updateTime": "2024-01-15T18:00:00Z",
    "attachments": [
      {
        "id": 1,
        "fileName": "优秀机构证书.pdf",
        "fileType": "pdf",
        "fileSize": 1024000,
        "fileUrl": "https://example.com/files/优秀机构证书.pdf",
        "uploadTime": "2024-01-15T10:00:00Z"
      }
    ]
  },
  "timestamp": "2024-01-15T10:00:00Z"
}
```

### 2.5 获取跟进记录列表

**接口地址**: `GET /publicbiz/agencyRecord/followUpList/{recordId}`

**功能说明**: 根据记录ID获取该记录的所有跟进记录列表，不需要分页

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 记录ID |

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | Array | 跟进记录列表 |
| data[].id | Long | 跟进记录ID |
| data[].title | String | 跟进标题 |
| data[].description | String | 跟进描述 |
| data[].followUpDate | String | 跟进日期，格式：YYYY-MM-DD |
| data[].operatorId | Long | 操作人ID |
| data[].operatorName | String | 操作人姓名 |
| data[].createTime | String | 创建时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "title": "首次跟进 - 了解情况",
      "description": "已与机构负责人电话沟通，了解具体情况，机构表示会积极配合整改。",
      "followUpDate": "2024-01-16",
      "operatorId": 1001,
      "operatorName": "张三",
      "createTime": "2024-01-16T09:00:00Z"
    },
    {
      "id": 2,
      "title": "二次跟进 - 检查整改",
      "description": "现场检查机构整改情况，发现已按要求完成整改，建议恢复正常状态。",
      "followUpDate": "2024-01-18",
      "operatorId": 1002,
      "operatorName": "李四",
      "createTime": "2024-01-18T14:30:00Z"
    }
  ],
  "timestamp": "2024-01-15T10:00:00Z"
}
```

### 2.6 新增跟进记录

**接口地址**: `POST /publicbiz/agencyRecord/followUp/create`

**功能说明**: 为指定的激励/处罚记录新增一条跟进记录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 记录ID |
| description | String | 是 | 跟进描述，详细说明跟进内容 |
| title | String | 否 | 跟进标题，如不填写系统将自动生成 |
| operatorId | Long | 否 | 操作人ID，如不填写将使用当前登录用户ID |
| operatorName | String | 否 | 操作人姓名，如不填写将使用当前登录用户姓名 |

**请求示例**:
```json
{
  "recordId": 1,
  "description": "已与机构负责人电话沟通，了解具体情况，机构表示会积极配合整改。",
  "title": "首次跟进 - 了解情况",
  "operatorId": 1001,
  "operatorName": "张三"
}
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 新创建的跟进记录ID |
| followUpDate | String | 跟进日期，系统自动设置为当前日期 |

**响应示例**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 3,
    "followUpDate": "2024-01-20"
  },
  "timestamp": "2024-01-20T10:00:00Z"
}
```



### 2.8 新增沟通日志记录

**接口地址**: `POST /publicbiz/agencyRecord/communicationcreate`

**功能说明**: 创建新的机构沟通日志记录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| communicationType | String | 是 | 沟通方式：call-通话记录，message-消息记录，email-邮件记录 |
| communicationTitle | String | 是 | 沟通标题 |
| communicationContent | String | 是 | 沟通内容，详细描述沟通情况 |
| participants | String | 否 | 参与人，多个参与人用逗号分隔 |
| agencyId | Long | 否 | 机构ID |
| followUpDate | String | 否 | 跟进日期，格式：YYYY-MM-DD |
| followUpItem | String | 否 | 跟进事项 |
| attachments | Array | 否 | 附件数组 |
| attachments[].fileName | String | 是 | 文件名 |
| attachments[].fileType | String | 是 | 文件类型，如：pdf、xls、doc、mp3、jpg、png等 |
| attachments[].fileSize | Long | 是 | 文件大小（字节） |
| attachments[].fileUrl | String | 是 | 文件访问URL |

**请求示例**:
```json
{
  "communicationType": "call",
  "communicationTitle": "客户投诉电话沟通",
  "communicationContent": "客户致电投诉阿姨服务态度问题，已详细记录客户反馈，承诺24小时内给出处理方案。",
  "participants": "张三,李四",
  "agencyId": 1001,
  "followUpDate": "2024-01-17",
  "followUpItem": "联系机构负责人了解情况",
  "attachments": [
    {
      "fileName": "通话录音.mp3",
      "fileType": "mp3",
      "fileSize": 2048000,
      "fileUrl": "https://example.com/files/通话录音.mp3"
    },
    {
      "fileName": "投诉记录.pdf",
      "fileType": "pdf",
      "fileSize": 512000,
      "fileUrl": "https://example.com/files/投诉记录.pdf"
    }
  ]
}
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 新创建的沟通日志记录ID |
| recordId | String | 系统生成的记录编号 |
| recordType | String | 记录类型，固定为"communication" |

**响应示例**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 3,
    "recordId": "REC003",
    "recordType": "communication"
  },
  "timestamp": "2024-01-16T15:00:00Z"
}
```

## 3. 数据字典

### 3.1 记录类型 (recordType)
- `incentive`: 激励记录
- `penalty`: 处罚记录

### 3.2 处理状态 (status)
- `pending`: 待处理
- `processing`: 处理中
- `completed`: 已完成
- `cancelled`: 已取消
- `effective`: 已生效

### 3.3 沟通类型 (communicationType)
- `call`: 通话记录
- `message`: 消息记录
- `email`: 邮件记录

### 3.4 沟通状态 (communicationStatus)
- `success`: 成功
- `failed`: 失败
- `pending`: 待处理

## 4. 错误码说明

### 4.1 HTTP状态码
- `200`: 请求成功
- `400`: 参数验证错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误


### 4.3 错误响应示例
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": {
    "field": "agencyId",
    "message": "机构ID不能为空"
  },
  "timestamp": "2024-01-15T10:00:00Z"
}
```

## 5. 注意事项

1. **权限控制**: 所有接口都需要有效的认证Token，部分敏感操作需要相应的权限验证
2. **数据验证**: 所有输入参数都会进行格式和业务逻辑验证
3. **日志记录**: 所有增删改操作都会记录操作日志
4. **事务处理**: 涉及多表操作时会使用数据库事务确保数据一致性
5. **缓存策略**: 查询接口支持缓存，提高响应速度
6. **限流控制**: 接口调用频率有限制，避免恶意调用

## 6. 更新日志

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 1.0.0 | 2024-01-15 | 初始版本，包含基础CRUD接口 | 系统管理员 | 