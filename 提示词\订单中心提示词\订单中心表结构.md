-- ==================== 订单中心相关表 ====================

-- 订单主表 CREATE TABLE `publicbiz_order` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 订单基本信息 `order_no` VARCHAR(50) NOT NULL COMMENT '订单号', `order_type` VARCHAR(30) NOT NULL COMMENT '订单类型：practice-高校实践/training-企业培训/personal-个人培训/domestic-家政服务/certification-考试认证', `business_line` VARCHAR(50) NOT NULL COMMENT '业务线：高校实践/企业培训/个人培训与认证/家政服务',

-- 关联信息 `opportunity_id` VARCHAR(50) COMMENT '关联商机ID', `lead_id` VARCHAR(50) COMMENT '关联线索ID',

-- 项目信息 `project_name` VARCHAR(200) COMMENT '项目名称', `project_description` TEXT COMMENT '项目描述', `start_date` DATE COMMENT '开始日期', `end_date` DATE COMMENT '结束日期',

-- 金额信息 `total_amount` DECIMAL(12,2) NOT NULL COMMENT '订单总金额', `paid_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '已支付金额', `refund_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '退款金额', `payment_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '支付状态：pending-待支付/paid-已支付/refunded-已退款/cancelled-已取消',

-- 订单状态 `order_status` VARCHAR(30) NOT NULL DEFAULT 'draft' COMMENT '订单状态：draft-草稿/pending_approval-待审批/approving-审批中/approved-已批准/rejected-已拒绝/pending_payment-待支付/executing-执行中/completed-已完成/cancelled-已取消',

-- 负责人信息 `manager_id` BIGINT COMMENT '负责人ID', `manager_name` VARCHAR(50) COMMENT '负责人姓名', `manager_phone` VARCHAR(20) COMMENT '负责人电话',

-- 合同信息 `contract_type` VARCHAR(20) DEFAULT 'electronic' COMMENT '合同类型：electronic-电子合同/paper-纸质合同', `contract_file_url` VARCHAR(500) COMMENT '合同文件URL', `contract_status` VARCHAR(20) DEFAULT 'unsigned' COMMENT '合同状态：unsigned-未签署/signed-已签署/rejected-已拒绝',

-- 备注信息 `remark` TEXT COMMENT '备注',

-- 结算相关字段 `settlement_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败', `settlement_time` DATETIME COMMENT '结算时间', `settlement_method` VARCHAR(30) COMMENT '结算方式', `is_selected_for_reconciliation` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否被选中生成对账单：0-未选中，1-已选中', `selection_time` DATETIME COMMENT '选中时间', `selector_id` BIGINT COMMENT '选择人ID', `selector_name` VARCHAR(50) COMMENT '选择人姓名',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_order_no` (`order_no`), KEY `idx_tenant_id` (`tenant_id`), KEY `idx_order_type` (`order_type`), KEY `idx_business_line` (`business_line`), KEY `idx_customer_id` (`customer_id`), KEY `idx_payment_status` (`payment_status`), KEY `idx_order_status` (`order_status`), KEY `idx_settlement_status` (`settlement_status`), KEY `idx_is_selected_for_reconciliation` (`is_selected_for_reconciliation`), KEY `idx_manager_id` (`manager_id`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='订单主表';

-- 订单支付记录表 CREATE TABLE `publicbiz_order_payment` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 业务字段 `order_id` BIGINT NOT NULL COMMENT '订单ID', `order_no` VARCHAR(50) NOT NULL COMMENT '订单号', `payment_no` VARCHAR(50) NOT NULL COMMENT '支付单号', `payment_type` VARCHAR(20) NOT NULL COMMENT '支付类型：cash-现金/wechat-微信支付/alipay-支付宝/bank_transfer-银行转账/pos-POS机刷卡/other-其他', `payment_amount` DECIMAL(12,2) NOT NULL COMMENT '支付金额', `payment_status` VARCHAR(20) NOT NULL COMMENT '支付状态：pending-待支付/success-支付成功/failed-支付失败/cancelled-已取消', `payment_time` DATETIME COMMENT '支付时间', `operator_id` BIGINT COMMENT '操作人ID', `operator_name` VARCHAR(50) COMMENT '操作人姓名', `payment_remark` TEXT COMMENT '支付备注', `transaction_id` VARCHAR(100) COMMENT '第三方交易号',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_payment_no` (`payment_no`), KEY `idx_order_id` (`order_id`), KEY `idx_order_no` (`order_no`), KEY `idx_payment_type` (`payment_type`), KEY `idx_payment_status` (`payment_status`), KEY `idx_payment_time` (`payment_time`), KEY `idx_operator_id` (`operator_id`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='订单支付记录表';

-- 高校实践订单详情表 CREATE TABLE `publicbiz_practice_order` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联字段 `order_id` BIGINT NOT NULL COMMENT '订单ID', `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

-- 高校信息 `university_name` VARCHAR(200) NOT NULL COMMENT '合作高校名称', `university_contact` VARCHAR(50) COMMENT '高校联系人', `university_phone` VARCHAR(20) COMMENT '高校联系电话', `university_email` VARCHAR(100) COMMENT '高校联系邮箱',

-- 企业信息 `enterprise_name` VARCHAR(200) NOT NULL COMMENT '合作企业名称', `enterprise_contact` VARCHAR(50) COMMENT '企业联系人', `enterprise_phone` VARCHAR(20) COMMENT '企业联系电话', `enterprise_email` VARCHAR(100) COMMENT '企业联系邮箱',

-- 项目信息 `project_name` VARCHAR(200) NOT NULL COMMENT '项目名称', `project_description` TEXT COMMENT '项目描述', `student_count` INT COMMENT '参与学生人数', `practice_duration` VARCHAR(50) COMMENT '实践时长', `practice_location` VARCHAR(500) COMMENT '实践地点',

-- 费用信息 `service_fee` DECIMAL(10,2) COMMENT '服务费', `management_fee` DECIMAL(10,2) COMMENT '管理费', `other_fee` DECIMAL(10,2) COMMENT '其他费用',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_order_id` (`order_id`), KEY `idx_order_no` (`order_no`), KEY `idx_university_name` (`university_name`), KEY `idx_enterprise_name` (`enterprise_name`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='高校实践订单详情表';

-- 企业培训订单详情表 CREATE TABLE `publicbiz_training_order` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联字段 `order_id` BIGINT NOT NULL COMMENT '订单ID', `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

-- 企业信息 `enterprise_name` VARCHAR(200) NOT NULL COMMENT '企业名称', `enterprise_contact` VARCHAR(50) COMMENT '企业联系人', `enterprise_phone` VARCHAR(20) COMMENT '企业联系电话', `enterprise_email` VARCHAR(100) COMMENT '企业联系邮箱', `enterprise_address` VARCHAR(500) COMMENT '企业地址',

-- 培训信息 `training_project` VARCHAR(200) NOT NULL COMMENT '培训项目名称', `training_description` TEXT COMMENT '培训项目描述', `participants_count` INT NOT NULL COMMENT '培训人数', `training_duration` VARCHAR(50) COMMENT '培训周期', `training_location` VARCHAR(500) COMMENT '培训地点', `training_type` VARCHAR(50) COMMENT '培训类型：技能培训/管理培训/认证培训',

-- 费用信息 `per_person_fee` DECIMAL(10,2) COMMENT '人均培训费', `total_fee` DECIMAL(10,2) COMMENT '总培训费', `material_fee` DECIMAL(10,2) COMMENT '教材费', `certification_fee` DECIMAL(10,2) COMMENT '认证费',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_order_id` (`order_id`), KEY `idx_order_no` (`order_no`), KEY `idx_enterprise_name` (`enterprise_name`), KEY `idx_training_project` (`training_project`), KEY `idx_training_type` (`training_type`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='企业培训订单详情表';

-- 个人培训订单详情表 CREATE TABLE `publicbiz_personal_order` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联字段 `order_id` BIGINT NOT NULL COMMENT '订单ID', `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

-- 学员信息 `student_oneid` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '学员OneID GUID', `student_name` VARCHAR(50) NOT NULL COMMENT '学员姓名', `student_phone` VARCHAR(20) COMMENT '学员电话', `student_email` VARCHAR(100) COMMENT '学员邮箱', `student_id_card` VARCHAR(18) COMMENT '学员身份证号',

-- 培训信息 `course_name` VARCHAR(200) NOT NULL COMMENT '课程名称', `course_type` VARCHAR(50) NOT NULL COMMENT '订单类型：个人培训/考试认证', `course_description` TEXT COMMENT '课程描述', `course_duration` VARCHAR(50) COMMENT '课程时长', `learning_status` VARCHAR(20) DEFAULT 'not_started' COMMENT '学习状态：not_started-未开始/learning-学习中/completed-已完成', `exam_status` VARCHAR(20) DEFAULT 'not_registered' COMMENT '考试状态：not_registered-未报名/registered-已报名/passed-已通过/failed-未通过',

-- 费用信息 `course_fee` DECIMAL(10,2) COMMENT '课程费', `exam_fee` DECIMAL(10,2) COMMENT '考试费', `certification_fee` DECIMAL(10,2) COMMENT '认证费',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_order_id` (`order_id`), KEY `idx_order_no` (`order_no`), KEY `idx_student_name` (`student_name`), KEY `idx_course_name` (`course_name`), KEY `idx_course_type` (`course_type`), KEY `idx_learning_status` (`learning_status`), KEY `idx_exam_status` (`exam_status`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='个人培训订单详情表';

-- 家政服务订单详情表 CREATE TABLE `publicbiz_domestic_order` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联字段 `order_id` BIGINT NOT NULL COMMENT '订单ID', `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

-- 客户信息 `customer_oneid` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '客户OneID GUID', `customer_name` VARCHAR(50) NOT NULL COMMENT '客户姓名', `customer_phone` VARCHAR(20) NOT NULL COMMENT '客户电话', `customer_address` VARCHAR(500) NOT NULL COMMENT '服务地址', `customer_remark` VARCHAR(2000) COMMENT '客户备注',

-- 服务信息 `service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID', `service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称', `service_package_id` BIGINT COMMENT '服务套餐ID', `service_package_name` VARCHAR(200) COMMENT '服务套餐名称', `service_start_date` DATE COMMENT '服务开始日期', `service_end_date` DATE COMMENT '服务结束日期', `service_duration` VARCHAR(50) COMMENT '服务时长', `service_frequency` VARCHAR(50) COMMENT '服务频次', `service_package_thumbnail` varchar(500) DEFAULT NULL COMMENT '套餐主图URL', `service_package_price` decimal(10,2) NOT NULL COMMENT '套餐价格', `service_package_original_price` decimal(10,2) DEFAULT NULL COMMENT '套餐原价', `service_package_unit` varchar(20) NOT NULL COMMENT '价格单位：次/项/天/月', `service_package_duration` varchar(100) DEFAULT NULL COMMENT '服务时长，如：4小时、26天、90天', `service_package_type` varchar(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐', `service_description` text COMMENT '服务描述', `service_details` longtext COMMENT '详细服务内容，富文本格式', `service_process` longtext COMMENT '服务流程，富文本格式', `purchase_notice` text COMMENT '购买须知', `service_times` int(11) DEFAULT '1' COMMENT '服务次数', `unit_price` decimal(10,2) NOT NULL COMMENT '单价', `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额', `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额', `actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额', `service_address` varchar(500) NOT NULL COMMENT '服务地址', `service_address_detail` varchar(200) DEFAULT NULL COMMENT '详细地址', `service_latitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址纬度', `service_longitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址经度', `service_schedule` json DEFAULT NULL COMMENT '服务时间安排(JSON格式)',

-- 服务人员信息 `practitioner_oneid` VARCHAR(36) COMMENT '服务人员OneID', `practitioner_name` VARCHAR(50) COMMENT '服务人员姓名', `practitioner_phone` VARCHAR(20) COMMENT '服务人员电话', `agency_id` BIGINT COMMENT '服务机构ID', `agency_name` VARCHAR(200) COMMENT '服务机构名称',

-- 任务信息 `task_count` INT DEFAULT 0 COMMENT '任务总数', `completed_task_count` INT DEFAULT 0 COMMENT '已完成任务数', `task_progress` DECIMAL(5,2) DEFAULT 0.00 COMMENT '任务进度百分比',

-- 费用信息 `service_fee` DECIMAL(10,2) COMMENT '服务费', `agency_fee` DECIMAL(10,2) COMMENT '机构费', `platform_fee` DECIMAL(10,2) COMMENT '平台费',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_order_id` (`order_id`), KEY `idx_order_no` (`order_no`), KEY `idx_customer_name` (`customer_name`), KEY `idx_service_type` (`service_type`), KEY `idx_practitioner_id` (`practitioner_id`), KEY `idx_agency_id` (`agency_id`), KEY `idx_service_start_date` (`service_start_date`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='家政服务订单详情表';

-- 家政服务任务表 CREATE TABLE `publicbiz_domestic_task` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联字段 `order_id` BIGINT NOT NULL COMMENT '订单ID', `order_no` VARCHAR(50) NOT NULL COMMENT '订单号', `domestic_order_id` BIGINT NOT NULL COMMENT '家政服务订单ID',

-- 任务信息 `task_no` VARCHAR(50) NOT NULL COMMENT '任务编号', `task_sequence` INT NOT NULL COMMENT '任务序号', `task_name` VARCHAR(200) NOT NULL COMMENT '任务名称', `task_description` TEXT COMMENT '任务描述', `task_type` VARCHAR(50) NOT NULL COMMENT '任务类型', `task_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending-待分配/assigned-已分配/in_progress-进行中/completed-已完成/cancelled-已取消',

-- 时间信息 `planned_start_time` DATETIME COMMENT '计划开始时间', `planned_end_time` DATETIME COMMENT '计划结束时间', `actual_start_time` DATETIME COMMENT '实际开始时间', `actual_end_time` DATETIME COMMENT '实际结束时间', `duration` VARCHAR(50) COMMENT '任务时长',

-- 服务人员信息 `practitioner_oneid` VARCHAR(36) COMMENT '服务人员OneID', `practitioner_id` BIGINT COMMENT '服务人员ID', `practitioner_name` VARCHAR(50) COMMENT '服务人员姓名', `practitioner_phone` VARCHAR(20) COMMENT '服务人员电话',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_task_no` (`task_no`), KEY `idx_order_id` (`order_id`), KEY `idx_order_no` (`order_no`), KEY `idx_domestic_order_id` (`domestic_order_id`), KEY `idx_task_sequence` (`task_sequence`), KEY `idx_task_status` (`task_status`), KEY `idx_practitioner_id` (`practitioner_id`), KEY `idx_planned_start_time` (`planned_start_time`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='家政服务任务表';

-- ==================== 结算中心相关表 ====================

-- 结算记录表 CREATE TABLE `publicbiz_settlement` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 结算基本信息 `settlement_no` VARCHAR(50) NOT NULL COMMENT '结算单号', `settlement_type` VARCHAR(30) NOT NULL COMMENT '结算类型：order-订单结算/practitioner-阿姨结算/agency-机构结算/platform-平台结算', `settlement_period` VARCHAR(20) NOT NULL COMMENT '结算周期：daily-日结/weekly-周结/monthly-月结', `settlement_date` DATE NOT NULL COMMENT '结算日期',

-- 关联信息 `order_id` BIGINT COMMENT '关联订单ID', `order_no` VARCHAR(50) COMMENT '关联订单号', `practitioner_oneid` VARCHAR(36) COMMENT '关联阿姨OneID', `agency_id` BIGINT COMMENT '关联机构ID',

-- 金额信息 `total_amount` DECIMAL(12,2) NOT NULL COMMENT '结算总金额', `settlement_amount` DECIMAL(12,2) NOT NULL COMMENT '实际结算金额', `commission_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '佣金金额', `tax_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '税费金额', `other_deduction` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '其他扣减',

-- 结算状态 `settlement_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败', `settlement_time` DATETIME COMMENT '结算时间', `settlement_method` VARCHAR(30) COMMENT '结算方式：bank_transfer-银行转账/alipay-支付宝/wechat-微信/other-其他',

-- 操作信息 `operator_id` BIGINT COMMENT '操作人ID', `operator_name` VARCHAR(50) COMMENT '操作人姓名', `remark` TEXT COMMENT '备注',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_settlement_no` (`settlement_no`), KEY `idx_tenant_id` (`tenant_id`), KEY `idx_settlement_type` (`settlement_type`), KEY `idx_settlement_period` (`settlement_period`), KEY `idx_settlement_date` (`settlement_date`), KEY `idx_order_id` (`order_id`), KEY `idx_practitioner_id` (`practitioner_id`), KEY `idx_agency_id` (`agency_id`), KEY `idx_settlement_status` (`settlement_status`), KEY `idx_operator_id` (`operator_id`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='结算记录表';

-- 结算明细表 CREATE TABLE `publicbiz_settlement_detail` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联字段 `settlement_id` BIGINT NOT NULL COMMENT '结算记录ID', `settlement_no` VARCHAR(50) NOT NULL COMMENT '结算单号', `order_id` BIGINT COMMENT '订单ID', `order_no` VARCHAR(50) COMMENT '订单号',

-- 明细信息 `item_type` VARCHAR(30) NOT NULL COMMENT '项目类型：service_fee-服务费/platform_fee-平台费/commission-佣金/tax-税费/deduction-扣减', `item_name` VARCHAR(200) NOT NULL COMMENT '项目名称', `item_description` TEXT COMMENT '项目描述', `amount` DECIMAL(12,2) NOT NULL COMMENT '金额', `rate` DECIMAL(5,2) COMMENT '费率百分比', `calculation_basis` VARCHAR(200) COMMENT '计算依据',

-- 阿姨信息 `practitioner_oneid` VARCHAR(36) NOT NULL COMMENT '阿姨OneID', `practitioner_name` VARCHAR(50) NOT NULL COMMENT '阿姨姓名', `practitioner_phone` VARCHAR(20) COMMENT '阿姨电话', `practitioner_bank_account` VARCHAR(50) COMMENT '阿姨银行账户', `practitioner_bank_name` VARCHAR(100) COMMENT '阿姨开户行',

    -- 机构信息

`agency_id` BIGINT NOT NULL COMMENT '机构ID', `agency_name` VARCHAR(200) NOT NULL COMMENT '机构名称', `agency_contact` VARCHAR(50) COMMENT '机构联系人', `agency_phone` VARCHAR(20) COMMENT '机构联系电话', `agency_bank_account` VARCHAR(50) COMMENT '机构银行账户', `agency_bank_name` VARCHAR(100) COMMENT '机构开户行',

-- 结算信息 `settlement_period` VARCHAR(20) NOT NULL COMMENT '结算周期：weekly-周结/monthly-月结', `settlement_date` DATE NOT NULL COMMENT '结算日期', `order_count` INT NOT NULL DEFAULT 0 COMMENT '订单数量', `total_amount` DECIMAL(12,2) NOT NULL COMMENT '总金额', `practitioner_amount` DECIMAL(12,2) NOT NULL COMMENT '阿姨应得金额', `agency_amount` DECIMAL(12,2) NOT NULL COMMENT '机构应得金额', `platform_amount` DECIMAL(12,2) NOT NULL COMMENT '平台分成金额', `commission_rate` DECIMAL(5,2) NOT NULL COMMENT '佣金比例',

-- 结算状态 `settlement_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败', `settlement_time` DATETIME COMMENT '结算时间', `settlement_method` VARCHAR(30) COMMENT '结算方式', `settlement_remark` TEXT COMMENT '结算备注',

-- 索引 PRIMARY KEY (`id`), KEY `idx_settlement_id` (`settlement_id`), KEY `idx_settlement_no` (`settlement_no`), KEY `idx_order_id` (`order_id`), KEY `idx_order_no` (`order_no`), KEY `idx_item_type` (`item_type`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='结算明细表';

-- ==================== 对账单管理相关表 ====================

-- 对账单主表 CREATE TABLE `publicbiz_reconciliation_statement` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 对账单基本信息 `statement_no` VARCHAR(50) NOT NULL COMMENT '对账单号，如：ZD20240816001', `statement_type` VARCHAR(30) NOT NULL COMMENT '对账单类型：agency-机构对账/platform-平台对账/practitioner-阿姨对账', `generation_time` DATETIME NOT NULL COMMENT '生成时间',

-- 关联信息 `agency_id` BIGINT COMMENT '关联机构ID', `agency_name` VARCHAR(200) COMMENT '机构名称', `practitioner_oneid` VARCHAR(36) COMMENT '关联阿姨OneID', `practitioner_name` VARCHAR(50) COMMENT '阿姨姓名',

-- 金额信息 `total_amount` DECIMAL(12,2) NOT NULL COMMENT '对账总金额', `agency_amount` DECIMAL(12,2) NOT NULL COMMENT '机构分成金额', `platform_amount` DECIMAL(12,2) NOT NULL COMMENT '平台分成金额', `agency_ratio` DECIMAL(5,2) NOT NULL COMMENT '机构分成比例(%)', `platform_ratio` DECIMAL(5,2) NOT NULL COMMENT '平台分成比例(%)',

-- 订单信息 `order_count` INT NOT NULL DEFAULT 0 COMMENT '包含订单数量', `order_list` JSON COMMENT '包含订单列表（JSON格式）',

-- 对账状态 `reconciliation_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '对账状态：pending-待对账确认/confirmed-已确认/paid-已支付/cancelled-已取消', `confirmation_time` DATETIME COMMENT '确认时间', `payment_time` DATETIME COMMENT '支付时间', `cancellation_time` DATETIME COMMENT '取消时间', `cancellation_reason` TEXT COMMENT '取消原因',

-- 操作信息 `operator_id` BIGINT COMMENT '操作人ID', `operator_name` VARCHAR(50) COMMENT '操作人姓名', `remark` TEXT COMMENT '备注',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_statement_no` (`statement_no`), KEY `idx_tenant_id` (`tenant_id`), KEY `idx_statement_type` (`statement_type`), KEY `idx_generation_time` (`generation_time`), KEY `idx_agency_id` (`agency_id`), KEY `idx_practitioner_oneid` (`practitioner_oneid`), KEY `idx_reconciliation_status` (`reconciliation_status`), KEY `idx_operator_id` (`operator_id`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='对账单主表';

-- 对账单明细表 CREATE TABLE `publicbiz_reconciliation_detail` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联字段 `statement_id` BIGINT NOT NULL COMMENT '对账单ID', `statement_no` VARCHAR(50) NOT NULL COMMENT '对账单号', `order_id` BIGINT NOT NULL COMMENT '订单ID', `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

-- 订单信息 `package_name` VARCHAR(200) NOT NULL COMMENT '套餐名称', `agency_name` VARCHAR(200) COMMENT '家政机构', `practitioner_name` VARCHAR(50) COMMENT '阿姨姓名', `order_time` DATETIME NOT NULL COMMENT '下单时间', `completion_time` DATETIME COMMENT '完成时间', `order_status` VARCHAR(20) NOT NULL COMMENT '订单状态：已完成/服务中', `settlement_status` VARCHAR(20) NOT NULL COMMENT '结算状态：待结算/结算中/已结算/结算失败', `order_amount` DECIMAL(10,2) NOT NULL COMMENT '订单金额',

-- 分成信息 `agency_amount` DECIMAL(10,2) NOT NULL COMMENT '机构分成金额', `platform_amount` DECIMAL(10,2) NOT NULL COMMENT '平台分成金额', `agency_ratio` DECIMAL(5,2) NOT NULL COMMENT '机构分成比例(%)', `platform_ratio` DECIMAL(5,2) NOT NULL COMMENT '平台分成比例(%)',

-- 索引 PRIMARY KEY (`id`), KEY `idx_statement_id` (`statement_id`), KEY `idx_statement_no` (`statement_no`), KEY `idx_order_id` (`order_id`), KEY `idx_order_no` (`order_no`), KEY `idx_agency_name` (`agency_name`), KEY `idx_practitioner_name` (`practitioner_name`), KEY `idx_order_status` (`order_status`), KEY `idx_settlement_status` (`settlement_status`), KEY `idx_order_time` (`order_time`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='对账单明细表';

-- 发票管理表 CREATE TABLE `publicbiz_invoice` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联对账单信息 `statement_id` BIGINT NOT NULL COMMENT '关联对账单ID', `statement_no` VARCHAR(50) NOT NULL COMMENT '对账单号', `agency_id` BIGINT NOT NULL COMMENT '机构ID', `agency_name` VARCHAR(200) NOT NULL COMMENT '机构名称', `reconciliation_amount` DECIMAL(12,2) NOT NULL COMMENT '对账金额',

-- 发票信息 `invoice_no` VARCHAR(50) COMMENT '发票号码', `invoice_date` DATE COMMENT '开票日期', `invoice_type` VARCHAR(30) COMMENT '发票类型：增值税普通发票/增值税专用发票/电子发票', `invoice_amount` DECIMAL(12,2) COMMENT '发票金额', `tax_rate` DECIMAL(5,2) DEFAULT 6.00 COMMENT '税率(%)', `tax_amount` DECIMAL(12,2) COMMENT '税额', `invoice_status` VARCHAR(20) NOT NULL DEFAULT 'not_invoiced' COMMENT '开票状态：not_invoiced-未开票/invoiced-已开票/partially_invoiced-部分开票',

-- 开票信息维护 `invoicing_status` VARCHAR(20) NOT NULL DEFAULT 'not_invoiced' COMMENT '开票状态：not_invoiced-未开票/invoicing-开票中/invoiced-已开票/failed-开票失败', `invoicing_remark` TEXT COMMENT '开票备注',

-- 操作信息 `operator_id` BIGINT COMMENT '操作人ID', `operator_name` VARCHAR(50) COMMENT '操作人姓名', `remark` TEXT COMMENT '备注',

-- 索引 PRIMARY KEY (`id`), KEY `idx_statement_id` (`statement_id`), KEY `idx_statement_no` (`statement_no`), KEY `idx_agency_id` (`agency_id`), KEY `idx_invoice_no` (`invoice_no`), KEY `idx_invoice_date` (`invoice_date`), KEY `idx_invoice_status` (`invoice_status`), KEY `idx_invoicing_status` (`invoicing_status`), KEY `idx_operator_id` (`operator_id`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='发票管理表';

-- 订单资金列表可以通过以下现有表合成：-- 1. publicbiz_domestic_order - 订单基本信息 -- 2. publicbiz_settlement - 结算信息 -- 3. publicbiz_reconciliation_statement + publicbiz_reconciliation_detail - 对账信息 -- 4. publicbiz_invoice - 发票信息 -- -- 合成查询示例：-- SELECT -- o.order_id, o.order_no, o.service_package_name as package_name, -- o.agency_name, o.practitioner_name, o.total_amount, -- s.settlement_status, s.settlement_time, s.settlement_method, -- rd.reconciliation_status, rs.statement_no, -- i.invoice_status, i.invoice_no, i.invoice_date -- FROM publicbiz_domestic_order o -- LEFT JOIN publicbiz_settlement s ON o.order_id = s.order_id -- LEFT JOIN publicbiz_reconciliation_detail rd ON o.order_id = rd.order_id -- LEFT JOIN publicbiz_reconciliation_statement rs ON rd.statement_id = rs.id -- LEFT JOIN publicbiz_invoice i ON rs.id = i.statement_id -- WHERE o.deleted = 0;

-- 待结算订单可以通过以下查询从订单主表获取：-- SELECT -- o.id as order_id, o.order_no, o.total_amount, -- do.service_package_name as package_name, do.agency_name, do.practitioner_name, -- o.create_time as order_time, do.service_end_date as completion_time, -- o.order_status, o.settlement_status, o.is_selected_for_reconciliation, -- o.selection_time, o.selector_id, o.selector_name -- FROM publicbiz_order o -- LEFT JOIN publicbiz_domestic_order do ON o.id = do.order_id -- WHERE o.order_status = 'completed' -- AND o.payment_status = 'paid' -- AND o.settlement_status = 'pending' -- AND o.deleted = 0;
