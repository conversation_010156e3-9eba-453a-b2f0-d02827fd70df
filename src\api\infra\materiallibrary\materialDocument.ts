import request from '@/config/axios'
import qs from 'qs'

// 文档VO
export interface DocumentVO {
  id: number
  documentName: string
  documentUrl: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId: number
  description?: string
  createTime?: string
  visibleOrgId?: number
  visibleOrgName?: string
}

// 文档列表
export const getDocumentList = (params: {
  pageNo: number
  pageSize: number
  documentName?: string
  sourceOrgId?: number
  categoryId?: number
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.get({ url: '/system/material/document/list', params })
}

// 新增文档
export const createDocument = (data: {
  documentName: string
  documentUrl: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/document/create', data })
}

// 编辑文档
export const updateDocument = (data: {
  id: number
  documentName: string
  documentUrl: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/document/update', data })
}

// 删除文档
export const deleteDocument = (id: number) => {
  return request.post({
    url: '/system/material/document/delete',
    data: qs.stringify({ id }),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 文档详情
export const getDocumentDetail = (id: number) => {
  return request.get({ url: '/system/material/document/detail', params: { id } })
}
