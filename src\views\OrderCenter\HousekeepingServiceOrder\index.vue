<!--
  页面名称：家政服务订单列表
  功能描述：展示家政服务订单列表，支持搜索、分页、编辑、删除、查看详情、操作日志
-->
<template>
  <div class="housekeeping-service-order">
    <!-- KPI统计卡片 -->
    <div class="kpi-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="kpi-card">
            <div class="kpi-value">{{ statistics.totalOrders }}</div>
            <div class="kpi-label">总订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="kpi-card">
            <div class="kpi-value">{{ statistics.pendingOrders }}</div>
            <div class="kpi-label">待处理订单</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="kpi-card">
            <div class="kpi-value">¥{{ formatAmount(statistics.monthlyAmount) }}</div>
            <div class="kpi-label">本月订单金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="kpi-card">
            <div class="kpi-value">{{ statistics.completionRate }}%</div>
            <div class="kpi-label">订单完成率</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和过滤区域 -->
    <div class="search-filter-section">
      <el-row :gutter="20" align="middle" justify="space-between">
        <el-col :span="18">
          <el-row :gutter="12" align="middle">
            <el-col :span="3">
              <el-select
                v-model="searchForm.orderStatus"
                placeholder="全部订单状态"
                clearable
                style="width: 100%"
                size="default"
              >
                <el-option label="待派单" value="pending" />
                <el-option label="已派单" value="assigned" />
                <el-option label="服务中" value="in_service" />
                <el-option label="已完成" value="completed" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select
                v-model="searchForm.paymentStatus"
                placeholder="全部支付状态"
                clearable
                style="width: 100%"
                size="default"
              >
                <el-option label="未支付" value="unpaid" />
                <el-option label="已支付" value="paid" />
                <el-option label="部分支付" value="partial" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select
                v-model="searchForm.serviceType"
                placeholder="全部服务类型"
                clearable
                style="width: 100%"
                size="default"
              >
                <el-option label="月嫂服务" value="maternity" />
                <el-option label="深度保洁" value="deep_cleaning" />
                <el-option label="小时工" value="hourly" />
                <el-option label="育儿嫂服务" value="nanny" />
              </el-select>
            </el-col>
            <el-col :span="9">
              <el-input
                v-model="searchForm.keyword"
                placeholder="搜索客户姓名、服务人员、服务机构..."
                clearable
                @keyup.enter="handleSearch"
                size="default"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="2">
              <el-button type="primary" @click="handleSearch" size="default">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6" style="text-align: right">
          <el-button type="success" @click="handleAdd" size="default">
            <el-icon><Plus /></el-icon>
            新建家政服务订单
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderNumber" label="订单号" width="150">
          <template #default="scope">
            <el-link type="primary" @click="handleView(scope.row)">
              {{ scope.row.orderNumber }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="客户信息" width="180">
          <template #default="scope">
            <div>
              <div>{{ scope.row.customerName }}</div>
              <div class="text-gray">{{ scope.row.customerPhone }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="serviceType" label="服务类型" width="120" />

        <el-table-column prop="servicePersonnel" label="服务人员" width="120" />

        <el-table-column prop="serviceAgency" label="服务机构" width="200" />

        <el-table-column prop="serviceAmount" label="服务金额" width="120">
          <template #default="scope"> ¥{{ formatAmount(scope.row.serviceAmount) }} </template>
        </el-table-column>

        <el-table-column label="任务进度" width="150">
          <template #default="scope">
            <div class="progress-container">
              <div class="progress-text"
                >{{ scope.row.completedTasks }}/{{ scope.row.totalTasks }}</div
              >
              <el-progress
                :percentage="getProgressPercentage(scope.row.completedTasks, scope.row.totalTasks)"
                :stroke-width="8"
                :show-text="false"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="订单状态" width="100">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ getOrderStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="支付状态" width="100">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
              {{ getPaymentStatusText(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="appointmentTime" label="预约时间" width="180" />

        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleView(scope.row)"> 查看 </el-button>
            <el-button size="small" type="warning" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
            <el-button size="small" @click="handleOptLog(scope.row)"> 操作日志 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑抽屉 -->
    <AddHousekeepingServiceOrder
      v-model:visible="drawerVisible"
      :order-data="currentOrder"
      @success="handleSuccess"
    />

    <!-- 操作日志抽屉 -->
    <OptLog v-model:visible="optLogVisible" :order-id="currentOrderId" />

    <!-- 查看详情抽屉 -->
    <HousekeepingServiceOrderView
      v-model:visible="viewDrawerVisible"
      :order-id="currentOrder?.id"
      @edit="handleEdit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import AddHousekeepingServiceOrder from './components/AddHousekeepingServiceOrder.vue'
import OptLog from './components/OptLog.vue'
import HousekeepingServiceOrderView from './components/HousekeepingServiceOrderView.vue'

// 响应式数据
const loading = ref(false)
const drawerVisible = ref(false)
const optLogVisible = ref(false)
const viewDrawerVisible = ref(false)
const currentOrder = ref(null)
const currentOrderId = ref('')

// 搜索表单
const searchForm = reactive({
  orderStatus: '',
  paymentStatus: '',
  serviceType: '',
  keyword: ''
})

// 统计数据
const statistics = reactive({
  totalOrders: 18,
  pendingOrders: 0,
  monthlyAmount: 2135220,
  completionRate: 92.5
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表格数据
const tableData = ref([
  {
    id: '1',
    orderNumber: 'DS202406001',
    customerName: '李女士',
    customerPhone: '13812345678',
    serviceType: '月嫂服务',
    servicePersonnel: '张阿姨',
    serviceAgency: '爱家月嫂服务中心',
    serviceAmount: 12800,
    completedTasks: 15,
    totalTasks: 30,
    orderStatus: 'in_service',
    paymentStatus: 'paid',
    appointmentTime: '2024/6/1 09:00:00'
  },
  {
    id: '2',
    orderNumber: 'DS202406002',
    customerName: '王先生',
    customerPhone: '13987654321',
    serviceType: '深度保洁',
    servicePersonnel: '李师傅团队',
    serviceAgency: '清洁无忧家政公司',
    serviceAmount: 1500,
    completedTasks: 1,
    totalTasks: 1,
    orderStatus: 'completed',
    paymentStatus: 'paid',
    appointmentTime: '2024/6/15 14:00:00'
  },
  {
    id: '3',
    orderNumber: 'DS202406003',
    customerName: '赵小姐',
    customerPhone: '13711112222',
    serviceType: '小时工',
    servicePersonnel: '未指派',
    serviceAgency: '',
    serviceAmount: 320,
    completedTasks: 0,
    totalTasks: 4,
    orderStatus: 'pending',
    paymentStatus: 'unpaid',
    appointmentTime: '2024/6/28 10:00:00'
  },
  {
    id: '4',
    orderNumber: 'DS202406004',
    customerName: '陈女士',
    customerPhone: '13655556666',
    serviceType: '育儿嫂服务',
    servicePersonnel: '母婴护理专家机构',
    serviceAgency: '母婴护理专家机构',
    serviceAmount: 9800,
    completedTasks: 0,
    totalTasks: 31,
    orderStatus: 'assigned',
    paymentStatus: 'unpaid',
    appointmentTime: '2024/7/1 08:00:00'
  }
])

// 方法定义
const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

const getProgressPercentage = (completed: number, total: number): number => {
  if (total === 0) return 0
  return Math.round((completed / total) * 100)
}

const getOrderStatusType = (status: string): string => {
  const statusMap = {
    pending: 'warning',
    assigned: 'info',
    in_service: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status: string): string => {
  const statusMap = {
    pending: '待派单',
    assigned: '已派单',
    in_service: '服务中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const getPaymentStatusType = (status: string): string => {
  const statusMap = {
    unpaid: 'warning',
    paid: 'success',
    partial: 'info'
  }
  return statusMap[status] || 'info'
}

const getPaymentStatusText = (status: string): string => {
  const statusMap = {
    unpaid: '未支付',
    paid: '已支付',
    partial: '部分支付'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  pagination.page = 1
  fetchList()
}

const handleAdd = () => {
  currentOrder.value = null
  drawerVisible.value = true
}

const handleEdit = (row: any) => {
  currentOrder.value = { ...row }
  drawerVisible.value = true
}

const handleView = (row: any) => {
  currentOrder.value = row
  viewDrawerVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const index = tableData.value.findIndex((item) => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const handleOptLog = (row: any) => {
  currentOrderId.value = row.id
  optLogVisible.value = true
}

const handleSuccess = () => {
  drawerVisible.value = false
  fetchList()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

const fetchList = async () => {
  loading.value = true
  try {
    await new Promise((resolve) => setTimeout(resolve, 500))
    statistics.totalOrders = tableData.value.length
    statistics.pendingOrders = tableData.value.filter(
      (item) => item.orderStatus === 'pending'
    ).length
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.housekeeping-service-order {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .kpi-cards {
    margin-bottom: 20px;

    .kpi-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #409eff;
      text-align: center;

      .kpi-value {
        font-size: 28px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
      }

      .kpi-label {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .search-filter-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .el-select {
      .el-input__wrapper {
        box-shadow: 0 0 0 1px #dcdfe6 inset;

        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #409eff inset;
        }
      }
    }

    .el-button {
      border-radius: 4px;
      font-weight: 500;
    }
  }

  .table-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .progress-container {
      .progress-text {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }
    }

    .text-gray {
      color: #999;
      font-size: 12px;
    }

    .pagination-container {
      padding: 20px;
      text-align: right;
    }
  }
}
</style>
