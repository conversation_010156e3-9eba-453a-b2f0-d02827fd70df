import request from '@/config/axios'
import qs from 'qs'

// 图文VO
export interface NewsVO {
  id: number
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId: number
  description?: string
  createTime?: string
  visibleOrgId?: number
  visibleOrgName?: string
}

// 图文列表
export const getNewsList = (params: {
  pageNo: number
  pageSize: number
  name?: string
  sourceOrgId?: number
  categoryId?: number
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.get({ url: '/system/material/news/list', params })
}

// 新增图文
export const createNews = (data: {
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/news/create', data })
}

// 编辑图文
export const updateNews = (data: {
  id: number
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/news/update', data })
}

// 删除图文
export const deleteNews = (id: number) => {
  return request.post({
    url: '/system/material/news/delete',
    data: qs.stringify({ id }),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 图文详情
export const getNewsDetail = (id: number) => {
  return request.get({ url: '/system/material/news/detail', params: { id } })
}
