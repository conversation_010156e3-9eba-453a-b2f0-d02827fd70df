<!--
  页面名称：任务编辑弹窗
  功能描述：编辑任务的基本信息和备注
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑任务"
    width="500px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-form-item label="任务ID">
        <el-input v-model="formData.taskId" disabled />
      </el-form-item>

      <el-form-item label="任务序号">
        <el-input v-model="formData.taskSequence" disabled />
      </el-form-item>

      <el-form-item label="计划服务日期">
        <el-input v-model="formData.plannedDate" disabled />
      </el-form-item>

      <el-form-item label="计划服务内容">
        <el-input v-model="formData.plannedContent" type="textarea" :rows="3" disabled />
      </el-form-item>

      <el-form-item label="任务状态">
        <el-tag :type="getTaskStatusType(formData.taskStatus)" size="small">
          {{ getTaskStatusText(formData.taskStatus) }}
        </el-tag>
      </el-form-item>

      <el-form-item label="当前服务人员">
        <el-input v-model="formData.currentPersonnel" disabled />
      </el-form-item>

      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="formData.remarks"
          type="textarea"
          :rows="4"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  visible: boolean
  taskData: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [taskData: any]
}>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const formRef = ref<FormInstance>()
const loading = ref(false)

const formData = ref({
  taskId: '',
  taskSequence: '',
  plannedDate: '',
  plannedContent: '',
  taskStatus: '',
  currentPersonnel: '',
  remarks: ''
})

const formRules: FormRules = {
  remarks: [{ max: 500, message: '备注信息不能超过500个字符', trigger: 'blur' }]
}

// 监听任务数据变化，更新表单
watch(
  () => props.taskData,
  (newData) => {
    if (newData) {
      formData.value = {
        taskId: newData.taskId || '',
        taskSequence: newData.taskSequence || '',
        plannedDate: newData.plannedDate || '',
        plannedContent: newData.plannedContent || '',
        taskStatus: newData.taskStatus || '',
        currentPersonnel: newData.currentPersonnel || '',
        remarks: newData.remarks || ''
      }
    }
  },
  { immediate: true }
)

const getTaskStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger',
    assigned: 'info'
  }
  return statusMap[status] || 'info'
}

const getTaskStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '待指派',
    in_progress: '执行中',
    completed: '已完成',
    cancelled: '已取消',
    assigned: '待执行'
  }
  return statusMap[status] || '未知'
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 更新任务数据
    const updatedTask = {
      ...props.taskData,
      remarks: formData.value.remarks
    }

    emit('success', updatedTask)
    ElMessage.success('任务更新成功')
    handleClose()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}
</style>
