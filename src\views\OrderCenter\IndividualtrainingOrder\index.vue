<!--
  页面名称：个人培训与认证订单首页
  功能描述：展示个人培训与认证订单列表，支持搜索、分页、新增、编辑、删除、查看操作日志
-->
<template>
  <div class="individual-training-index">
    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">18</div>
            <div class="stat-label">总订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">0</div>
            <div class="stat-label">待处理订单</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">¥2,135,220</div>
            <div class="stat-label">本月订单金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">92.5%</div>
            <div class="stat-label">订单完成率</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" @submit.prevent="onSearch">
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.orderStatus"
            placeholder="全部订单状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部订单状态" value="" />
            <el-option label="待支付" value="pending_payment" />
            <el-option label="学习中" value="learning" />
            <el-option label="待考试" value="pending_exam" />
            <el-option label="已完成" value="completed" />
            <el-option label="已通过" value="passed" />
            <el-option label="已关闭" value="closed" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付状态">
          <el-select
            v-model="searchForm.paymentStatus"
            placeholder="全部支付状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部支付状态" value="" />
            <el-option label="未支付" value="unpaid" />
            <el-option label="已支付" value="paid" />
            <el-option label="已退款" value="refunded" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            v-model="searchForm.orderType"
            placeholder="全部类型"
            clearable
            style="width: 150px"
          >
            <el-option label="全部类型" value="" />
            <el-option label="个人培训" value="training" />
            <el-option label="考试认证" value="certification" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索学员姓名、课程名称..."
            style="width: 300px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="action-buttons">
        <el-button type="success" @click="onAdd">
          <el-icon><Plus /></el-icon>
          新建个人培训订单
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderNumber" label="订单号" width="150">
          <template #default="scope">
            <el-link type="primary" @click="onView(scope.row)">{{ scope.row.orderNumber }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="studentName" label="学员姓名" width="120" />
        <el-table-column prop="orderType" label="订单类型" width="120">
          <template #default="scope">
            <el-tag :type="getOrderTypeTagType(scope.row.orderType)">
              {{ getOrderTypeText(scope.row.orderType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="courseName" label="课程/考试项目" width="250" />
        <el-table-column prop="orderSource" label="订单来源" width="120">
          <template #default="scope">
            <el-tooltip content="已添加到剪贴板" placement="top">
              <span class="copy-text" @click="copyToClipboard(scope.row.orderSource)">
                {{ scope.row.orderSource || '-' }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="orderAmount" label="订单金额" width="120">
          <template #default="scope"> ¥{{ scope.row.orderAmount }} </template>
        </el-table-column>
        <el-table-column prop="orderStatus" label="订单状态" width="120">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ getOrderStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" label="支付状态" width="120">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
              {{ getPaymentStatusText(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="learningStatus" label="学习/考试状态" width="120">
          <template #default="scope">
            <el-tag :type="getLearningStatusType(scope.row.learningStatus)">
              {{ getLearningStatusText(scope.row.learningStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="registrationTime" label="报名时间" width="120" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="onView(scope.row)">查看</el-button>
            <el-button size="small" type="warning" @click="onEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="onDelete(scope.row)">删除</el-button>
            <el-button size="small" @click="onOptLog(scope.row)">操作日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑抽屉 -->
    <AddIndividualtrainingOrder
      v-model:visible="drawerVisible"
      :edit-data="editData"
      @success="onSuccess"
    />

    <!-- 查看详情抽屉 -->
    <IndividualtrainingOrderView
      v-model:visible="viewDrawerVisible"
      :order-data="currentOrderData"
      @edit="onEditFromView"
      @order-status-updated="onOrderStatusUpdated"
    />

    <!-- 操作日志抽屉 -->
    <OptLog v-model:visible="optLogVisible" :order-id="currentOrderId" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import AddIndividualtrainingOrder from './components/AddIndividualtrainingOrder.vue'
import IndividualtrainingOrderView from './components/IndividualtrainingOrderView.vue'
import OptLog from './components/OptLog.vue'

// 响应式数据
/** 搜索表单数据 */
const searchForm = ref({
  orderStatus: '',
  paymentStatus: '',
  orderType: '',
  keyword: ''
})

/** 表格数据 */
const tableData = ref([
  {
    id: 1,
    orderNumber: 'PT202406001',
    studentName: '王小明',
    orderType: 'training',
    courseName: '项目管理PMP认证课程',
    orderSource: '线上小程序',
    orderAmount: '4,500',
    orderStatus: 'in_progress',
    paymentStatus: 'paid',
    learningStatus: 'learning',
    registrationTime: '2024-06-10'
  },
  {
    id: 2,
    orderNumber: 'PT202406003',
    studentName: '孙同学',
    orderType: 'training',
    courseName: 'Python编程基础课程',
    orderSource: '线下报名',
    orderAmount: '2,800',
    orderStatus: 'in_progress',
    paymentStatus: 'paid',
    learningStatus: 'learning',
    registrationTime: '2024-06-15'
  },
  {
    id: 3,
    orderNumber: 'CE202406001',
    studentName: '赵同学',
    orderType: 'certification',
    courseName: '高级母婴护理师认证',
    orderSource: '线上小程序',
    orderAmount: '1,200',
    orderStatus: 'pending_execution',
    paymentStatus: 'paid',
    learningStatus: 'pending_exam',
    registrationTime: '2024-06-12'
  },
  {
    id: 4,
    orderNumber: 'CE202406002',
    studentName: '李同学',
    orderType: 'certification',
    courseName: '营养师资格认证',
    orderSource: '线下报名',
    orderAmount: '1,500',
    orderStatus: 'pending_payment',
    paymentStatus: 'pending',
    learningStatus: 'pending_confirmation',
    registrationTime: '2024-06-18'
  },
  {
    id: 5,
    orderNumber: 'CE202406003',
    studentName: '张同学',
    orderType: 'certification',
    courseName: '心理咨询师三级认证',
    orderSource: '线上小程序',
    orderAmount: '2,000',
    orderStatus: 'completed',
    paymentStatus: 'paid',
    learningStatus: 'passed',
    registrationTime: '2024-05-20'
  },
  {
    id: 6,
    orderNumber: 'PT202406002',
    studentName: '陈同学',
    orderType: 'training',
    courseName: '数据分析师认证课程',
    orderSource: '线下报名',
    orderAmount: '3,800',
    orderStatus: 'completed',
    paymentStatus: 'paid',
    learningStatus: 'completed',
    registrationTime: '2024-05-15'
  }
])

/** 分页信息 */
const pagination = ref({
  page: 1,
  size: 10,
  total: 6
})

/** 加载状态 */
const loading = ref(false)

/** 抽屉显示状态 */
const drawerVisible = ref(false)
const viewDrawerVisible = ref(false)
const optLogVisible = ref(false)

/** 编辑数据 */
const editData = ref(null)

/** 当前订单数据 */
const currentOrderData = ref(null)

/** 当前订单ID */
const currentOrderId = ref('')

// 方法
/** 获取订单类型标签类型 */
const getOrderTypeTagType = (type: string) => {
  const typeMap = {
    training: 'primary',
    certification: 'success'
  }
  return typeMap[type] || 'info'
}

/** 获取订单类型文本 */
const getOrderTypeText = (type: string) => {
  const typeMap = {
    training: '个人培训',
    certification: '考试认证'
  }
  return typeMap[type] || '未知'
}

/** 获取订单状态类型 */
const getOrderStatusType = (status: string) => {
  const statusMap = {
    pending_execution: 'warning',
    in_progress: 'primary',
    pending_payment: 'info',
    completed: 'success'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap = {
    pending_execution: '待执行',
    in_progress: '执行中',
    pending_payment: '待支付',
    completed: '已完成'
  }
  return statusMap[status] || '未知'
}

/** 获取支付状态类型 */
const getPaymentStatusType = (status: string) => {
  const statusMap = {
    pending: 'warning',
    paid: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取支付状态文本 */
const getPaymentStatusText = (status: string) => {
  const statusMap = {
    pending: '待支付',
    paid: '已支付',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

/** 获取学习状态类型 */
const getLearningStatusType = (status: string) => {
  const statusMap = {
    learning: 'primary',
    pending_exam: 'warning',
    pending_confirmation: 'info',
    passed: 'success',
    completed: 'success'
  }
  return statusMap[status] || 'info'
}

/** 获取学习状态文本 */
const getLearningStatusText = (status: string) => {
  const statusMap = {
    learning: '学习中',
    pending_exam: '待考试',
    pending_confirmation: '待确认',
    passed: '已通过',
    completed: '已完成'
  }
  return statusMap[status] || '未知'
}

/** 复制到剪贴板 */
const copyToClipboard = (text: string) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      ElMessage.success('已复制到剪贴板')
    })
  } else {
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('已复制到剪贴板')
  }
}

/** 搜索 */
const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}

/** 重置搜索 */
const onReset = () => {
  searchForm.value = {
    orderStatus: '',
    paymentStatus: '',
    orderType: '',
    keyword: ''
  }
  onSearch()
}

/** 获取列表数据 */
const fetchList = async () => {
  loading.value = true
  try {
    // TODO: 调用接口获取数据
    // 模拟接口调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500))
  } catch (error) {
    console.error('获取列表失败:', error)
  } finally {
    loading.value = false
  }
}

/** 新增 */
const onAdd = () => {
  editData.value = null
  drawerVisible.value = true
}

/** 编辑 */
const onEdit = (row: any) => {
  editData.value = { ...row }
  drawerVisible.value = true
}

/** 查看 */
const onView = (row: any) => {
  currentOrderData.value = { ...row }
  viewDrawerVisible.value = true
}

/** 从查看页面编辑 */
const onEditFromView = (data: any) => {
  editData.value = { ...data }
  drawerVisible.value = true
}

/** 删除 */
const onDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const index = tableData.value.findIndex((item) => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      pagination.value.total -= 1
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

/** 操作日志 */
const onOptLog = (row: any) => {
  currentOrderId.value = row.orderNumber
  optLogVisible.value = true
}

/** 成功回调 */
const onSuccess = () => {
  drawerVisible.value = false
  fetchList()
}

/** 订单状态更新回调 */
const onOrderStatusUpdated = (updatedOrder: any) => {
  const index = tableData.value.findIndex((item) => item.id === updatedOrder.id)
  if (index > -1) {
    tableData.value[index] = updatedOrder
    ElMessage.success('订单状态更新成功')
  }
}

/** 分页大小改变 */
const onSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  fetchList()
}

/** 当前页改变 */
const onCurrentChange = (page: number) => {
  pagination.value.page = page
  fetchList()
}

// 生命周期
onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.individual-training-index {
  padding: 20px;

  .statistics-section {
    margin-bottom: 20px;

    .stat-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #409eff;

      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
      }

      .stat-label {
        color: #666;
        font-size: 14px;
      }
    }
  }

  .search-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .table-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .copy-text {
      cursor: pointer;
      color: #409eff;

      &:hover {
        text-decoration: underline;
      }
    }

    .pagination-section {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
