## 新增音频

**接口地址**:`/system/material/voice/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 1,
  "name": "",
  "url": "",
  "sourceOrgId": 0,
  "sourceOrgName": "",
  "categoryId": 0,
  "description": "",
  "visibleOrgId": 0,
  "visibleOrgName": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| voiceSaveReqVO | 管理后台 - 音频素材新增/更新 Request VO | body | true | VoiceSaveReqVO | VoiceSaveReqVO |
| &emsp;&emsp;id | 音频ID |  | false | integer(int64) |  |
| &emsp;&emsp;name | 音频名称 |  | true | string |  |
| &emsp;&emsp;url | 音频URL |  | true | string |  |
| &emsp;&emsp;sourceOrgId | 来源机构ID |  | true | integer(int64) |  |
| &emsp;&emsp;sourceOrgName | 来源机构名称 |  | true | string |  |
| &emsp;&emsp;categoryId | 分类ID |  | false | integer(int64) |  |
| &emsp;&emsp;description | 描述 |  | false | string |  |
| &emsp;&emsp;visibleOrgId | 可视范围机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```

## 删除音频

**接口地址**:`/system/material/voice/delete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 音频详情

**接口地址**:`/system/material/voice/detail`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema                  |
| ------ | ---- | ----------------------- |
| 200    | OK   | CommonResultVoiceRespVO |

**响应参数**:

| 参数名称                   | 参数说明         | 类型              | schema         |
| -------------------------- | ---------------- | ----------------- | -------------- |
| code                       |                  | integer(int32)    | integer(int32) |
| data                       |                  | VoiceRespVO       | VoiceRespVO    |
| &emsp;&emsp;id             | 音频ID           | integer(int64)    |                |
| &emsp;&emsp;name           | 音频名称         | string            |                |
| &emsp;&emsp;url            | 音频URL          | string            |                |
| &emsp;&emsp;sourceOrgId    | 来源机构ID       | integer(int64)    |                |
| &emsp;&emsp;sourceOrgName  | 来源机构名称     | string            |                |
| &emsp;&emsp;categoryId     | 分类ID           | integer(int64)    |                |
| &emsp;&emsp;createTime     | 创建时间         | string(date-time) |                |
| &emsp;&emsp;visibleOrgId   | 可视范围机构ID   | integer(int64)    |                |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 | string            |                |
| msg                        |                  | string            |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"id": 1,
		"name": "示例音频",
		"url": "https://example.com/voice.mp3",
		"sourceOrgId": 1,
		"sourceOrgName": "内部素材库",
		"categoryId": 1,
		"createTime": "",
		"visibleOrgId": 0,
		"visibleOrgName": ""
	},
	"msg": ""
}
```

## 音频列表

**接口地址**:`/system/material/voice/list`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| name           | 音频名称，模糊搜索     | query    | false    | string   |        |
| sourceOrgId    | 来源机构ID             | query    | false    | string   |        |
| categoryId     | 分类ID                 | query    | false    | string   |        |
| visibleOrgId   | 可视范围机构ID         | query    | false    | string   |        |
| visibleOrgName | 可视范围机构名称       | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                            |
| ------ | ---- | --------------------------------- |
| 200    | OK   | CommonResultPageResultVoiceRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultVoiceRespVO | PageResultVoiceRespVO |
| &emsp;&emsp;list | 数据 | array | VoiceRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 音频ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;name | 音频名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;url | 音频URL | string |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgId | 来源机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgName | 来源机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;categoryId | 分类ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgId | 可视范围机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgName | 可视范围机构名称 | string |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1,
				"name": "示例音频",
				"url": "https://example.com/voice.mp3",
				"sourceOrgId": 1,
				"sourceOrgName": "内部素材库",
				"categoryId": 1,
				"createTime": "",
				"visibleOrgId": 0,
				"visibleOrgName": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 编辑音频

**接口地址**:`/system/material/voice/update`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 1,
  "name": "",
  "url": "",
  "sourceOrgId": 0,
  "sourceOrgName": "",
  "categoryId": 0,
  "description": "",
  "visibleOrgId": 0,
  "visibleOrgName": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| voiceSaveReqVO | 管理后台 - 音频素材新增/更新 Request VO | body | true | VoiceSaveReqVO | VoiceSaveReqVO |
| &emsp;&emsp;id | 音频ID |  | false | integer(int64) |  |
| &emsp;&emsp;name | 音频名称 |  | true | string |  |
| &emsp;&emsp;url | 音频URL |  | true | string |  |
| &emsp;&emsp;sourceOrgId | 来源机构ID |  | true | integer(int64) |  |
| &emsp;&emsp;sourceOrgName | 来源机构名称 |  | true | string |  |
| &emsp;&emsp;categoryId | 分类ID |  | false | integer(int64) |  |
| &emsp;&emsp;description | 描述 |  | false | string |  |
| &emsp;&emsp;visibleOrgId | 可视范围机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 字段验证规则

### 语音名称 (name)

- **必填**: 是
- **类型**: 字符串
- **长度**: 1-200字符
- **规则**: 不能为空，不能包含特殊字符

### 语音URL (url)

- **必填**: 是
- **类型**: 字符串
- **长度**: 1-500字符
- **格式**: 有效的URL格式
- **支持格式**: mp3, wav, aac, m4a, ogg, flac, amr, wma等音频格式

### 分类ID (categoryId)

- **必填**: 否
- **类型**: 长整型
- **规则**: 如果提供，必须是有效的分类ID，关联mp_material_category.id

### 微信媒体ID (mediaId)

- **必填**: 否
- **类型**: 字符串
- **长度**: 1-100字符
- **规则**: 微信平台返回的媒体ID

### 文件信息

- **fileName**: 原始文件名，长度1-255字符
- **fileSize**: 文件大小（字节），建议不超过100MB
- **fileType**: 文件MIME类型，如audio/mp3

### 语音时长 (duration)

- **必填**: 否
- **类型**: 整型
- **单位**: 秒
- **范围**: 0-86400（24小时）

### 语音格式 (format)

- **必填**: 否
- **类型**: 字符串
- **长度**: 1-20字符
- **支持格式**: mp3, wav, aac, m4a, ogg, flac, amr, wma

### 素材来源类型 (sourceType)

- **必填**: 否
- **类型**: 整型
- **可选值**: 1-本地上传，2-微信同步，3-外部链接
- **默认值**: 1

### 来源机构 (sourceOrgId/sourceOrgName)

- **sourceOrgId**: 来源机构ID，长整型
- **sourceOrgName**: 来源机构名称，长度1-200字符

### 状态 (status)

- **必填**: 否
- **类型**: 整型
- **可选值**: 0-禁用，1-启用
- **默认值**: 1

### 标签 (tags)

- **必填**: 否
- **类型**: 字符串
- **长度**: 1-500字符
- **格式**: 多个标签用逗号分隔

### 描述 (description)

- **必填**: 否
- **类型**: 字符串
- **长度**: 1-1000字符

### 永久素材 (isPermanent)

- **必填**: 否
- **类型**: 整型
- **可选值**: 0-临时，1-永久
- **默认值**: 0

### 可视范围 (visibleOrgId/visibleOrgName)

- **visibleOrgId**: 可视范围机构ID，长整型
- **visibleOrgName**: 可视范围机构名称，长度1-200字符

## 错误码说明

| 错误码 | 错误信息     | 说明                   |
| ------ | ------------ | ---------------------- |
| 400    | 参数校验失败 | 请求参数不符合验证规则 |
| 404    | 语音不存在   | 指定的语音ID不存在     |
| 500    | 系统内部错误 | 服务器内部错误         |

## 注意事项

1. **文件上传**: 语音文件需要先通过文件上传接口 `/infra/file/upload` 上传，获取URL后再调用语音创建接口
2. **支持格式**: 系统支持常见的音频格式，包括mp3, wav, aac, m4a, ogg, flac, amr, wma等
3. **文件大小**: 建议单个语音文件不超过100MB
4. **微信集成**: 支持与微信公众号平台的素材同步，通过mediaId关联
5. **权限控制**: 用户只能操作自己有权限的机构范围内的语音素材
6. **分页查询**: 列表接口支持分页，建议每页不超过50条记录
7. **缓存策略**: 语音列表数据会有缓存，更新后可能需要等待1-2分钟才能看到最新数据
8. **临时素材**: 临时素材有过期时间限制，过期后会自动清理
9. **标签管理**: 支持多标签分类，标签之间用逗号分隔
10. **状态管理**: 支持启用/禁用状态控制，禁用的素材不会在前端显示

## 前端集成示例

### JavaScript调用示例

```javascript
// 1. 新增语音
const createVoice = async (voiceData) => {
  const response = await fetch('/system/material/voice/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(voiceData)
  })
  return response.json()
}

// 2. 获取语音列表
const getVoiceList = async (params) => {
  const queryString = new URLSearchParams(params).toString()
  const response = await fetch(`/system/material/voice/list?${queryString}`)
  return response.json()
}

// 3. 更新语音
const updateVoice = async (voiceData) => {
  const response = await fetch('/system/material/voice/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(voiceData)
  })
  return response.json()
}

// 4. 删除语音
const deleteVoice = async (id) => {
  const response = await fetch('/system/material/voice/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: `id=${id}`
  })
  return response.json()
}

// 5. 获取语音详情
const getVoiceDetail = async (id) => {
  const response = await fetch(`/system/material/voice/detail?id=${id}`)
  return response.json()
}
```

### Vue3组件使用示例

```vue
<template>
  <div>
    <!-- 语音播放器 -->
    <audio v-if="voiceUrl" :src="voiceUrl" controls></audio>

    <!-- 语音信息显示 -->
    <div v-if="voiceInfo">
      <p>名称: {{ voiceInfo.name }}</p>
      <p>时长: {{ formatDuration(voiceInfo.duration) }}</p>
      <p>格式: {{ voiceInfo.format }}</p>
      <p>文件大小: {{ formatFileSize(voiceInfo.fileSize) }}</p>
      <p>来源类型: {{ getSourceTypeText(voiceInfo.sourceType) }}</p>
      <p>标签: {{ voiceInfo.tags || '无' }}</p>
      <p>状态: {{ voiceInfo.status === 1 ? '启用' : '禁用' }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const voiceUrl = ref('')
const voiceInfo = ref(null)

// 格式化时长显示
const formatDuration = (seconds) => {
  if (!seconds) return '-'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '-'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i]
}

// 获取来源类型文本
const getSourceTypeText = (type) => {
  const types = {
    1: '本地上传',
    2: '微信同步',
    3: '外部链接'
  }
  return types[type] || '未知'
}

// 加载语音详情
const loadVoiceDetail = async (id) => {
  try {
    const response = await fetch(`/system/material/voice/detail?id=${id}`)
    const result = await response.json()
    if (result.code === 0) {
      voiceInfo.value = result.data
      voiceUrl.value = result.data.url
    }
  } catch (error) {
    console.error('加载语音详情失败:', error)
  }
}
</script>
```
