<template>
  <el-dialog
    :model-value="visible"
    title="批量导入师资"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @update:model-value="(val) => emit('update:visible', val)"
    @close="handleClose"
  >
    <!-- 步骤指示器 -->
    <div class="import-steps">
      <div class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <div class="step-circle">1</div>
        <div class="step-text">下载模板</div>
      </div>
      <div class="step-line" :class="{ completed: currentStep > 1 }"></div>
      <div class="step-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <div class="step-circle">2</div>
        <div class="step-text">上传文件</div>
      </div>
      <div class="step-line" :class="{ completed: currentStep > 2 }"></div>
      <div class="step-item" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
        <div class="step-circle">3</div>
        <div class="step-text">数据预览</div>
      </div>
      <div class="step-line" :class="{ completed: currentStep > 3 }"></div>
      <div class="step-item" :class="{ active: currentStep >= 4 }">
        <div class="step-circle">4</div>
        <div class="step-text">确认导入</div>
      </div>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 步骤1：下载模板 -->
      <div v-if="currentStep === 1" class="step-panel">
        <h3>下载Excel模板</h3>
        <p class="step-desc"
          >请先下载Excel模板,按照模板格式填写师资信息,然后上传文件进行批量导入。</p
        >

        <div class="template-fields">
          <h4>模板字段说明：</h4>
          <ul>
            <li><strong>讲师姓名*</strong>：必填，讲师真实姓名</li>
            <li><strong>讲师类型*</strong>：必填，内部讲师/外部讲师</li>
            <li><strong>业务模块*</strong>：必填，高校业务/家政业务/培训业务/认证业务</li>
            <li><strong>关联机构</strong>：外部讲师关联的机构名称</li>
            <li><strong>擅长领域*</strong>：必填，讲师擅长的专业领域</li>
            <li><strong>合作状态*</strong>：必填，合作中/待签约</li>
            <li><strong>联系电话</strong>：讲师联系方式</li>
            <li><strong>邮箱地址</strong>：讲师邮箱</li>
            <li><strong>个人简介</strong>：讲师详细介绍</li>
          </ul>
        </div>
      </div>

      <!-- 步骤2：上传文件 -->
      <div v-if="currentStep === 2" class="step-panel">
        <h3>上传Excel文件</h3>
        <p class="step-desc">请选择填写好的Excel文件进行上传</p>

        <el-upload
          ref="uploadRef"
          class="upload-area"
          drag
          :auto-upload="false"
          :show-file-list="true"
          :limit="1"
          accept=".xlsx,.xls,.csv"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text"> 点击选择文件或拖拽文件到此处 </div>
          <template #tip>
            <div class="el-upload__tip"> 只能上传 xlsx/xls/csv 文件，且不超过 10MB </div>
          </template>
        </el-upload>
      </div>

      <!-- 步骤3：数据预览 -->
      <div v-if="currentStep === 3" class="step-panel">
        <h3>数据预览</h3>
        <p class="step-desc">请确认以下数据是否正确,如有错误请修改Excel文件后重新上传</p>

        <!-- 数据预览表格 -->
        <div class="preview-table">
          <div v-if="previewData.length === 0" class="no-data"> 暂无数据 </div>
          <el-table v-else :data="previewData" border style="width: 100%" max-height="300">
            <el-table-column prop="name" label="讲师姓名" width="100" />
            <el-table-column prop="type" label="讲师类型" width="100" />
            <el-table-column prop="biz" label="业务模块" width="100" />
            <el-table-column prop="org" label="关联机构" width="120" />
            <el-table-column prop="field" label="擅长领域" width="150" />
            <el-table-column prop="status" label="合作状态" width="100" />
            <el-table-column prop="phone" label="联系电话" width="120" />
            <el-table-column prop="email" label="邮箱地址" width="150" />
            <el-table-column prop="description" label="个人简介" width="200" />
            <el-table-column prop="errorMessage" label="错误信息" width="200">
              <template #default="{ row }">
                <span v-if="row.errorMessage" class="error-message">
                  {{ row.errorMessage }}
                </span>
                <span v-else class="no-error">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="validStatus" label="状态" width="80">
              <template #default="{ row }">
                <span :class="row.isValid ? 'valid' : 'invalid'">
                  {{ row.isValid ? '√ 有效' : '× 错误' }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 导入统计 -->
        <div class="import-stats">
          <h4>导入统计:</h4>
          <div class="stats-content">
            <div class="stat-item">
              <span>总记录数: {{ totalRecords }}</span>
            </div>
            <div class="stat-item">
              <span class="success">有效记录: {{ validRecords }}</span>
            </div>
            <div class="stat-item">
              <span class="error">错误记录: {{ errorRecords }}</span>
            </div>
          </div>
        </div>

        <!-- 错误详情 -->
        <div v-if="errorDetails.length > 0" class="error-details">
          <h4>错误详情:</h4>
          <div class="error-content">
            <div v-for="(error, index) in errorDetails" :key="index" class="error-item">
              {{ error }}
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤4：确认导入 -->
      <div v-if="currentStep === 4" class="step-panel">
        <h3>确认导入</h3>
        <p class="step-desc">确认无误后点击完成按钮进行最终导入</p>

        <!-- 导入结果 -->
        <div v-if="importResult" class="import-result">
          <h4>导入结果</h4>
          <div class="result-content">
            <div class="result-item">
              <el-icon class="success-icon"><circle-check /></el-icon>
              <span>成功导入: {{ importResult.successCount }}条</span>
            </div>
            <div class="result-item">
              <el-icon class="error-icon"><warning-filled /></el-icon>
              <span>导入失败: {{ importResult.failCount }}条</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <!-- 第一步时显示下载按钮 -->
          <el-button
            v-if="currentStep === 1"
            type="primary"
            size="large"
            @click="downloadTemplate"
            class="download-btn-footer"
          >
            <el-icon><Download /></el-icon>
            下载Excel模板
          </el-button>
        </div>
        <div class="footer-right">
          <el-button v-if="currentStep > 1" @click="prevStep">上一步</el-button>
          <el-button
            v-if="currentStep < 3"
            type="primary"
            @click="nextStep"
            :disabled="currentStep === 2 && !uploadFile"
          >
            下一步
          </el-button>
          <el-button
            v-if="currentStep === 3"
            type="primary"
            @click="nextStep"
            :loading="importLoading"
          >
            完成
          </el-button>
          <el-button @click="handleClose">取消</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElUpload } from 'element-plus'
import { UploadFilled, CircleCheck, WarningFilled, Download } from '@element-plus/icons-vue'
import { validateTeacherImport, executeTeacherImport } from '@/api/infra/teacher'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close', isSuccess: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const currentStep = ref(1)
const uploadFile = ref<File | null>(null)
const previewData = ref<any[]>([])
const importLoading = ref(false)
const importResult = ref<any>(null)
const validTeacherList = ref<any[]>([]) // 存储校验通过的数据

// 统计数据
const totalRecords = ref(0)
const validRecords = ref(0)
const errorRecords = ref(0)
const errorDetails = ref<string[]>([])

// 监听visible变化，重置状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetState()
    }
  }
)

// 重置状态
const resetState = () => {
  currentStep.value = 1
  uploadFile.value = null
  previewData.value = []
  importResult.value = null
  validTeacherList.value = []
  totalRecords.value = 0
  validRecords.value = 0
  errorRecords.value = 0
  errorDetails.value = []
}

// 下一步
const nextStep = async () => {
  console.log('当前步骤:', currentStep.value)

  if (currentStep.value === 1) {
    // 直接进入下一步，不自动下载模板
    currentStep.value = 2
  } else if (currentStep.value === 2) {
    console.log('第二步：准备校验文件')
    if (!uploadFile.value) {
      ElMessage.warning('请先选择要上传的文件')
      return
    }
    console.log('文件已选择，开始校验:', uploadFile.value.name)
    // 校验数据
    await validateFileData()
    console.log('校验完成，进入第三步')
    currentStep.value = 3
  } else if (currentStep.value === 3) {
    console.log('第三步：准备执行导入')
    // 执行导入
    await confirmImport()
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 下载模板
const downloadTemplate = () => {
  const link = document.createElement('a')
  link.href = '/templates/师资库导入模板.csv'
  link.download = '师资库导入模板.csv'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  ElMessage.success('模板下载成功')
}

// 文件选择处理
const handleFileChange = (file: any) => {
  uploadFile.value = file.raw
}

// 文件移除处理
const handleFileRemove = () => {
  uploadFile.value = null
}

// 校验文件数据
const validateFileData = async () => {
  console.log('validateFileData函数被调用')
  if (!uploadFile.value) {
    console.log('没有上传文件')
    return
  }

  try {
    console.log('开始调用校验接口，文件:', uploadFile.value.name)
    const res = await validateTeacherImport(uploadFile.value)
    console.log('校验接口返回结果:', res)

    if (res) {
      // 处理校验结果 - 注意：request已经提取了data字段
      const validateList = res.validateList || []
      console.log('校验列表数据:', validateList)

      previewData.value = validateList.map((item: any) => ({
        name: item.name,
        type: item.type,
        biz: item.biz,
        org: item.org,
        field: item.field,
        status: item.status,
        phone: item.phone,
        email: item.email,
        description: item.description,
        isValid: item.validateStatus === 'VALID',
        errorMessage: item.errorMessage,
        originalData: item.originalData
      }))

      console.log('处理后的预览数据:', previewData.value)

      // 统计数据
      totalRecords.value = res.totalCount || 0
      validRecords.value = res.validCount || 0
      errorRecords.value = res.errorCount || 0

      // 提取校验通过的数据
      validTeacherList.value = validateList
        .filter((item: any) => item.validateStatus === 'VALID')
        .map((item: any) => item.originalData)

      // 提取错误详情
      errorDetails.value = validateList
        .filter((item: any) => item.validateStatus === 'ERROR')
        .map((item: any) => `第${item.rowNum}行: ${item.errorMessage}`)
    }
  } catch (error) {
    console.error('校验文件失败:', error)
    ElMessage.error('校验文件失败')
  }
}

// 确认导入
const confirmImport = async () => {
  if (validTeacherList.value.length === 0) {
    ElMessage.warning('没有校验通过的数据可以导入')
    return
  }

  importLoading.value = true
  try {
    const res = await executeTeacherImport(validTeacherList.value)

    if (res) {
      importResult.value = {
        successCount: res.successCount || 0,
        failCount: res.failCount || 0
      }

      if (res.successCount > 0) {
        ElMessage.success(
          `导入完成!成功导入${res.successCount}条记录${res.failCount > 0 ? `,失败${res.failCount}条记录` : ''}`
        )
        emit('close', true)
      } else {
        ElMessage.error('导入失败，请检查数据')
      }
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    importLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  emit('close', false)
}
</script>

<style scoped>
.import-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  padding: 20px 0;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #dcdfe6;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.step-item.active .step-circle {
  background-color: #409eff;
}

.step-item.completed .step-circle {
  background-color: #67c23a;
}

.step-text {
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
}

.step-item.active .step-text {
  color: #409eff;
  font-weight: bold;
}

.step-item.completed .step-text {
  color: #67c23a;
}

.step-line {
  width: 80px;
  height: 2px;
  background-color: #dcdfe6;
  margin: 0 10px;
  transition: all 0.3s;
}

.step-line.completed {
  background-color: #67c23a;
}

.step-content {
  min-height: 300px;
}

.step-panel {
  padding: 20px 0;
}

/* 第一步特殊样式，让内容更紧凑 */
.step-panel:first-child {
  padding: 15px 0;
}

/* 第二步特殊样式，让内容更紧凑 */
.step-panel:nth-child(2) {
  padding: 15px 0;
}

/* 第三步特殊样式，让内容更紧凑 */
.step-panel:nth-child(3) {
  padding: 15px 0;
}

.step-panel h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 18px;
}

/* 第一步的标题更紧凑 */
.step-panel:first-child h3 {
  margin: 0 0 12px 0;
}

/* 第二步的标题更紧凑 */
.step-panel:nth-child(2) h3 {
  margin: 0 0 12px 0;
}

/* 第三步的标题更紧凑 */
.step-panel:nth-child(3) h3 {
  margin: 0 0 12px 0;
}

.step-desc {
  color: #606266;
  margin-bottom: 20px;
  line-height: 1.6;
}

/* 第一步的描述文字更紧凑 */
.step-panel:first-child .step-desc {
  margin-bottom: 15px;
}

/* 第二步的描述文字更紧凑 */
.step-panel:nth-child(2) .step-desc {
  margin-bottom: 15px;
}

/* 第三步的描述文字更紧凑 */
.step-panel:nth-child(3) .step-desc {
  margin-bottom: 15px;
}

.template-fields,
.data-tips {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 6px;
}

/* 第一步的模板字段说明更紧凑 */
.step-panel:first-child .template-fields {
  padding: 15px;
}

.template-fields h4,
.data-tips h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

/* 第一步的字段说明标题更紧凑 */
.step-panel:first-child .template-fields h4 {
  margin: 0 0 12px 0;
}

.template-fields ul,
.data-tips ul {
  margin: 0;
  padding-left: 20px;
}

.template-fields li,
.data-tips li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.5;
}

/* 第一步的列表项更紧凑 */
.step-panel:first-child .template-fields li {
  margin-bottom: 6px;
  line-height: 1.4;
}

.upload-area {
  width: 100%;
}

.upload-area :deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  transition: border-color 0.3s;
}

.upload-area :deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}

.upload-area :deep(.el-icon--upload) {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-area :deep(.el-upload__text) {
  color: #606266;
  font-size: 14px;
  margin-bottom: 16px;
}

.preview-table {
  margin-bottom: 20px;
}

.preview-table :deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

.preview-table :deep(.el-table__header) {
  background-color: #f5f7fa;
}

.preview-table :deep(.el-table__header th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
}

.no-data {
  text-align: center;
  padding: 40px 0;
  color: #909399;
  font-size: 14px;
}

.valid {
  color: #67c23a;
  font-weight: bold;
}

.invalid {
  color: #f56c6c;
  font-weight: bold;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1.4;
}

.no-error {
  color: #909399;
  font-size: 12px;
}

.import-stats {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
}

.import-stats h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.stats-content {
  display: flex;
  gap: 20px;
}

.stat-item {
  color: #606266;
}

.stat-item .success {
  color: #67c23a;
  font-weight: bold;
}

.stat-item .error {
  color: #f56c6c;
  font-weight: bold;
}

.error-details {
  margin-bottom: 20px;
}

.error-details h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.error-content {
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  padding: 15px;
}

.error-item {
  color: #f56c6c;
  margin-bottom: 5px;
  line-height: 1.4;
}

.error-item:last-child {
  margin-bottom: 0;
}

.import-result {
  margin-top: 20px;
}

.import-result h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.result-content {
  display: flex;
  gap: 20px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
}

.success-icon {
  color: #67c23a;
  font-size: 16px;
}

.error-icon {
  color: #e6a23c;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left {
  display: flex;
  align-items: center;
}

.footer-right {
  display: flex;
  align-items: center;
}

.footer-right .el-button {
  margin-left: 10px;
}

.download-section {
  margin-top: 30px;
  text-align: center;
}

.download-btn {
  padding: 12px 24px;
  font-size: 16px;
}

.download-btn-footer {
  padding: 12px 24px;
  font-size: 16px;
}
</style>
