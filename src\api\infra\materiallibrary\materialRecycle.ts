import request from '@/config/axios'
import qs from 'qs'

// 回收站素材VO
export interface RecycleMaterialVO {
  id: string | number
  name: string
  type: string // 素材类型
  source: string
  deleteTime: string
  remainDays: number
  previewUrl?: string
  visibleOrgName?: string
}

// 获取回收站素材列表
export const getRecycleList = (params: {
  pageNo: number
  pageSize: number
  name?: string
  source?: string
  type?: string
}) => {
  return request.get({ url: '/system/material/recycle/list', params })
}

// 恢复素材
export const restoreMaterial = (id: string | number) => {
  return request.post({
    url: '/system/material/recycle/restore',
    data: qs.stringify({ id }),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 永久删除素材
export const deleteMaterialPermanently = (id: string | number) => {
  return request.post({
    url: '/system/material/recycle/delete',
    data: qs.stringify({ id }),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}
