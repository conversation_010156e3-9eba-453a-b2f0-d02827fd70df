import request from '@/config/axios'

export interface TalentUser {
  id: number
  name: string
  phone: string
  identityId: string
  source: string
  tags: string[]
  status: string
  completeness: number
}

export interface TalentPoolPageParams {
  keyword?: string
  source?: string
  tag?: string
  status?: string
  pageNo?: number
  pageSize?: number,
  orgId?: number | string
}

export interface TalentPoolPageResult {
  total: number
  list: TalentUser[]
}

export const TalentPoolApi = {
  // 分页查询人才
  getTalentPage: async (params: TalentPoolPageParams): Promise<TalentPoolPageResult> => {
    return await request.get({ url: '/apachecore/talentpool/page', params })
  },
  // 查询详情
  getTalentDetail: async (id: number) => {
    return await request.get({ url: '/apachecore/talentpool/get?id=' + id })
  },
  // 编辑
  updateTalent: async (data: TalentUser) => {
    return await request.put({ url: '/apachecore/talentpool/update', data })
  },
  // 停用
  disableTalent: async (id: number) => {
    return await request.post({ url: '/apachecore/talentpool/disable?id=' + id })
  },
  // 发起合并
  mergeTalent: async (id: number) => {
    return await request.post({ url: '/apachecore/talentpool/merge?id=' + id })
  },
  // 新增人才档案
  createTalent: async (data: any): Promise<any> => {
    return await request.post({ url: '/apachecore/talentpool/create', data })
  },
  // 获取人才画像详情（参数 id）
  getTalentProfileDetail: async (params: { id: string | number }) => {
    return await request.get({ url: '/apachecore/talentpool/detail', params })
  },
  // 变更人才状态（启用/停用/禁用等）
  changeTalentStatus: async (id: number, status: string) => {
    return await request.post({ url: '/apachecore/talentpool/change-status', params: { id, status } })
  }
}
