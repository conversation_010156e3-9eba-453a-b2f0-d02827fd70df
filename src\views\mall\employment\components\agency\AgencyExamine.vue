<!--
  页面名称：机构注册审核
  功能描述：展示机构注册信息，支持审核操作
-->
<template>
  <div class="agency-examine">
    <!-- 机构信息卡片 -->
    <el-card class="agency-info-card">
      <template #header>
        <div class="card-header">
          <h3>机构信息</h3>
        </div>
      </template>

      <div class="agency-header">
        <div class="agency-icon">
          <i class="fas fa-building"></i>
        </div>
        <div class="agency-basic-info">
          <h2 class="agency-name">{{ agencyInfo.name }}</h2>
          <div class="agency-meta">
            <span class="agency-id">ID: {{ agencyInfo.id }}</span>
            <span class="applicant">申请人: {{ agencyInfo.applicant }}</span>
            <span class="phone">{{ formatPhone(agencyInfo.phone) }}</span>
          </div>
          <div class="submit-time"> 提交时间: {{ formatDateTime(agencyInfo.submitTime) }} </div>
        </div>
      </div>
    </el-card>

    <!-- 机构详细信息 -->
    <el-card class="agency-detail-card">
      <template #header>
        <div class="card-header">
          <h3>机构详细信息</h3>
        </div>
      </template>

      <div class="detail-grid">
        <div class="detail-item">
          <div class="detail-label">机构全称</div>
          <div class="detail-value">{{ agencyDetail.fullName }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">机构简称</div>
          <div class="detail-value">{{ agencyDetail.shortName }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">法人代表</div>
          <div class="detail-value">{{ agencyDetail.legalRepresentative }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">成立日期</div>
          <div class="detail-value">{{ formatDate(agencyDetail.establishmentDate) }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">统一社会信用代码</div>
          <div class="detail-value">{{ agencyDetail.creditCode }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">联系电话</div>
          <div class="detail-value">{{ formatPhone(agencyDetail.contactPhone) }}</div>
        </div>
        <div class="detail-item full-width">
          <div class="detail-label">注册地址</div>
          <div class="detail-value">{{ agencyDetail.registeredAddress }}</div>
        </div>
        <div class="detail-item full-width">
          <div class="detail-label">经营地址</div>
          <div class="detail-value">{{ agencyDetail.operatingAddress }}</div>
        </div>
        <div class="detail-item full-width">
          <div class="detail-label">经营范围</div>
          <div class="detail-value">{{ agencyDetail.businessScope }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">申请人</div>
          <div class="detail-value">{{ agencyDetail.applicant }}</div>
        </div>
      </div>
    </el-card>

    <!-- 证照资质 -->
    <el-card class="license-card">
      <template #header>
        <div class="card-header">
          <h3>证照资质</h3>
        </div>
      </template>

      <div class="license-grid">
        <div class="license-item" v-for="lic in licenses" :key="lic.fileName + lic.fileType">
          <div class="license-label">{{ fileTypeText(lic.fileType) }}</div>
          <div class="license-content">
            <div class="license-status">
              <div class="status-icon" :class="lic.fileUrl ? 'uploaded' : 'not-uploaded'">
                <i :class="lic.fileUrl ? 'fas fa-check' : 'fas fa-times'"></i>
              </div>
              <span class="status-text">{{ lic.fileUrl ? '已上传' : '未上传' }}</span>
            </div>
            <el-link
              v-if="lic.fileUrl"
              type="primary"
              @click="viewLicense({ name: lic.fileName, fileUrl: lic.fileUrl })"
              >查看</el-link
            >
          </div>
        </div>
      </div>
    </el-card>

    <!-- 门头照片 -->
    <el-card class="facade-photo-card">
      <template #header>
        <div class="card-header">
          <h3>门头照片</h3>
        </div>
      </template>

      <div class="photo-container">
        <div class="photo-item">
          <div class="photo-placeholder">
            <template v-if="doorPhotoUrl">
              <el-image
                :src="doorPhotoUrl"
                :preview-src-list="doorPhotoList"
                fit="cover"
                style="width: 100%; height: 100%; border-radius: 8px;"
              />
            </template>
            <template v-else>
              <i class="fas fa-image"></i>
            </template>
          </div>
          <div class="photo-label">门头照片1</div>
          <el-link type="primary" @click="viewLargePhoto('facade1')"> 查看大图 </el-link>
        </div>
      </div>
    </el-card>

    <!-- 审核操作 -->
    <div class="audit-operation-section">
      <div class="section-title">
        <span class="title-text">审核操作</span>
        <span class="required-mark">*</span>
      </div>

      <el-select
        v-model="auditForm.operation"
        placeholder="请选择审核操作"
        class="audit-select"
        :popper-class="'audit-select-dropdown'"
      >
        <el-option label="请选择审核操作" value="" disabled />
        <el-option label="通过" value="approve" />
        <el-option label="拒绝" value="reject" />
      </el-select>
    </div>

    <!-- 审核备注（仅在拒绝时显示） -->
    <div class="audit-operation-section" v-if="auditForm.operation === 'reject'">
      <div class="section-title">
        <span class="title-text">审核备注</span>
        <span class="required-mark">*</span>
      </div>

      <el-input
        v-model="auditForm.comment"
        type="textarea"
        :rows="3"
        maxlength="200"
        show-word-limit
        placeholder="请输入拒绝原因或备注（必填）"
      />
    </div>

    <!-- 审核日志 -->
    <el-card class="audit-log-card">
      <template #header>
        <div class="card-header">
          <h3>审核日志</h3>
        </div>
      </template>

      <div class="log-container">
        <div v-if="auditLogs.length > 0">
          <div class="log-entry" v-for="(log, index) in auditLogs" :key="index">
            <div class="log-content">
              <span class="log-action">{{ log.subType || '审核操作' }}</span>
              <span class="log-user">by {{ log.userName || log.operator || log.creatorName || log.creator || '系统' }}</span>
              <span class="log-time">{{ formatDateTime(log.createTime || log.create_time || log.createTime) }}</span>
            </div>
            <div v-if="log.action" class="log-remark">{{ log.action }}</div>
          </div>
        </div>
        <div v-else class="log-empty">
          <div class="log-content">
            <span class="log-action">提交机构注册申请</span>
            <span class="log-user">by 李女士</span>
            <span class="log-time">2022-11-20 09:15</span>
          </div>
          <div class="log-remark">机构信息提交完成，等待审核</div>
        </div>
      </div>
    </el-card>

    <!-- 底部操作按钮 -->
    <div class="action-buttons">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="submitAudit">确定</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import {
  getAgencyDetail,
  updateAgencyReview,
  type AgencyDetailVO
} from '@/api/mall/employment/agency'
import { getOperateLogPage } from '@/api/system/operatelog'

/** 路由参数 */
const route = useRoute()

/** 传入的机构（可选） */
const props = defineProps<{ agency?: any }>()

/** 机构基本信息 */
const agencyInfo = ref({
  name: '-',
  id: '',
  applicant: '-',
  phone: '',
  submitTime: ''
})

/** 机构详细信息 */
const agencyDetail = ref({
  fullName: '-',
  shortName: '-',
  legalRepresentative: '-',
  establishmentDate: '',
  creditCode: '-',
  contactPhone: '',
  registeredAddress: '-',
  operatingAddress: '-',
  businessScope: '-',
  applicant: '-'
})

const licenses = ref<AgencyDetailVO['qualifications']>([])
const doorPhotoList = ref<string[]>([])
const doorPhotoUrl = ref<string>('')

/** 证照类型文案映射 */
const fileTypeText = (type: string) => {
  switch (type) {
    case 'business_license':
      return '营业执照'
    case 'qualification_cert':
      return '资质证书'
    case 'contract':
      return '合同文件'
    case 'human_resources':
      return '人力资源服务许可证'
    case 'opening_permit':
      return '开户许可证'
    case 'door_photo':
      return '门头照'
    case 'organizational_structure':
      return '组织机构代码证书'
    case 'id_card':
      return '法人身份证'
    default:
      return '其他附件'
  }
}

/** 审核表单 */
const auditForm = reactive({
  operation: '',
  comment: ''
})

/** 审核日志 */
const auditLogs = ref<any[]>([])

/** 格式化手机号 */
const formatPhone = (phone: string) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

/** 格式化日期时间 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString()
}

/** 提交审核 */
const submitAudit = async () => {
  if (!auditForm.operation) {
    ElMessage.warning('请选择审核操作')
    return
  }

  if (auditForm.operation === 'reject' && !auditForm.comment?.trim()) {
    ElMessage.warning('请输入审核备注')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要${auditForm.operation === 'approve' ? '通过' : '拒绝'}该机构的注册申请吗？`,
      '确认审核',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await updateAgencyReview({
      id: Number(agencyInfo.value.id),
      reviewStatus: auditForm.operation === 'approve' ? 'approved' : 'rejected',
      reviewRemark: auditForm.comment || undefined
    })
    ElMessage.success('审核提交成功')
    resetForm()
    // 通知父级刷新
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核提交失败:', error)
      ElMessage.error('审核提交失败')
    }
  }
}

/** 重置表单 */
const resetForm = () => {
  auditForm.operation = ''
  auditForm.comment = ''
}

/** 查看证照文件 */
const viewLicense = (license: any) => {
  if (license.fileUrl) {
    window.open(license.fileUrl, '_blank')
  } else {
    ElMessage.warning('文件链接不存在')
  }
}

/** 查看大图 */
const viewLargePhoto = (photoId: string) => {
  if (!doorPhotoList.value || doorPhotoList.value.length === 0) {
    ElMessage.warning('暂无门头照片')
    return
  }
  // el-image 自带点击放大预览，这里提示用户可直接点击图片预览
  ElMessage.info('点击图片可预览大图')
}

/** 获取机构详情 */
const fetchAgencyDetail = async (id: string | number) => {
  try {
    const res: any = await getAgencyDetail(Number(id))
    const data: AgencyDetailVO = res?.data || res
    agencyInfo.value = {
      id: String(data.id || ''),
      name: data.agencyName || '-',
      applicant: data.applicantName || '-',
      phone: data.applicantPhone || data.contactPhone || '',
      submitTime: data.applicationTime || ''
    }
    agencyDetail.value = {
      fullName: data.agencyName || '-',
      shortName: data.agencyShortName || '-',
      legalRepresentative: data.legalRepresentative || '-',
      establishmentDate: data.establishmentDate || '',
      creditCode: data.unifiedSocialCreditCode || '-',
      contactPhone: data.contactPhone || '',
      registeredAddress: data.registeredAddress || '-',
      operatingAddress: data.operatingAddress || '-',
      businessScope: data.businessScope || '-',
      applicant: data.applicantName || '-'
    }
    licenses.value = data.qualifications || []
    doorPhotoList.value = (licenses.value || [])
      .filter((x: any) => x.fileType === 'door_photo' || x.file_type === 'door_photo')
      .map((x: any) => x.fileUrl || x.file_url)
      .filter(Boolean)
    // 绑定门头照片（door_photo）
    doorPhotoUrl.value =
      (licenses.value.find((x: any) => x.fileType === 'door_photo')?.fileUrl as string) || ''
    
    // 获取审核日志
    await fetchAuditLogs(Number(id))
  } catch (error) {
    console.error('获取机构详情失败:', error)
    ElMessage.error('获取机构详情失败')
  }
}

/** 获取审核日志 */
const fetchAuditLogs = async (agencyId: number) => {
  try {
    const res = await getOperateLogPage({
      pageNo: 1,
      pageSize: 50,
      bizId: agencyId,
      type: 'AGENCY',
      bizType: 'AGENCY'
    })
    
    console.log('审核日志API返回数据:', res)
    
    if (res && res.data && Array.isArray(res.data.list)) {
      // 对日志进行排序，最新的在前面
      auditLogs.value = res.data.list.sort((a: any, b: any) => {
        const timeA = new Date(a.createTime || a.create_time || 0).getTime()
        const timeB = new Date(b.createTime || b.create_time || 0).getTime()
        return timeB - timeA
      })
      console.log('处理后的审核日志数据:', auditLogs.value)
    } else if (res && Array.isArray(res.list)) {
      // 兼容不同的返回结构
      auditLogs.value = res.list.sort((a: any, b: any) => {
        const timeA = new Date(a.createTime || a.create_time || 0).getTime()
        const timeB = new Date(b.createTime || b.create_time || 0).getTime()
        return timeB - timeA
      })
      console.log('兼容处理后的审核日志数据:', auditLogs.value)
    } else {
      auditLogs.value = []
    }
  } catch (error) {
    console.error('获取审核日志失败:', error)
    // 如果接口调用失败，使用默认数据
    auditLogs.value = [
      {
        action: '提交机构注册申请',
        userName: '李女士',
        createTime: new Date().toISOString(),
        remark: '机构信息提交完成，等待审核'
      }
    ]
  }
}

const emit = defineEmits(['close', 'success'])

onMounted(() => {
  // 优先使用父组件传入的 agency.id，否则看路由参数
  const id = props.agency?.id || (route.params.id as string)
  console.log('初始化ID:', id)
  if (id) {
    fetchAgencyDetail(id)
  } else {
    console.log('没有ID，使用默认数据')
  }
})

// 监听父组件传入的机构 id 变化，确保每次打开都能拉取详情
watch(
  () => props.agency?.id,
  (newId) => {
    if (newId) fetchAgencyDetail(newId)
  }
)
</script>

<style scoped lang="scss">
.agency-examine {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
  .agency-info-card {
    margin-bottom: 20px;
    border: 1px solid #e9ecef;

    :deep(.el-card__header) {
      background: transparent;
    }

    :deep(.el-card__body) {
      background: #f8f9fa;
    }

    .card-header {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .agency-header {
      display: flex;
      align-items: flex-start;
      gap: 20px;

      .agency-icon {
        width: 60px;
        height: 60px;
        background: #f5f5f5;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 24px;
          color: #666;
        }
      }

      .agency-basic-info {
        flex: 1;

        .agency-name {
          margin: 0 0 10px 0;
          font-size: 20px;
          font-weight: 600;
          color: #333;
        }

        .agency-meta {
          margin-bottom: 8px;
          color: #666;
          font-size: 14px;

          span {
            margin-right: 20px;

            &:last-child {
              margin-right: 0;
            }
          }
        }

        .submit-time {
          color: #999;
          font-size: 12px;
        }
      }
    }
  }

  .agency-detail-card {
    margin-bottom: 20px;

    :deep(.el-card__header) {
      background: transparent;
    }

    :deep(.el-card__body) {
      background: #f8f9fa;
    }

    .card-header {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .detail-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;

      .detail-item {
        display: flex;
        flex-direction: column;
        gap: 8px;

        &.full-width {
          grid-column: 1 / -1;
        }

        .detail-label {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }

        .detail-value {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          word-break: break-all;
        }
      }
    }
  }

  .audit-operation-section {
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .title-text {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .required-mark {
        color: #f56c6c;
        margin-left: 4px;
        font-size: 14px;
      }
    }

    .audit-select {
      width: 100%;

      :deep(.el-input__wrapper) {
        border: 1px solid #409eff;
        box-shadow: 0 0 0 1px #409eff;
      }

      :deep(.el-input__inner) {
        color: #333;
      }
    }
  }

  .license-card {
    margin-bottom: 20px;

    :deep(.el-card__header) {
      background: transparent;
    }

    :deep(.el-card__body) {
      background: #f8f9fa;
    }

    .card-header {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .license-grid {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .license-item {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 16px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: #fafafa;
        width: 100%;

        .license-label {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }

        .license-content {
          display: flex;
          align-items: center;
          gap: 12px;

          .license-status {
            display: flex;
            align-items: center;
            gap: 8px;

            .status-icon {
              width: 16px;
              height: 16px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              &.uploaded {
                background: #67c23a;
                color: white;
              }

              &.not-uploaded {
                background: #c0c4cc;
                color: white;
              }

              i {
                font-size: 10px;
              }
            }

            .status-text {
              font-size: 12px;
              color: #666;
            }
          }

          .el-link {
            font-size: 12px;
          }
        }
      }
    }
  }

  .facade-photo-card {
    margin-bottom: 20px;

    :deep(.el-card__header) {
      background: transparent;
    }

    :deep(.el-card__body) {
      background: #f8f9fa;
    }

    .card-header {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .photo-container {
      .photo-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .photo-placeholder {
          width: 100%;
          height: 150px;
          background: #f5f5f5;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 48px;
            color: #c0c4cc;
          }
        }

        .photo-label {
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }

        .el-link {
          font-size: 12px;
        }
      }
    }
  }

  .audit-log-card {
    margin-bottom: 20px;

    :deep(.el-card__header) {
      background: transparent;
    }

    :deep(.el-card__body) {
      background: #f8f9fa;
    }

    .card-header {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .log-container {
      .log-entry {
        display: flex;
        flex-direction: column; /* Changed to column */
        padding: 12px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e9ecef;

        .log-content {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;

          .log-action {
            color: #409eff;
            font-weight: 500;
          }

          .log-user {
            color: #666;
            margin-right: auto;
          }

          .log-time {
            color: #999;
            font-size: 12px;
          }
        }

        .log-remark {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
        }
      }

      .log-empty {
        display: flex;
        flex-direction: column; /* Changed to column */
        padding: 12px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e9ecef;

        .log-content {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;

          .log-action {
            color: #409eff;
            font-weight: 500;
          }

          .log-user {
            color: #666;
            margin-right: auto;
          }

          .log-time {
            color: #999;
            font-size: 12px;
          }
        }

        .log-remark {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .el-button {
      min-width: 80px;
    }
  }
}
</style>
