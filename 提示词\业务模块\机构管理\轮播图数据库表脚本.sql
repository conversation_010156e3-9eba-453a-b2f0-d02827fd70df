轮播图表：publicbiz_carousel

CREATE TABLE `publicbiz_carousel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `platform` varchar(20) DEFAULT 'employer' COMMENT '平台：employer-雇主端，aunt-阿姨端',
  `carousel_title` varchar(200) NOT NULL COMMENT '轮播图标题',
  `carousel_image_url` varchar(500) NOT NULL COMMENT '轮播图片URL',
  `carousel_link_url` varchar(500) DEFAULT NULL COMMENT '跳转链接URL',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';

