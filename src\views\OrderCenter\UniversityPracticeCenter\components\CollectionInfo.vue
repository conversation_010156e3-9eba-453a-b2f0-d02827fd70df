<!--
  组件名称：收款信息展示
  功能描述：展示高校实践订单的收款信息
-->
<template>
  <div class="collection-info">
    <div class="info-header">
      <h4>收款信息</h4>
      <el-button v-if="canEdit" type="primary" size="small" @click="handleEdit">
        编辑收款信息
      </el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <div v-else-if="collectionInfo" class="info-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="收款状态">
          <el-tag :type="getPaymentStatusType(collectionInfo.paymentStatus)">
            {{ getPaymentStatusText(collectionInfo.paymentStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="收款金额">
          <span class="amount">¥{{ formatAmount(collectionInfo.collectionAmount) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="收款方式">
          {{ collectionInfo.collectionMethodText }}
        </el-descriptions-item>
        <el-descriptions-item label="收款日期">
          {{ formatDate(collectionInfo.collectionDate) }}
        </el-descriptions-item>
        <el-descriptions-item label="操作人">
          {{ collectionInfo.operatorName }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatDate(collectionInfo.updateTime) }}
        </el-descriptions-item>
        <el-descriptions-item v-if="collectionInfo.collectionRemark" label="收款备注" :span="2">
          {{ collectionInfo.collectionRemark }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-else class="no-data">
      <el-empty description="暂无收款信息" />
    </div>

    <!-- 收款信息编辑对话框 -->
    <CollectionDialog
      :visible="editDialogVisible"
      @update:visible="(val) => (editDialogVisible = val)"
      :order-data="orderData"
      :is-edit="true"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  UniversityPracticeOrderApi,
  type UniversityPracticeOrder,
  type CollectionInfoResult
} from '@/api/OrderCenter/UniversityPracticeCenter'
import CollectionDialog from './CollectionDialog.vue'

interface Props {
  orderData: UniversityPracticeOrder
}

interface Emits {
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 收款信息
const collectionInfo = ref<CollectionInfoResult | null>(null)

// 加载状态
const loading = ref(false)

// 编辑对话框显示状态
const editDialogVisible = ref(false)

// 是否可以编辑收款信息
const canEdit = computed(() => {
  return props.orderData.paymentStatus === 'paid'
})

// 获取支付状态类型
const getPaymentStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    pending: 'warning',
    paid: 'success',
    refunded: 'info',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    refunded: '已退款',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

// 格式化金额
const formatAmount = (amount: number) => {
  return amount ? amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '0.00'
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 加载收款信息
const loadCollectionInfo = async () => {
  if (!props.orderData.id) return

  try {
    loading.value = true
    const result = await UniversityPracticeOrderApi.getCollectionInfo({ id: props.orderData.id })
    collectionInfo.value = result
  } catch (error) {
    console.error('加载收款信息失败:', error)
    ElMessage.error('加载收款信息失败')
  } finally {
    loading.value = false
  }
}

// 处理编辑
const handleEdit = () => {
  editDialogVisible.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  emit('refresh')
  loadCollectionInfo()
}

// 组件挂载时加载数据
onMounted(() => {
  loadCollectionInfo()
})
</script>

<style scoped>
.collection-info {
  margin: 20px 0;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.info-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.info-content {
  background: #fafafa;
  border-radius: 4px;
  padding: 16px;
}

.amount {
  font-weight: 600;
  color: #67c23a;
  font-size: 16px;
}

.loading-container {
  padding: 20px;
}

.no-data {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}
</style>
