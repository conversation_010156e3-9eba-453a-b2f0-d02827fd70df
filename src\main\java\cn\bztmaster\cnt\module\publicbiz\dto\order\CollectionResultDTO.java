package cn.bztmaster.cnt.module.publicbiz.dto.order;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 收款结果DTO
 * 用于返回收款操作的结果信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CollectionResultDTO {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 支付状态
     */
    private String paymentStatus;

    /**
     * 收款金额
     */
    private BigDecimal collectionAmount;

    /**
     * 收款方式
     */
    private String collectionMethod;

    /**
     * 收款方式中文名称
     */
    private String collectionMethodText;

    /**
     * 收款日期
     */
    private LocalDate collectionDate;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 收款备注
     */
    private String collectionRemark;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作结果消息
     */
    private String message;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误详情
     */
    private String errorDetail;
}

