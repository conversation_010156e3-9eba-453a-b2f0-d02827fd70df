<!--
  页面名称：订单审批弹窗
  功能描述：支持审批通过和审批拒绝操作，调用相应的API接口
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="订单审批"
    width="700px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="approval-container">
      <!-- 审批表单 -->
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="审批结果*" prop="approvalResult">
          <el-radio-group v-model="form.approvalResult" @change="onApprovalResultChange">
            <el-radio value="approve">
              <el-icon color="#67c23a" style="margin-right: 4px"><Check /></el-icon>
              审批通过
            </el-radio>
            <el-radio value="reject">
              <el-icon color="#f56c6c" style="margin-right: 4px"><Close /></el-icon>
              审批驳回
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="驳回原因*" prop="rejectReason" v-if="form.approvalResult === 'reject'">
          <el-input
            v-model="form.rejectReason"
            type="textarea"
            :rows="4"
            placeholder="请输入驳回原因..."
          />
        </el-form-item>

        <el-form-item label="审批意见" prop="comments">
          <el-input
            v-model="form.comments"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见 (可选)..."
          />
        </el-form-item>

        <!-- 订单信息展示 -->
        <el-form-item label="订单信息">
          <div class="order-info">
            <div class="info-item">
              <span class="info-label">订单号：</span>
              <span class="info-value">{{ getOrderNumber() }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">项目名称：</span>
              <span class="info-value">{{ getProjectName() }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">订单金额：</span>
              <span class="info-value amount">¥{{ formatAmount(getOrderAmount()) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">当前状态：</span>
              <span class="info-value">
                <el-tag :type="getOrderStatusType(orderData?.orderStatus || '')">
                  {{ getOrderStatusText(orderData?.orderStatus || '') }}
                </el-tag>
              </span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :disabled="!form.approvalResult"
        >
          {{ form.approvalResult === 'approve' ? '审批通过' : '审批驳回' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import type { UniversityPracticeOrder } from '@/api/OrderCenter/UniversityPracticeCenter'
import { UniversityPracticeOrderApi } from '@/api/OrderCenter/UniversityPracticeCenter'
import type { EnterpriseTrainingOrder } from '@/api/OrderCenter/enterprise-training'
import {
  approveEnterpriseTrainingOrder,
  rejectEnterpriseTrainingOrder
} from '@/api/OrderCenter/enterprise-training'

// Props
interface Props {
  visible: boolean
  orderData?: UniversityPracticeOrder | EnterpriseTrainingOrder
  orderType?: 'university_practice' | 'enterprise_training' | 'individual_training' | 'housekeeping'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: undefined,
  orderType: 'university_practice'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)

// 表单数据
const form = ref({
  approvalResult: 'approve', // 审批结果：approve(通过) 或 reject(驳回)
  rejectReason: '', // 驳回原因
  comments: '' // 审批意见
})

// 表单校验规则
const rules = {
  approvalResult: [{ required: true, message: '请选择审批结果', trigger: 'change' }],
  rejectReason: [
    {
      required: true,
      message: '请输入驳回原因',
      trigger: 'blur',
      validator: (rule: any, value: string, callback: any) => {
        if (form.value.approvalResult === 'reject' && !value.trim()) {
          callback(new Error('请输入驳回原因'))
        } else {
          callback()
        }
      }
    }
  ],
  comments: [{ required: false, message: '请输入审批意见', trigger: 'blur' }]
}

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount.toLocaleString('zh-CN')
}

/** 获取订单状态类型 */
const getOrderStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'warning',
    approved: 'success',
    rejected: 'danger',
    pending_payment: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    approved: '已批准',
    rejected: '已拒绝',
    pending_payment: '待支付',
    in_progress: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 获取订单号 - 兼容不同订单类型 */
const getOrderNumber = () => {
  if (!props.orderData) return ''
  if (props.orderType === 'enterprise_training') {
    return (props.orderData as EnterpriseTrainingOrder).orderNumber || ''
  }
  return (props.orderData as UniversityPracticeOrder).orderNo || ''
}

/** 获取项目名称 - 兼容不同订单类型 */
const getProjectName = () => {
  if (!props.orderData) return ''
  if (props.orderType === 'enterprise_training') {
    return (props.orderData as EnterpriseTrainingOrder).trainingProject || ''
  }
  return (props.orderData as UniversityPracticeOrder).projectName || ''
}

/** 获取订单金额 - 兼容不同订单类型 */
const getOrderAmount = () => {
  if (!props.orderData) return 0
  if (props.orderType === 'enterprise_training') {
    return (props.orderData as EnterpriseTrainingOrder).orderAmount || 0
  }
  return (props.orderData as UniversityPracticeOrder).totalAmount || 0
}

// 审批结果变化处理
const onApprovalResultChange = (value: string) => {
  if (value === 'approve') {
    // 选择通过时，清空驳回原因
    form.value.rejectReason = ''
  }
  console.log('审批结果变化:', value)
}

/** 提交表单 */
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    const orderId = props.orderData?.id
    const orderNo = getOrderNumber()

    if (!orderId || !orderNo) {
      ElMessage.error('订单信息不完整')
      return
    }

    loading.value = true

    let result
    if (form.value.approvalResult === 'approve') {
      // 根据订单类型调用不同的审批通过接口
      if (props.orderType === 'enterprise_training') {
        result = await approveEnterpriseTrainingOrder({
          orderId,
          orderNo,
          approvalType: 'order_approval',
          comments: form.value.comments
        })
      } else {
        // 高校实践订单调用原接口
        result = await UniversityPracticeOrderApi.approveOrder({
          orderId,
          orderNo,
          approvalType: 'order_approval',
          comments: form.value.comments
        })
      }
    } else {
      // 根据订单类型调用不同的审批驳回接口
      if (props.orderType === 'enterprise_training') {
        result = await rejectEnterpriseTrainingOrder({
          orderId,
          orderNo,
          approvalType: 'order_approval',
          rejectReason: form.value.rejectReason,
          comments: form.value.comments
        })
      } else {
        // 高校实践订单调用原接口
        result = await UniversityPracticeOrderApi.rejectOrder({
          orderId,
          orderNo,
          approvalType: 'order_approval',
          rejectReason: form.value.rejectReason,
          comments: form.value.comments
        })
      }
    }

    console.log('审批接口返回结果:', result)
    console.log('result类型:', typeof result)
    console.log('result.code值:', result?.code)
    console.log('result.code类型:', typeof result?.code)
    console.log('result.code === 0:', result?.code === 0)
    console.log('result.data:', result?.data)
    console.log('result.msg:', result?.msg)

    // 检查接口返回结果 - 支持多种返回格式
    // 1. 直接返回true: true
    // 2. 标准格式: {code: 0, data: true, msg: ""}
    // 3. 企业培训订单格式: {code: 0, data: {success: true, ...}, msg: ""}
    // 4. 企业培训订单直接返回格式: {success: true, approvalId: "...", message: "..."}
    let isSuccess = false

    if (result === true) {
      isSuccess = true
    } else if (result && result.code === 0) {
      // 对于企业培训订单，需要进一步检查 data.success
      if (result.data && typeof result.data === 'object') {
        isSuccess = result.data.success === true
      } else {
        // 对于标准格式，code === 0 就表示成功
        isSuccess = true
      }
    } else if (result && result.success === true) {
      // 企业培训订单直接返回格式
      isSuccess = true
    }

    console.log('isSuccess:', isSuccess)
    console.log('成功判断详情:')
    console.log('- result === true:', result === true)
    console.log('- result.code === 0:', result && result.code === 0)
    console.log('- result.data:', result?.data)
    console.log('- result.data.success === true:', result?.data?.success === true)
    console.log('- result.success === true:', result && result.success === true)

    if (isSuccess) {
      // 优先使用接口返回的消息，如果没有则使用默认消息
      const successMessage =
        result?.data?.message ||
        result?.message ||
        result?.msg ||
        (form.value.approvalResult === 'approve' ? '审批通过成功' : '审批驳回成功')
      console.log('审批操作成功:', successMessage)

      ElMessage.success(successMessage)
      emit('success', {
        approvalResult: form.value.approvalResult,
        rejectReason: form.value.rejectReason,
        comments: form.value.comments,
        message: successMessage,
        approvalId: result?.data?.approvalId || result?.approvalId // 传递审批ID
      })
      handleCancel()
    } else {
      const errorMessage = result?.msg || '操作失败'
      console.error('审批操作失败:', errorMessage)
      console.error('失败原因分析:')
      console.error('- result存在:', !!result)
      console.error('- result值:', result)
      console.error('- result类型:', typeof result)
      console.error('- result.code值:', result?.code)
      console.error('- result.code === 0:', result?.code === 0)
      ElMessage.error(errorMessage)
    }
  } catch (error) {
    console.error('审批操作失败:', error)
    ElMessage.error('审批操作失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 取消 */
const handleCancel = () => {
  emit('update:visible', false)
  // 重置表单
  form.value = {
    approvalResult: 'approve',
    rejectReason: '',
    comments: ''
  }
}
</script>

<style scoped lang="scss">
.approval-container {
  .order-info {
    background: #f8f9fa;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 16px;

    .info-item {
      display: flex;
      margin-bottom: 12px;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-weight: 500;
        color: #606266;
        min-width: 80px;
        flex-shrink: 0;
      }

      .info-value {
        color: #303133;
        flex: 1;

        &.amount {
          color: #67c23a;
          font-weight: bold;
          font-size: 16px;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
