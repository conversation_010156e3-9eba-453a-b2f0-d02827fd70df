<!--
  页面名称：家政服务订单操作日志
  功能描述：展示家政服务订单的操作日志，支持查看操作历史
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="handleUpdateVisible"
    title="家政服务订单 - 操作日志"
    direction="rtl"
    size="500px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <div class="log-list">
        <div v-for="(log, index) in logList" :key="index" class="log-item">
          <div class="log-header">
            <div class="log-actor">
              <span class="actor-name">{{ log.actorName }}</span>
              <span class="actor-role">({{ log.actorRole }})</span>
            </div>
            <div class="log-time">{{ log.operationTime }}</div>
            <div class="log-action">
              <el-tag :type="log.actionType === 'create' ? 'success' : 'warning'" size="small">
                {{ log.actionType === 'create' ? '创建' : '编辑' }}
              </el-tag>
            </div>
          </div>
          <div class="log-content">
            <div v-for="(detail, detailIndex) in log.details" :key="detailIndex" class="log-detail">
              <template v-if="detail.type === 'status_change'">
                <span class="detail-label">{{ detail.label }}:</span>
                <span class="detail-old">{{ detail.oldValue }}</span>
                <span class="detail-arrow">→</span>
                <span class="detail-new">{{ detail.newValue }}</span>
              </template>
              <template v-else-if="detail.type === 'personnel_change'">
                <span class="detail-label">{{ detail.label }}:</span>
                <span class="detail-old">{{ detail.oldValue }}</span>
                <span class="detail-arrow">→</span>
                <span class="detail-new">{{ detail.newValue }}</span>
              </template>
              <template v-else>
                <span class="detail-text">{{ detail.text }}</span>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// Props
interface Props {
  visible: boolean
  orderId?: string
}

const props = withDefaults(defineProps<Props>(), {
  orderId: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const logList = ref([
  {
    actorName: '李小明',
    actorRole: '客服',
    operationTime: '2024/5/28 15:30:00',
    actionType: 'create',
    details: [
      {
        type: 'text',
        text: '客户通过电话预约月嫂服务'
      }
    ]
  },
  {
    actorName: '王小红',
    actorRole: '运营',
    operationTime: '2024/5/28 16:00:00',
    actionType: 'edit',
    details: [
      {
        type: 'status_change',
        label: '订单状态',
        oldValue: '待派单',
        newValue: '服务中'
      },
      {
        type: 'text',
        text: '服务人员已派单,开始服务'
      }
    ]
  },
  {
    actorName: '张经理',
    actorRole: '主管',
    operationTime: '2024/5/29 09:15:00',
    actionType: 'edit',
    details: [
      {
        type: 'personnel_change',
        label: '服务人员',
        oldValue: '张阿姨',
        newValue: '王阿姨'
      },
      {
        type: 'text',
        text: '张阿姨因家中有急事请假,安排王阿姨顶岗'
      }
    ]
  }
])

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.orderId) {
      // 加载操作日志数据
      fetchLogList()
    }
  }
)

// 方法定义
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}

const fetchLogList = async () => {
  try {
    // 这里调用实际的API接口获取操作日志
    // const res = await getOrderOptLog(props.orderId)
    // logList.value = res.data

    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500))
  } catch (error) {
    console.error('获取操作日志失败:', error)
  }
}

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.drawer-content {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;

  .log-list {
    .log-item {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;

      .log-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e4e7ed;

        .log-actor {
          .actor-name {
            font-weight: bold;
            color: #333;
          }

          .actor-role {
            color: #666;
            margin-left: 4px;
          }
        }

        .log-time {
          color: #999;
          font-size: 12px;
        }

        .log-action {
          .el-tag {
            font-size: 12px;
          }
        }
      }

      .log-content {
        background-color: #fff3cd;
        border-left: 3px solid #ffa500;
        padding: 12px;
        border-radius: 4px;

        .log-detail {
          margin-bottom: 8px;
          line-height: 1.5;

          &:last-child {
            margin-bottom: 0;
          }

          .detail-label {
            font-weight: bold;
            color: #333;
            margin-right: 8px;
          }

          .detail-old {
            color: #dc3545;
            font-weight: bold;
          }

          .detail-arrow {
            margin: 0 8px;
            color: #666;
          }

          .detail-new {
            color: #28a745;
            font-weight: bold;
          }

          .detail-text {
            color: #333;
          }
        }
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fff;
}
</style>
