/_高校实践订单_/

@前端页面自动生成提示词模板.md 现在生成高校实践中心的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。高校实践中心首页的路径src\views\orders\OrderCenter\index.vue 请按照附件截图完成首页界面的设计

现在请参照附件截图完成新增高校实践中心界面的设计。高校实践中心页面路径：src\views\orders\OrderCenter\components\AddUniversityPracticeCenter.vue。同样点击*************** 页面的新建和数据列操作栏的编辑按钮时，

会从右侧滑出 AddUniversityPracticeCenter.vue 抽屉表单，实现新增高校实践中心的完整流程。按钮和整体布局美观，交互流畅现在根据附件截图完成分配界面的设计，分配界面的文件路径：src\views\orders\OrderCenter\components\AddUniversityPracticeCenter.vue

@AddUniversityPracticeCenter.vue 每个模块添加一个浅色的背景颜色 模块与模块之前区分，总订单数、待处理订单、本月订单金额、订单完成率

@index.vue 点击操作栏 编辑按钮时也需要从右侧滑出抽屉AddPartner.vue；点击操作栏删除按钮时，暂时不请求后端接口，直接删除当前行提示删除成功

@OptLog.vue 这个是操作日志界面，请先参考附件截图完成界面设计，然后点击操作栏的“操作日志”按钮时，从右侧滑出 AddUniversityPracticeCenter.vue 抽屉

/_企业培训订单_/ 现在请参照附件截图完成新增企业培训订单界面的设计。企业培训订单页面路径：src\views\OrderCenter\EnterpriseTraining\components\AddEnterpriseTraining.vue。同样点击*************** 页面的新建和数据列操作栏的编辑按钮时，

@前端页面自动生成提示词模板.md 现在生成企业培训订单的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。企业培训订单首页的路径src\views\OrderCenter\EnterpriseTraining\index.vue 请按照附件截图完成首页界面的设计

现在请参照附件截图完成新增企业培训订单界面的设计。企业培训订单页面路径：src\views\OrderCenter\EnterpriseTraining\components\AddEnterpriseTraining.vue。同样点击*************** 页面的新建和数据列操作栏的编辑按钮时，

会从右侧滑出 AddEnterpriseTraining.vue 抽屉表单，实现新增企业培训订单的完整流程。按钮和整体布局美观，交互流畅现在根据附件截图完成分配界面的设计，分配界面的文件路径：src\views\OrderCenter\EnterpriseTraining\components\AddEnterpriseTraining.vue

@AddEnterpriseTraining.vue 每个模块添加一个浅色的背景颜色 模块与模块之前区分，总订单数、待处理订单、本月订单金额、订单完成率

@index.vue 点击操作栏 编辑按钮时也需要从右侧滑出抽屉AddEnterpriseTraining.vue；点击操作栏删除按钮时，暂时不请求后端接口，直接删除当前行提示删除成功

@OptLog.vue 这个是操作日志界面，请先参考附件截图完成界面设计，然后点击操作栏的“操作日志”按钮时，从右侧滑出 AddEnterpriseTraining.vue 抽屉

/_家政服务订单_/

@前端页面自动生成提示词模板.md 现在生成家政服务订单的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。家政服务订单首页的路径src\views\OrderCenter\HousekeepingServiceOrder\index.vue 请按照附件截图完成首页界面的设计

现在请参照附件截图完成新增家政服务订单界面的设计。家政服务订单页面路径：src\views\OrderCenter\HousekeepingServiceOrder\components\AddHousekeepingServiceOrder.vue。同样点击*************** 页面的新建和数据列操作栏的编辑按钮时，

@前端页面自动生成提示词模板.md 现在生成家政服务订单的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。家政服务订单首页的路径src\views\OrderCenter\HousekeepingServiceOrder\index.vue 请按照附件截图完成首页界面的设计

现在请参照附件截图完成新增家政服务订单界面的设计。家政服务订单页面路径：src\views\OrderCenter\HousekeepingServiceOrder\components\AddHousekeepingServiceOrder.vue。同样点击*************** 页面的新建和数据列操作栏的编辑按钮时，

会从右侧滑出 AddHousekeepingServiceOrder.vue 抽屉表单，实现新增家政服务订单的完整流程。按钮和整体布局美观，交互流畅现在根据附件截图完成分配界面的设计，分配界面的文件路径：src\views\OrderCenter\HousekeepingServiceOrder\components\AddHousekeepingServiceOrder.vue

@AddHousekeepingServiceOrder.vue 每个模块添加一个浅色的背景颜色 模块与模块之前区分，总订单数、待处理订单、本月订单金额、订单完成率

@index.vue 点击操作栏 编辑按钮时也需要从右侧滑出抽屉 AddHousekeepingServiceOrder.vue；点击操作栏删除按钮时，暂时不请求后端接口，直接删除当前行提示删除成功

@OptLog.vue 这个是操作日志界面，请先参考附件截图完成界面设计，然后点击操作栏的“操作日志”按钮时，从右侧滑出 AddHousekeepingServiceOrder.vue 抽屉

/_个人培训与认证订单_/ @前端页面自动生成提示词模板.md 现在生成个人培训与认证订单的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。个人培训与认证订单首页的路径src\views\OrderCenter\IndividualtrainingOrder\index.vue 请按照附件截图完成首页界面的设计

现在请参照附件截图完成新增个人培训与认证订单界面的设计。个人培训与认证订单页面路径：src\views\OrderCenter\IndividualtrainingOrder\components\AddIndividualtrainingOrder.vue。同样点击*************** 页面的新建和数据列操作栏的编辑按钮时，

@前端页面自动生成提示词模板.md 现在生成个人培训与认证订单的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。个人培训与认证订单首页的路径src\views\OrderCenter\IndividualtrainingOrder\index.vue 请按照附件截图完成首页界面的设计

现在请参照附件截图完成新增个人培训与认证订单界面的设计。个人培训与认证订单页面路径：src\views\OrderCenter\IndividualtrainingOrder\components\AddIndividualtrainingOrder.vue。同样点击*************** 页面的新建和数据列操作栏的编辑按钮时，

会从右侧滑出 AddIndividualtrainingOrder.vue 抽屉表单，实现新增个人培训与认证订单的完整流程。按钮和整体布局美观，交互流畅现在根据附件截图完成分配界面的设计，分配界面的文件路径：src\views\OrderCenter\IndividualtrainingOrder\components\AddIndividualtrainingOrder.vue

@AddIndividualtrainingOrder.vue 每个模块添加一个浅色的背景颜色 模块与模块之前区分，总订单数、待处理订单、本月订单金额、订单完成率

@index.vue 点击操作栏 编辑按钮时也需要从右侧滑出抽屉 AddIndividualtrainingOrder.vue；点击操作栏删除按钮时，暂时不请求后端接口，直接删除当前行提示删除成功

@OptLog.vue 这个是操作日志界面，请先参考附件截图完成界面设计，然后点击操作栏的“操作日志”按钮时，从右侧滑出 AddIndividualtrainingOrder.vue 抽屉

现在请参照附件截图完成新增个人培训与认证订单界面的设计。个人培训与认证订单页面路径：src\views\OrderCenter\IndividualtrainingOrder\components\IndividualtrainingOrderView.vue。同样点击*************** 页面的查看按钮时，

/_家政服务订单_/ @前端页面自动生成提示词模板.md 现在生成家政服务订单的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。家政服务订单首页的路径src\views\OrderCenter\HousekeepingServiceOrder\index.vue

现在请参照附件截图完成新增家政服务订单查看界面的设计。家政服务订单页面路径：src\views\OrderCenter\HousekeepingServiceOrder\components\IndividualtrainingOrderView.vue。@index.vue 点击操作栏 查看按钮时也需要从右侧滑出抽屉 IndividualtrainingOrderView.vue；点击操作栏删除按钮时，暂时不请求后端接口，直接删除当前行提示删除成功查看详情页面，点击不同的标签展示内容不同；根据截图实现功能，其中收支记录里面的【新增收支记录和编辑是共用一个页面】;

/_家政服务订单_/ @前端页面自动生成提示词模板.md 现在生成家政服务订单的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。家政服务订单首页的路径src\views\OrderCenter\HousekeepingServiceOrder\index.vue

现在请参照附件截图完成家政服务订单查看界面的设计。家政服务订单页面路径：src\views\OrderCenter\HousekeepingServiceOrder\components\IndividualtrainingOrderView.vue。查看详情页面，将【基本信息】中的服务人员字段放在预约时间下面；服务地址字数过多，自动换行；【服务任务列表】点击完成凭证，弹出如截图内容，将功能实现;【服务任务列表】点击编辑，弹出如截图内容，将列表的备注字段更新;【服务任务列表】勾选多个后，点击批量重指派弹出如截图内容，将功能实现;【服务任务列表】将操作根据状态做按钮的状态切换;【服务任务列表】点击取消，弹出确定要取消这个任务吗？点击确认后备注更新为【任务已取消】，操作按钮只剩下编辑，其他的隐藏;【服务任务列表】将指派弹出与重指派同一个页面，确认后，任务状态修改为待执行;

/_高校实践订单_/ @前端页面自动生成提示词模板.md 现在生成高校实践订单的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。高校实践订单首页的路径src\views\OrderCenter\UniversityPracticeCenter\index.vue

现在请参照附件截图完成高校实践订单查看界面的设计。家政服务订单页面路径：src\views\OrderCenter\UniversityPracticeCenter\components\IndividualtrainingOrderView.vue;【新增纸质合同】请按照附件截图完成点击【纸质合同按钮】后弹窗内容设计;【新增电子合同】请按照附件截图完成【电子合同】后弹窗内容设计;【新增审批流程】列表中的订单状态为【待审批】，查看详情页面将显示【发起审批】按钮，点击按钮弹出发起审批页面；发起审批后，在审批流程里面新增一条记录; 将对应的合同上传后，详情里面的【纸质合同内容】【电子合同内容】，也需要同步更新内容;

/_高校实践订单_/ @前端页面自动生成提示词模板.md 现在生成高校实践订单的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。高校实践订单首页的路径src\views\OrderCenter\UniversityPracticeCenter\index.vue

现在请参照附件截图完成高校实践订单查看界面的设计。家政服务订单页面路径：src\views\OrderCenter\UniversityPracticeCenter\components\IndividualtrainingOrderView.vue;【新增纸质合同】请按照附件截图完成点击【纸质合同按钮】后弹窗内容设计;【新增电子合同】请按照附件截图完成【电子合同】后弹窗内容设计;【新增审批流程】列表中的订单状态为【待审批】，查看详情页面将显示【发起审批】按钮，点击按钮弹出发起审批页面；发起审批后，在审批流程里面新增一条记录; 将对应的合同上传后，详情里面的【纸质合同内容】【电子合同内容】，也需要同步更新内容;
