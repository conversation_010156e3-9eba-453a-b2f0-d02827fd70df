----生成数据库表 @/employment @/employment请根据就业服务-就业管理-旗下阿姨页面相关元素并结合以上截图内容生成相关数据库表脚本语句并将将本存放在D:\WorkSpace\JavaCode\CNT-HRSP\chuanneng-hr-service-vue\提示词\业务模块\就业服务的 @数据库表创建脚本.sql 下。

生成后端接口提示词文档 @/employment @/employment @数据库表创建脚本.sql请结合截图中的内容并参照@接口文档生成提示词.md 针对旗下阿姨板块页面相关功能帮我整理一份接口文档生成的提示词，便于我用于生成后端接口相关代码。页面中涉及了列表查询、导出列表、新增阿姨、编辑阿姨信息、查看阿姨信息、生成简历海报以及解约等相关功能。要结合@数据库表创建脚本.sql 中 阿姨管理相关记录表进行。

注意：此处需要重新生成一份旗下阿姨相关的接口文档生成提示词，不要和其他内容混在一起，帮我重新生成一份提示词文件。

生成后端接口代码相关提示词（需要用cursor工具打开后端代码操作）请结合相关@旗下阿姨接口文档生成提示词.md文档 以及@数据库表创建脚本.sql 中旗下阿姨相关表生成后端相关业务代码，要完善所有业务逻辑，对于新增、修改、删除等变更数据的操作都需要接入操作日志相关记录。

注意：此处删除不要做物理删除，要做逻辑删除。

@/bztmaster-module-system-server我check代码发现，对应的旗下阿姨模块中对应阿姨列表的导出以及生成阿姨建立海报的功能的业务逻辑没有实现。1、导出阿姨列表数据请按照 阿姨姓名、综合评级、服务类型、累计单数、当前状态、平台状态这几列进行导出。2、生成简历海报就请按照阿姨详情的相关信息导出生成一份简历形式的PDF文件。

相关字典：

服务类型:service_type
旗下阿姨-平台状态：platform_status
综合评级:composite_rating 1.0-1.99 1星2.0-2.99 2星3.0-3.99 3星4.0-4.99 4星5 5星

---

- //处理实际接口绑定

@/employment @/employment 1、请帮我将旗下阿姨模块列表、新增以及编辑页中的下拉框更换成通过实际接口调用，对应的调用方式以及接口文档请参照@基础数据字典接口文档.md对应数据字典类型：

2、请帮我将列表中的查询、新增、编辑、解约以及查看详情等操作的功能更换成实际的接口对接，具体对接方式请参照@旗下阿姨管理详细接口文档.md

## 前端页面调整提示词

```
@/employment @/employment
1、就业服务-服务套餐 已上架和待上架、回收站的列表中状态字段的后面要新增两列字段 审核状态和所属机构字段；//
2、已上架列表中操作栏控制只有下架和查看日志的操作。
3、待上架列表中，当审核状态为审核中时操作栏可以进行审核、撤回、日志查看、删除等操作；
4、待上架列表中，当审核状态为已拒绝时操作栏可以进行编辑、上架、日志查看、删除等操作；
5、回收站列表的操作栏下要去掉编辑按钮，只能启用和删除；
6、请帮我生成对应的字段补充的脚本语句，我需要新增 审核状态、所属机构ID以及所属机构名称字段；
注意：请严格按照上面的几点进行内容进行调整，不要随意乱调整其他地方的代码。
```

##后端接口调整提示词

```
@/bztmaster-module-publicbiz-server @/bztmaster-module-publicbiz-api
1、服务套餐模块新增了审核状态、所属机构ID、所属机构名称三个字段，此处相关接口都需要添加三个字段对应的出入参的逻辑；
2、查询套餐时，只能根据当前登录人员的所属机构查询对应机构的服务套餐数据；
3、上架接口的逻辑需要调整，在进行上架时对应的审核状态要变更成审核中，需要审核通过之后对应的状态字段变为上架，审核状态为审核通过；若审批拒绝则对应的状态字段仍为待上架，审核状态为已拒绝；

注意：请严格按照以上3点要求进行调整，不要随意改动其他地方的代码。
```

```
@/employment @/employment
1、已上架列表中所属机构的名称没有正常展示，需要参照待上架和回收站列表中的所属机构进行展示。

注意：此处只需要调整已上架列表下所属机构的展示，请不要随意调整其他模块的代码。
```

```
@/employment @/employment
审核状态：pending-待审核， auditing-审核中，approved-已通过，rejected-已拒绝
服务套餐模块列表的页面审核状态字段展示的中文信息请根上面的内容进行调整；

注意：此处只需要调整服务套餐模块列表审核状态字段展示的中文信息，不要随意改动其它代码。
```

```
@/employment @/employment
1、当状态为待上架，审核状态为待审核状态时操作栏可以进行编辑、上架、日志、删除等操作；
```

```
@/employment @/employment
1、请按照截图中的内容完善套餐审核的页面，需要展示相关套餐详情信息以及可以进行审核操作，操作时可选择通过和拒绝，但是拒绝时拒绝原因必填。
2、操作完成后，点击“确定”按钮后需要调用审核接口 "/publicbiz/employment/service-package/{id}/audit"
@服务套餐管理详细接口文档.md 请参考对应接口文档。
3、需要新增拒绝原因字段，请帮我生成补充字段的脚本用于到时存储拒绝原因。
```

```
@/employment @/employment
1、请按照截图中的内容完善套餐审核的页面，需要展示相关套餐详情信息以及可以进行审核操作，操作时可选择通过和拒绝，但是拒绝时拒绝原因必填。
2、操作完成后，点击“确定”按钮后需要调用审核接口 "/publicbiz/employment/service-package/{id}/audit"
@服务套餐管理详细接口文档.md 请参考对应接口文档。
3、需要新增拒绝原因字段，请帮我生成补充字段的脚本用于到时存储拒绝原因。
4、审核时，如果审核选择拒绝，需要传递拒绝原因的。
5、请注意套餐审核页面的页面展示形式，请以抽屉样式从右边展现。此处务必注意和编辑、新增页保持类似展现形式。
注意：此处请严格按照上面内容调整，不要随意改动其他不相关的代码。

```

```
@/employment @/employment
1、套餐审核详情的商品主图展示的地方请与下方服务分类那一栏内容保持对齐，不要太靠边上。
2、待上架列表中，需要将上架操作时的接口进行更换：/publicbiz/employment/service-package/{id}/shelf @服务套餐管理详细接口文档.md ,请不要直接将接口放在页面中调用，要进行封装后再调用。
3、套餐审核详情页服务内容对应详情的服务内容，服务流程内容对应的是套餐详情的服务描述，用户须知对应的是套餐详情的购买须知。

请根据以上几点进行调整。注意不要随意改动其他内容的代码以免功能调坏了。
```

```
@/employment @/employment
1、已上架和待上架列表中的日志操作，直接展示现有的套餐审核的页面，如果是查看日志时则需要隐藏页面的按钮以及相关审批操作的内容，如果是正常的审批操作则不需要隐藏。
2、套餐审核详细页面审核日志加载需要将对应的模块更换成实际接口对接：system/operate-log/page?pageNo=1&pageSize=100&type=SERVICE_PACKAGE 服务套餐
加载内容按照截图中相关内容加载。

请确认并调整以上代码信息。注意不要随意改动其他模块代码。
```
