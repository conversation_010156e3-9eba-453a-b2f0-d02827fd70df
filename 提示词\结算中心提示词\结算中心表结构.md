-- ==================== 结算中心相关表 ====================

-- 结算记录表 CREATE TABLE `publicbiz_settlement` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 结算基本信息 `settlement_no` VARCHAR(50) NOT NULL COMMENT '结算单号', `settlement_type` VARCHAR(30) NOT NULL COMMENT '结算类型：order-订单结算/practitioner-阿姨结算/agency-机构结算/platform-平台结算', `settlement_period` VARCHAR(20) NOT NULL COMMENT '结算周期：daily-日结/weekly-周结/monthly-月结', `settlement_date` DATE NOT NULL COMMENT '结算日期',

-- 关联信息 `order_id` BIGINT COMMENT '关联订单ID', `order_no` VARCHAR(50) COMMENT '关联订单号', `practitioner_oneid` VARCHAR(36) COMMENT '关联阿姨OneID', `agency_id` BIGINT COMMENT '关联机构ID',

-- 金额信息 `total_amount` DECIMAL(12,2) NOT NULL COMMENT '结算总金额', `settlement_amount` DECIMAL(12,2) NOT NULL COMMENT '实际结算金额', `commission_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '佣金金额', `tax_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '税费金额', `other_deduction` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '其他扣减',

-- 结算状态 `settlement_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败', `settlement_time` DATETIME COMMENT '结算时间', `settlement_method` VARCHAR(30) COMMENT '结算方式：bank_transfer-银行转账/alipay-支付宝/wechat-微信/other-其他',

-- 操作信息 `operator_id` BIGINT COMMENT '操作人ID', `operator_name` VARCHAR(50) COMMENT '操作人姓名', `remark` TEXT COMMENT '备注',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_settlement_no` (`settlement_no`), KEY `idx_tenant_id` (`tenant_id`), KEY `idx_settlement_type` (`settlement_type`), KEY `idx_settlement_period` (`settlement_period`), KEY `idx_settlement_date` (`settlement_date`), KEY `idx_order_id` (`order_id`), KEY `idx_practitioner_oneId` (`practitioner_oneId`), KEY `idx_agency_id` (`agency_id`), KEY `idx_settlement_status` (`settlement_status`), KEY `idx_operator_id` (`operator_id`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='结算记录表';

-- 结算明细表 CREATE TABLE `publicbiz_settlement_detail` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联字段 `settlement_id` BIGINT NOT NULL COMMENT '结算记录ID', `settlement_no` VARCHAR(50) NOT NULL COMMENT '结算单号', `order_id` BIGINT COMMENT '订单ID', `order_no` VARCHAR(50) COMMENT '订单号',

-- 明细信息 `item_type` VARCHAR(30) NOT NULL COMMENT '项目类型：service_fee-服务费/platform_fee-平台费/commission-佣金/tax-税费/deduction-扣减', `item_name` VARCHAR(200) NOT NULL COMMENT '项目名称', `item_description` TEXT COMMENT '项目描述', `amount` DECIMAL(12,2) NOT NULL COMMENT '金额', `rate` DECIMAL(5,2) COMMENT '费率百分比', `calculation_basis` VARCHAR(200) COMMENT '计算依据',

-- 阿姨信息 `practitioner_oneid` VARCHAR(36) NOT NULL COMMENT '阿姨OneID', `practitioner_name` VARCHAR(50) NOT NULL COMMENT '阿姨姓名', `practitioner_phone` VARCHAR(20) COMMENT '阿姨电话', `practitioner_bank_account` VARCHAR(50) COMMENT '阿姨银行账户', `practitioner_bank_name` VARCHAR(100) COMMENT '阿姨开户行',

    -- 机构信息

`agency_id` BIGINT NOT NULL COMMENT '机构ID', `agency_name` VARCHAR(200) NOT NULL COMMENT '机构名称', `agency_contact` VARCHAR(50) COMMENT '机构联系人', `agency_phone` VARCHAR(20) COMMENT '机构联系电话', `agency_bank_account` VARCHAR(50) COMMENT '机构银行账户', `agency_bank_name` VARCHAR(100) COMMENT '机构开户行',

-- 结算信息 `settlement_period` VARCHAR(20) NOT NULL COMMENT '结算周期：weekly-周结/monthly-月结', `settlement_date` DATE NOT NULL COMMENT '结算日期', `order_count` INT NOT NULL DEFAULT 0 COMMENT '订单数量', `total_amount` DECIMAL(12,2) NOT NULL COMMENT '总金额', `practitioner_amount` DECIMAL(12,2) NOT NULL COMMENT '阿姨应得金额', `agency_amount` DECIMAL(12,2) NOT NULL COMMENT '机构应得金额', `platform_amount` DECIMAL(12,2) NOT NULL COMMENT '平台分成金额', `commission_rate` DECIMAL(5,2) NOT NULL COMMENT '佣金比例',

-- 结算状态 `settlement_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败', `settlement_time` DATETIME COMMENT '结算时间', `settlement_method` VARCHAR(30) COMMENT '结算方式', `settlement_remark` TEXT COMMENT '结算备注',

-- 索引 PRIMARY KEY (`id`), KEY `idx_settlement_id` (`settlement_id`), KEY `idx_settlement_no` (`settlement_no`), KEY `idx_order_id` (`order_id`), KEY `idx_order_no` (`order_no`), KEY `idx_item_type` (`item_type`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='结算明细表';

-- ==================== 对账单管理相关表 ====================

-- 对账单主表 CREATE TABLE `publicbiz_reconciliation_statement` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 对账单基本信息 `statement_no` VARCHAR(50) NOT NULL COMMENT '对账单号，如：ZD20240816001', `statement_type` VARCHAR(30) NOT NULL COMMENT '对账单类型：agency-机构对账/platform-平台对账/practitioner-阿姨对账', `generation_time` DATETIME NOT NULL COMMENT '生成时间',

-- 关联信息 `agency_id` BIGINT COMMENT '关联机构ID', `agency_name` VARCHAR(200) COMMENT '机构名称', `practitioner_oneid` VARCHAR(36) COMMENT '关联阿姨OneID', `practitioner_name` VARCHAR(50) COMMENT '阿姨姓名',

-- 金额信息 `total_amount` DECIMAL(12,2) NOT NULL COMMENT '对账总金额', `agency_amount` DECIMAL(12,2) NOT NULL COMMENT '机构分成金额', `platform_amount` DECIMAL(12,2) NOT NULL COMMENT '平台分成金额', `agency_ratio` DECIMAL(5,2) NOT NULL COMMENT '机构分成比例(%)', `platform_ratio` DECIMAL(5,2) NOT NULL COMMENT '平台分成比例(%)',

-- 订单信息 `order_count` INT NOT NULL DEFAULT 0 COMMENT '包含订单数量', `order_list` JSON COMMENT '包含订单列表（JSON格式）',

-- 对账状态 `reconciliation_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '对账状态：pending-待对账确认/confirmed-已确认/paid-已支付/cancelled-已取消', `confirmation_time` DATETIME COMMENT '确认时间', `payment_time` DATETIME COMMENT '支付时间', `cancellation_time` DATETIME COMMENT '取消时间', `cancellation_reason` TEXT COMMENT '取消原因',

-- 操作信息 `operator_id` BIGINT COMMENT '操作人ID', `operator_name` VARCHAR(50) COMMENT '操作人姓名', `remark` TEXT COMMENT '备注',

-- 索引 PRIMARY KEY (`id`), UNIQUE KEY `uk_statement_no` (`statement_no`), KEY `idx_tenant_id` (`tenant_id`), KEY `idx_statement_type` (`statement_type`), KEY `idx_generation_time` (`generation_time`), KEY `idx_agency_id` (`agency_id`), KEY `idx_practitioner_oneid` (`practitioner_oneid`), KEY `idx_reconciliation_status` (`reconciliation_status`), KEY `idx_operator_id` (`operator_id`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='对账单主表';

-- 对账单明细表 CREATE TABLE `publicbiz_reconciliation_detail` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联字段 `statement_id` BIGINT NOT NULL COMMENT '对账单ID', `statement_no` VARCHAR(50) NOT NULL COMMENT '对账单号', `order_id` BIGINT NOT NULL COMMENT '订单ID', `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

-- 订单信息 `package_name` VARCHAR(200) NOT NULL COMMENT '套餐名称', `agency_name` VARCHAR(200) COMMENT '家政机构', `practitioner_name` VARCHAR(50) COMMENT '阿姨姓名', `order_time` DATETIME NOT NULL COMMENT '下单时间', `completion_time` DATETIME COMMENT '完成时间', `order_status` VARCHAR(20) NOT NULL COMMENT '订单状态：已完成/服务中', `settlement_status` VARCHAR(20) NOT NULL COMMENT '结算状态：待结算/结算中/已结算/结算失败', `order_amount` DECIMAL(10,2) NOT NULL COMMENT '订单金额',

-- 分成信息 `agency_amount` DECIMAL(10,2) NOT NULL COMMENT '机构分成金额', `platform_amount` DECIMAL(10,2) NOT NULL COMMENT '平台分成金额', `agency_ratio` DECIMAL(5,2) NOT NULL COMMENT '机构分成比例(%)', `platform_ratio` DECIMAL(5,2) NOT NULL COMMENT '平台分成比例(%)',

-- 索引 PRIMARY KEY (`id`), KEY `idx_statement_id` (`statement_id`), KEY `idx_statement_no` (`statement_no`), KEY `idx_order_id` (`order_id`), KEY `idx_order_no` (`order_no`), KEY `idx_agency_name` (`agency_name`), KEY `idx_practitioner_name` (`practitioner_name`), KEY `idx_order_status` (`order_status`), KEY `idx_settlement_status` (`settlement_status`), KEY `idx_order_time` (`order_time`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='对账单明细表';

-- 发票管理表 CREATE TABLE `publicbiz_invoice` ( -- 公共字段 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增', `tenant_id` BIGINT COMMENT '租户ID', `creator` VARCHAR(64) COMMENT '创建人', `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` VARCHAR(64) COMMENT '更新人', `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

-- 关联对账单信息 `statement_id` BIGINT NOT NULL COMMENT '关联对账单ID', `statement_no` VARCHAR(50) NOT NULL COMMENT '对账单号', `agency_id` BIGINT NOT NULL COMMENT '机构ID', `agency_name` VARCHAR(200) NOT NULL COMMENT '机构名称', `reconciliation_amount` DECIMAL(12,2) NOT NULL COMMENT '对账金额',

-- 发票信息 `invoice_no` VARCHAR(50) COMMENT '发票号码', `invoice_date` DATE COMMENT '开票日期', `invoice_type` VARCHAR(30) COMMENT '发票类型：增值税普通发票/增值税专用发票/电子发票', `invoice_amount` DECIMAL(12,2) COMMENT '发票金额', `tax_rate` DECIMAL(5,2) DEFAULT 6.00 COMMENT '税率(%)', `tax_amount` DECIMAL(12,2) COMMENT '税额', `invoice_status` VARCHAR(20) NOT NULL DEFAULT 'not_invoiced' COMMENT '开票状态：not_invoiced-未开票/invoiced-已开票/partially_invoiced-部分开票',

-- 开票信息维护 `invoicing_status` VARCHAR(20) NOT NULL DEFAULT 'not_invoiced' COMMENT '开票状态：not_invoiced-未开票/invoicing-开票中/invoiced-已开票/failed-开票失败', `invoicing_remark` TEXT COMMENT '开票备注',

-- 操作信息 `operator_id` BIGINT COMMENT '操作人ID', `operator_name` VARCHAR(50) COMMENT '操作人姓名', `remark` TEXT COMMENT '备注',

-- 索引 PRIMARY KEY (`id`), KEY `idx_statement_id` (`statement_id`), KEY `idx_statement_no` (`statement_no`), KEY `idx_agency_id` (`agency_id`), KEY `idx_invoice_no` (`invoice_no`), KEY `idx_invoice_date` (`invoice_date`), KEY `idx_invoice_status` (`invoice_status`), KEY `idx_invoicing_status` (`invoicing_status`), KEY `idx_operator_id` (`operator_id`), KEY `idx_create_time` (`create_time`), KEY `idx_deleted` (`deleted`) ) COMMENT='发票管理表';
