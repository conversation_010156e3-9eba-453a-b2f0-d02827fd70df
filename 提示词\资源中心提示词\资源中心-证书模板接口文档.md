# 资源中心-证书模板接口文档

## 1. 接口概述

### 1.1 基础信息

- **基础路径**: `/publicbiz/certificate/template`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: GET/POST

### 1.2 统一响应格式

```json
{
  "code": 0, // 状态码，0表示成功，非0表示失败
  "msg": "success", // 响应消息
  "data": {} // 响应数据
}
```

### 1.3 分页响应格式

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 100, // 总记录数
    "list": [] // 数据列表
  }
}
```

## 2. 数据字典

### 2.1 证书类型 (type)

| 值         | 说明     |
| ---------- | -------- |
| training   | 培训证书 |
| completion | 结业证书 |
| skill      | 技能证书 |

### 2.2 模板状态 (status)

| 值       | 说明   |
| -------- | ------ |
| draft    | 草稿   |
| active   | 启用中 |
| inactive | 已停用 |

### 2.3 字段类型 (fieldType)

| 值     | 说明           |
| ------ | -------------- |
| name   | 学员姓名       |
| code   | 证书编号       |
| id     | 身份证号       |
| date   | 发证日期       |
| qrcode | 证书查验二维码 |

### 2.4 字体族 (fontFamily)

| 值              | 说明            |
| --------------- | --------------- |
| 微软雅黑        | 微软雅黑        |
| 黑体            | 黑体            |
| Arial           | Arial           |
| Times New Roman | Times New Roman |
| 宋体            | 宋体            |

## 3. 接口详情

### 3.1 证书模板分页查询 (page)

#### 接口地址

```
GET /publicbiz/certificate/template/page
```

#### 功能说明

分页查询证书模板列表，支持按类型、状态、关键词筛选

#### 请求参数

| 参数名  | 类型    | 必填 | 说明                                |
| ------- | ------- | ---- | ----------------------------------- |
| page    | Integer | 否   | 页码，默认1                         |
| size    | Integer | 否   | 每页大小，默认10                    |
| type    | String  | 否   | 证书类型：training/completion/skill |
| status  | String  | 否   | 模板状态：draft/active/inactive     |
| keyword | String  | 否   | 搜索关键词，匹配模板名称            |

#### 响应字段

| 字段名               | 类型    | 说明            |
| -------------------- | ------- | --------------- |
| total                | Integer | 总记录数        |
| list                 | Array   | 证书模板列表    |
| list[].id            | Long    | 模板ID          |
| list[].name          | String  | 模板名称        |
| list[].type          | String  | 证书类型        |
| list[].description   | String  | 模板描述        |
| list[].background    | String  | 背景图片文件名  |
| list[].backgroundUrl | String  | 背景图片完整URL |
| list[].course        | String  | 适用课程ID      |
| list[].courseName    | String  | 适用课程名称    |
| list[].status        | String  | 模板状态        |
| list[].htmlContent   | String  | HTML模板内容    |
| list[].previewUrl    | String  | 模板预览图URL   |
| list[].creator       | String  | 创建人          |
| list[].creatorName   | String  | 创建人姓名      |
| list[].createTime    | String  | 创建时间        |
| list[].updater       | String  | 更新人          |
| list[].updateTime    | String  | 更新时间        |

#### 请求示例

```
GET /publicbiz/certificate/template/page?page=1&size=10&type=training&status=active&keyword=新媒体
```

#### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 1,
    "list": [
      {
        "id": 1,
        "name": "新媒体初级培训证书模板",
        "type": "training",
        "description": "新媒体初级培训证书模板描述",
        "background": "certificate-bg-001.jpg",
        "backgroundUrl": "https://example.com/images/backgrounds/certificate-bg-001.jpg",
        "course": "COURSE_001",
        "courseName": "新媒体初级培训",
        "status": "active",
        "htmlContent": "<div class=\"certificate-template\">...</div>",
        "previewUrl": "https://example.com/preview/cert-001.jpg",
        "creator": "admin",
        "creatorName": "管理员",
        "createTime": "2024-08-01 10:00:00",
        "updater": "admin",
        "updateTime": "2024-08-01 10:00:00"
      }
    ]
  }
}
```

### 3.2 证书模板详情查询 (detail)

#### 接口地址

```
GET /publicbiz/certificate/template/detail
```

#### 功能说明

根据模板ID获取证书模板详细信息，包含字段配置

#### 请求参数

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 模板ID |

#### 响应字段

| 字段名              | 类型    | 说明            |
| ------------------- | ------- | --------------- |
| id                  | Long    | 模板ID          |
| name                | String  | 模板名称        |
| type                | String  | 证书类型        |
| description         | String  | 模板描述        |
| background          | String  | 背景图片文件名  |
| backgroundUrl       | String  | 背景图片完整URL |
| course              | String  | 适用课程ID      |
| courseName          | String  | 适用课程名称    |
| status              | String  | 模板状态        |
| htmlContent         | String  | HTML模板内容    |
| previewUrl          | String  | 模板预览图URL   |
| fields              | Array   | 字段配置列表    |
| fields[].id         | Long    | 字段ID          |
| fields[].fieldId    | String  | 字段唯一标识符  |
| fields[].fieldType  | String  | 字段类型        |
| fields[].fieldLabel | String  | 字段标签        |
| fields[].positionX  | Integer | X坐标位置       |
| fields[].positionY  | Integer | Y坐标位置       |
| fields[].fontSize   | Integer | 字体大小        |
| fields[].fontColor  | String  | 字体颜色        |
| fields[].fontFamily | String  | 字体族          |
| fields[].sortOrder  | Integer | 排序顺序        |
| creator             | String  | 创建人          |
| creatorName         | String  | 创建人姓名      |
| createTime          | String  | 创建时间        |
| updater             | String  | 更新人          |
| updateTime          | String  | 更新时间        |

#### 请求示例

```
GET /publicbiz/certificate/template/detail?id=1
```

#### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "新媒体初级培训证书模板",
    "type": "training",
    "description": "新媒体初级培训证书模板描述",
    "background": "certificate-bg-001.jpg",
    "backgroundUrl": "https://example.com/images/backgrounds/certificate-bg-001.jpg",
    "course": "COURSE_001",
    "courseName": "新媒体初级培训",
    "status": "active",
    "htmlContent": "<div class=\"certificate-template\" style=\"width: 900px; height: 600px;\">...</div>",
    "previewUrl": "https://example.com/preview/cert-001.jpg",
    "fields": [
      {
        "id": 1,
        "fieldId": "field_1",
        "fieldType": "name",
        "fieldLabel": "【学员姓名】",
        "positionX": 350,
        "positionY": 200,
        "fontSize": 32,
        "fontColor": "#333",
        "fontFamily": "微软雅黑",
        "sortOrder": 1
      },
      {
        "id": 2,
        "fieldId": "field_2",
        "fieldType": "date",
        "fieldLabel": "【发证日期】",
        "positionX": 350,
        "positionY": 400,
        "fontSize": 16,
        "fontColor": "#666",
        "fontFamily": "微软雅黑",
        "sortOrder": 2
      },
      {
        "id": 3,
        "fieldId": "field_3",
        "fieldType": "qrcode",
        "fieldLabel": "【证书查验二维码】",
        "positionX": 750,
        "positionY": 500,
        "fontSize": 12,
        "fontColor": "#999",
        "fontFamily": "微软雅黑",
        "sortOrder": 3
      }
    ],
    "creator": "admin",
    "creatorName": "管理员",
    "createTime": "2024-08-01 10:00:00",
    "updater": "admin",
    "updateTime": "2024-08-01 10:00:00"
  }
}
```

### 3.3 新增证书模板 (create)

#### 接口地址

```
POST /publicbiz/certificate/template/create
```

#### 功能说明

创建新的证书模板，包含基础信息和字段配置

#### 请求参数

| 参数名              | 类型    | 必填 | 说明                                |
| ------------------- | ------- | ---- | ----------------------------------- |
| name                | String  | 是   | 模板名称                            |
| type                | String  | 是   | 证书类型：training/completion/skill |
| description         | String  | 否   | 模板描述                            |
| background          | String  | 否   | 背景图片文件名                      |
| backgroundUrl       | String  | 否   | 背景图片完整URL                     |
| course              | String  | 否   | 适用课程ID                          |
| courseName          | String  | 否   | 适用课程名称                        |
| status              | String  | 是   | 模板状态：draft/active/inactive     |
| htmlContent         | String  | 否   | HTML模板内容                        |
| fields              | Array   | 否   | 字段配置列表                        |
| fields[].fieldId    | String  | 是   | 字段唯一标识符                      |
| fields[].fieldType  | String  | 是   | 字段类型                            |
| fields[].fieldLabel | String  | 是   | 字段标签                            |
| fields[].positionX  | Integer | 是   | X坐标位置                           |
| fields[].positionY  | Integer | 是   | Y坐标位置                           |
| fields[].fontSize   | Integer | 是   | 字体大小                            |
| fields[].fontColor  | String  | 是   | 字体颜色                            |
| fields[].fontFamily | String  | 是   | 字体族                              |
| fields[].sortOrder  | Integer | 是   | 排序顺序                            |

#### 响应字段

| 字段名 | 类型 | 说明           |
| ------ | ---- | -------------- |
| id     | Long | 新创建的模板ID |

#### 请求示例

```
POST /publicbiz/certificate/template/create
Content-Type: application/json

{
  "name": "新媒体初级培训证书模板",
  "type": "training",
  "description": "新媒体初级培训证书模板描述",
  "background": "certificate-bg-001.jpg",
  "backgroundUrl": "https://example.com/images/backgrounds/certificate-bg-001.jpg",
  "course": "COURSE_001",
  "courseName": "新媒体初级培训",
  "status": "active",
  "fields": [
    {
      "fieldId": "field_1",
      "fieldType": "name",
      "fieldLabel": "【学员姓名】",
      "positionX": 350,
      "positionY": 200,
      "fontSize": 32,
      "fontColor": "#333",
      "fontFamily": "微软雅黑",
      "sortOrder": 1
    },
    {
      "fieldId": "field_2",
      "fieldType": "date",
      "fieldLabel": "【发证日期】",
      "positionX": 350,
      "positionY": 400,
      "fontSize": 16,
      "fontColor": "#666",
      "fontFamily": "微软雅黑",
      "sortOrder": 2
    },
    {
      "fieldId": "field_3",
      "fieldType": "qrcode",
      "fieldLabel": "【证书查验二维码】",
      "positionX": 750,
      "positionY": 500,
      "fontSize": 12,
      "fontColor": "#999",
      "fontFamily": "微软雅黑",
      "sortOrder": 3
    }
  ]
}
```

#### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 2
  }
}
```

### 3.4 更新证书模板 (update)

#### 接口地址

```
POST /publicbiz/certificate/template/update
```

#### 功能说明

更新现有证书模板的信息和字段配置

#### 请求参数

| 参数名              | 类型    | 必填 | 说明                                |
| ------------------- | ------- | ---- | ----------------------------------- |
| id                  | Long    | 是   | 模板ID                              |
| name                | String  | 是   | 模板名称                            |
| type                | String  | 是   | 证书类型：training/completion/skill |
| description         | String  | 否   | 模板描述                            |
| background          | String  | 否   | 背景图片文件名                      |
| backgroundUrl       | String  | 否   | 背景图片完整URL                     |
| course              | String  | 否   | 适用课程ID                          |
| courseName          | String  | 否   | 适用课程名称                        |
| status              | String  | 是   | 模板状态：draft/active/inactive     |
| htmlContent         | String  | 否   | HTML模板内容                        |
| fields              | Array   | 否   | 字段配置列表                        |
| fields[].fieldId    | String  | 是   | 字段唯一标识符                      |
| fields[].fieldType  | String  | 是   | 字段类型                            |
| fields[].fieldLabel | String  | 是   | 字段标签                            |
| fields[].positionX  | Integer | 是   | X坐标位置                           |
| fields[].positionY  | Integer | 是   | Y坐标位置                           |
| fields[].fontSize   | Integer | 是   | 字体大小                            |
| fields[].fontColor  | String  | 是   | 字体颜色                            |
| fields[].fontFamily | String  | 是   | 字体族                              |
| fields[].sortOrder  | Integer | 是   | 排序顺序                            |

#### 响应字段

| 字段名 | 类型 | 说明                     |
| ------ | ---- | ------------------------ |
| -      | -    | 无返回数据，仅返回状态码 |

#### 请求示例

```
POST /publicbiz/certificate/template/update
Content-Type: application/json

{
  "id": 1,
  "name": "新媒体初级培训证书模板（更新版）",
  "type": "training",
  "description": "新媒体初级培训证书模板描述（已更新）",
  "background": "certificate-bg-002.jpg",
  "backgroundUrl": "https://example.com/images/backgrounds/certificate-bg-002.jpg",
  "course": "COURSE_001",
  "courseName": "新媒体初级培训",
  "status": "active",
  "fields": [
    {
      "fieldId": "field_1",
      "fieldType": "name",
      "fieldLabel": "【学员姓名】",
      "positionX": 400,
      "positionY": 220,
      "fontSize": 36,
      "fontColor": "#000",
      "fontFamily": "黑体",
      "sortOrder": 1
    }
  ]
}
```

#### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

### 3.5 删除证书模板 (delete)

#### 接口地址

```
POST /publicbiz/certificate/template/delete
```

#### 功能说明

删除指定的证书模板（软删除）

#### 请求参数

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 模板ID |

#### 响应字段

| 字段名 | 类型 | 说明                     |
| ------ | ---- | ------------------------ |
| -      | -    | 无返回数据，仅返回状态码 |

#### 请求示例

```
POST /publicbiz/certificate/template/delete
Content-Type: application/json

{
  "id": 1
}
```

#### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

### 3.6 证书模板状态更新 (updateStatus)

#### 接口地址

```
POST /publicbiz/certificate/template/updateStatus
```

#### 功能说明

更新指定证书模板的状态，支持启用(active)和停用(inactive)操作

#### 请求参数

| 参数名 | 类型   | 必填 | 说明                                 |
| ------ | ------ | ---- | ------------------------------------ |
| id     | Long   | 是   | 模板ID                               |
| status | String | 是   | 目标状态：active-启用，inactive-停用 |

#### 响应字段

| 字段名 | 类型 | 说明                     |
| ------ | ---- | ------------------------ |
| -      | -    | 无返回数据，仅返回状态码 |

#### 请求示例

启用模板：

```
POST /publicbiz/certificate/template/updateStatus
Content-Type: application/json

{
  "id": 1,
  "status": "active"
}
```

停用模板：

```
POST /publicbiz/certificate/template/updateStatus
Content-Type: application/json

{
  "id": 1,
  "status": "inactive"
}
```

#### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

### 3.7 证书模板统计报表 (statistics)

#### 接口地址

```
GET /publicbiz/certificate/template/statistics
```

#### 功能说明

获取证书模板的统计数据，包括总数、启用数、停用数、草稿数

#### 请求参数

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| -      | -    | -    | 无参数 |

#### 响应字段

| 字段名        | 类型    | 说明               |
| ------------- | ------- | ------------------ |
| total         | Integer | 证书模板总数       |
| activeCount   | Integer | 启用中的模板数量   |
| inactiveCount | Integer | 已停用的模板数量   |
| draftCount    | Integer | 草稿状态的模板数量 |

#### 请求示例

```
GET /publicbiz/certificate/template/statistics
```

#### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 15,
    "activeCount": 8,
    "inactiveCount": 4,
    "draftCount": 3
  }
}
```

### 3.8 证书模板列表查询 (list)

#### 接口地址

```
GET /publicbiz/certificate/template/list
```

#### 功能说明

获取所有证书模板的简要列表，不分页

#### 请求参数

| 参数名 | 类型   | 必填 | 说明                                |
| ------ | ------ | ---- | ----------------------------------- |
| status | String | 否   | 模板状态筛选：draft/active/inactive |

#### 响应字段

| 字段名        | 类型   | 说明         |
| ------------- | ------ | ------------ |
| data          | Array  | 证书模板数组 |
| data[].id     | Long   | 模板ID       |
| data[].name   | String | 模板名称     |
| data[].type   | String | 证书类型     |
| data[].status | String | 模板状态     |

#### 请求示例

```
GET /publicbiz/certificate/template/list?status=active
```

#### 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "name": "新媒体初级培训证书模板",
      "type": "training",
      "status": "active"
    },
    {
      "id": 2,
      "name": "技能等级证书模板",
      "type": "skill",
      "status": "active"
    }
  ]
}
```

## 4. 错误码说明

### 4.1 通用错误码

| 错误码 | 说明           |
| ------ | -------------- |
| 0      | 成功           |
| 400    | 请求参数错误   |
| 401    | 未授权访问     |
| 403    | 权限不足       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

### 4.2 业务错误码

| 错误码 | 说明                         |
| ------ | ---------------------------- |
| 10001  | 证书模板名称已存在           |
| 10002  | 证书模板不存在               |
| 10003  | 证书模板正在使用中，无法删除 |
| 10004  | 证书模板状态不允许此操作     |
| 10005  | 背景图片上传失败             |
| 10006  | HTML模板内容格式错误         |
| 10007  | 字段配置参数错误             |
| 10008  | 模板字段数量超出限制         |
| 10009  | 字段位置坐标超出范围         |
| 10010  | 不支持的字体类型             |

### 4.3 错误响应示例

```json
{
  "code": 10001,
  "msg": "证书模板名称已存在",
  "data": null
}
```

## 5. 注意事项

### 5.1 接口调用说明

1. 所有接口都需要在请求头中携带有效的认证信息
2. POST请求的Content-Type必须为application/json
3. 分页查询的页码从1开始
4. 删除操作为软删除，不会物理删除数据

### 5.2 字段配置说明

1. 字段唯一标识符(fieldId)在同一模板内必须唯一，用作字段的业务标识
2. 字段ID(id)为数据库主键，由系统自动生成
3. 坐标位置以像素为单位，原点(0,0)在左上角
4. 字体大小范围：10-80像素
5. 字体颜色支持十六进制格式，如#333、#FF0000
6. 二维码字段(qrcode)用于证书查验，通常放置在证书右下角位置
7. 二维码字段的字体大小建议设置为12-14像素，颜色建议使用较浅的颜色如#999

### 5.3 HTML模板说明

1. HTML模板支持占位符变量：{{studentName}}、{{certificateCode}}、{{issueDate}}、{{studentId}}、{{qrcodeUrl}}
2. 模板内容会进行安全性检查，不允许包含脚本代码
3. 建议模板尺寸：900px × 600px
4. 二维码字段使用{{qrcodeUrl}}占位符，系统会自动生成证书查验二维码

### 5.4 背景图片说明

1. 支持的图片格式：JPG、PNG、GIF
2. 建议图片尺寸：900px × 600px
3. 图片大小限制：不超过5MB
4. 图片会自动上传到OSS并返回完整URL

## 6. 版本历史

| 版本 | 日期       | 说明                       |
| ---- | ---------- | -------------------------- |
| v1.0 | 2024-08-07 | 初始版本，包含基础CRUD接口 |

---

**文档维护者**: 开发团队 **最后更新**: 2024-08-07 **联系方式**: <EMAIL>
