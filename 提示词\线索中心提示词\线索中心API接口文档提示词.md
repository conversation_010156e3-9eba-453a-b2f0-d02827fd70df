---
description:
globs:
alwaysApply: false
---

提示词请分析以下线索中心相关文件：

1. 数据库表结构：`提示词/线索中心提示词/线索中心数据库表结构.sql`
2. 前端页面文件：
   - 主页面：`src/views/infra/clueCenter/index.vue`
   - 新增线索组件：`src/views/infra/clueCenter/components/AddClue.vue`
   - 分配线索组件：`src/views/infra/clueCenter/components/DistributeClue.vue`
   - 跟进线索组件：`src/views/infra/clueCenter/components/FollowUpClue.vue`

基于这些文件的内容，请为我生成一份完整的线索中心API接口文档，文档需要包含：

**文档结构要求：**

1. 接口概述和基础信息（基础路径、数据格式、统一响应格式）
2. 接口必须包含一个动词指示该接口的功能，例如page/add/update/delete，不要RESTfull API风格
3. 每个接口的详细说明，包括：
   - 接口地址和请求方式
   - 功能说明
   - 请求参数表格（参数名、类型、必填、说明）
   - 响应字段表格（字段名、类型、说明）
   - 完整的请求示例（包含URL和JSON数据）
   - 完整的返回示例（JSON格式）
4. 数据字典（枚举值说明）
5. 错误码说明

**接口范围：**

- 线索管理相关接口（增删改查、分页查询、导出等）
- 线索分配/转移接口
- 跟进记录管理接口
- 其他从前端代码中识别出的相关接口

## 请确保文档格式规范、内容完整，便于开发者理解和对接使用。

---

#线索中心 API 接口文档（动词风格）

## 1. 接口概述和基础信息

- **基础路径**：`/api/publicbiz/leads`
- **数据格式**：请求与响应均采用 `application/json`
- **统一响应格式**：

  ```json
  {
    "code": 0,
    "msg": "success",
    "data": { ... }
  }
  ```

  - `code`：0 表示成功，非0为失败
  - `msg`：提示信息
  - `data`：返回数据体

---

## 2. 接口详细说明

### 2.1 线索管理

#### 2.1.1 分页查询线索列表

- **接口地址**：`POST /api/publicbiz/leads/page`
- **请求方式**：POST
- **功能说明**：分页查询线索列表，支持多条件筛选
- **请求参数**：

| 参数名         | 类型   | 必填 | 说明                  |
| -------------- | ------ | ---- | --------------------- |
| page           | int    | 否   | 页码，默认1           |
| pageSize       | int    | 否   | 每页条数，默认10      |
| leadSource     | string | 否   | 线索来源              |
| leadStatus     | string | 否   | 线索状态              |
| businessModule | string | 否   | 业务模块              |
| keyword        | string | 否   | 客户姓名/电话模糊搜索 |

- **响应字段**：

| 字段名           | 类型   | 说明       |
| ---------------- | ------ | ---------- |
| total            | int    | 总条数     |
| list             | array  | 线索列表   |
| └ id             | string | 线索ID     |
| └ customerName   | string | 客户姓名   |
| └ customerPhone  | string | 联系电话   |
| └ leadSource     | string | 线索来源   |
| └ businessModule | string | 业务模块   |
| └ leadStatus     | string | 线索状态   |
| └ createMethod   | string | 创建方式   |
| └ creator        | string | 创建人     |
| └ currentOwner   | string | 当前跟进人 |
| └ createTime     | string | 创建时间   |
| └ remark         | string | 备注信息   |

- **请求示例**：

  ```json
  POST /api/publicbiz/leads/page
  {
    "page": 1,
    "pageSize": 10,
    "leadSource": "抖音",
    "leadStatus": "未处理"
  }
  ```

- **返回示例**：

  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      "total": 2,
      "list": [
        {
          "id": "XS2024001",
          "customerName": "李女士",
          "customerPhone": "138****5678",
          "leadSource": "抖音",
          "businessModule": "家政业务",
          "leadStatus": "未处理",
          "createMethod": "系统生成",
          "creator": "系统自动",
          "currentOwner": "未分配",
          "createTime": "2024-06-18",
          "remark": "客户有意向，需尽快联系"
        }
      ]
    }
  }
  ```

---

#### 2.1.2 新增线索

- **接口地址**：`POST /api/publicbiz/leads/add`
- **请求方式**：POST
- **功能说明**：新增一条线索
- **请求参数**：

| 参数名         | 类型   | 必填 | 说明             |
| -------------- | ------ | ---- | ---------------- |
| customerName   | string | 是   | 客户姓名         |
| customerPhone  | string | 是   | 联系电话（11位） |
| leadSource     | string | 是   | 线索来源         |
| businessModule | string | 是   | 业务模块         |
| leadStatus     | string | 是   | 线索状态         |
| currentOwner   | string | 是   | 当前跟进人       |
| remark         | string | 否   | 备注信息         |

- **响应字段**：

| 字段名 | 类型   | 说明     |
| ------ | ------ | -------- |
| id     | string | 新线索ID |

- **请求示例**：

  ```json
  POST /api/publicbiz/leads/add
  {
    "customerName": "王先生",
    "customerPhone": "***********",
    "leadSource": "官网注册",
    "businessModule": "高企业务",
    "leadStatus": "未处理",
    "currentOwner": "李四",
    "remark": "客户咨询高企申报"
  }
  ```

- **返回示例**：

  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      "id": "XS2024013"
    }
  }
  ```

---

#### 2.1.3 编辑线索

- **接口地址**：`POST /api/publicbiz/leads/update`
- **请求方式**：POST
- **功能说明**：编辑指定线索信息
- **请求参数**：

| 参数名         | 类型   | 必填 | 说明             |
| -------------- | ------ | ---- | ---------------- |
| id             | string | 是   | 线索ID           |
| customerName   | string | 是   | 客户姓名         |
| customerPhone  | string | 是   | 联系电话（11位） |
| leadSource     | string | 是   | 线索来源         |
| businessModule | string | 是   | 业务模块         |
| leadStatus     | string | 是   | 线索状态         |
| currentOwner   | string | 是   | 当前跟进人       |
| remark         | string | 否   | 备注信息         |

- **响应字段**：

| 字段名 | 类型   | 说明   |
| ------ | ------ | ------ |
| id     | string | 线索ID |

- **请求示例**：

  ```json
  POST /api/publicbiz/leads/update
  {
    "id": "XS2024013",
    "customerName": "王先生",
    "customerPhone": "***********",
    "leadSource": "官网注册",
    "businessModule": "高企业务",
    "leadStatus": "跟进中",
    "currentOwner": "张三",
    "remark": "已电话沟通"
  }
  ```

- **返回示例**：

  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      "id": "XS2024013"
    }
  }
  ```

---

#### 2.1.4 删除线索

- **接口地址**：`POST /api/publicbiz/leads/delete`
- **请求方式**：POST
- **功能说明**：删除指定线索
- **请求参数**：

| 参数名 | 类型   | 必填 | 说明   |
| ------ | ------ | ---- | ------ |
| id     | string | 是   | 线索ID |

- **响应字段**：

| 字段名 | 类型   | 说明         |
| ------ | ------ | ------------ |
| id     | string | 被删除线索ID |

- **请求示例**：

  ```json
  POST /api/publicbiz/leads/delete
  {
    "id": "XS2024013"
  }
  ```

- **返回示例**：

  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      "id": "XS2024013"
    }
  }
  ```

---

### 2.2 线索分配/转移

#### 2.2.1 分配线索

- **接口地址**：`POST /api/publicbiz/leads/assign`
- **请求方式**：POST
- **功能说明**：将线索分配给指定部门和跟进人
- **请求参数**：

| 参数名       | 类型   | 必填 | 说明     |
| ------------ | ------ | ---- | -------- |
| id           | string | 是   | 线索ID   |
| dept1        | string | 是   | 一级部门 |
| dept2        | string | 是   | 二级部门 |
| dept3        | string | 是   | 三级部门 |
| currentOwner | string | 是   | 跟进人   |

- **响应字段**：

| 字段名 | 类型   | 说明   |
| ------ | ------ | ------ |
| id     | string | 线索ID |

- **请求示例**：

  ```json
  POST /api/publicbiz/leads/assign
  {
    "id": "XS2024013",
    "dept1": "业务部门",
    "dept2": "销售部",
    "dept3": "华南销售部",
    "currentOwner": "赵六 (华南销售专员)"
  }
  ```

- **返回示例**：

  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      "id": "XS2024013"
    }
  }
  ```

---

### 2.3 跟进记录管理

#### 2.3.1 查询线索跟进记录

- **接口地址**：`POST /api/publicbiz/leads/follow-up/page`
- **请求方式**：POST
- **功能说明**：获取指定线索的所有跟进记录
- **请求参数**：

| 参数名 | 类型   | 必填 | 说明   |
| ------ | ------ | ---- | ------ |
| leadId | string | 是   | 线索ID |

- **响应字段**：

| 字段名    | 类型   | 说明         |
| --------- | ------ | ------------ |
| list      | array  | 跟进记录列表 |
| └ id      | string | 跟进记录ID   |
| └ content | string | 跟进内容     |
| └ user    | string | 跟进人       |
| └ time    | string | 跟进时间     |

- **请求示例**：

  ```json
  POST /api/publicbiz/leads/follow-up/page
  {
    "leadId": "XS2024013"
  }
  ```

- **返回示例**：

  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      "list": [
        {
          "id": "1",
          "content": "已电话沟通，客户有意向",
          "user": "张三",
          "time": "2024-06-19 10:00"
        }
      ]
    }
  }
  ```

---

#### 2.3.2 新增跟进记录

- **接口地址**：`POST /api/publicbiz/leads/follow-up/add`
- **请求方式**：POST
- **功能说明**：为指定线索添加跟进记录
- **请求参数**：

| 参数名  | 类型   | 必填 | 说明     |
| ------- | ------ | ---- | -------- |
| leadId  | string | 是   | 线索ID   |
| content | string | 是   | 跟进内容 |

- **响应字段**：

| 字段名 | 类型   | 说明       |
| ------ | ------ | ---------- |
| id     | string | 跟进记录ID |

- **请求示例**：

  ```json
  POST /api/publicbiz/leads/follow-up/add
  {
    "leadId": "XS2024013",
    "content": "已上门拜访，客户反馈良好"
  }
  ```

- **返回示例**：

  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      "id": "2"
    }
  }
  ```

---

## 3. 数据字典（枚举值说明）

### 3.1 线索来源（leadSource）

- 官网注册、市场活动、公众号文章、视频号、抖音、入驻商家、小红书、微博、知乎、百度推广、微信朋友圈、QQ群/微信群、线下展会、合作伙伴推荐、老客户推荐、电话营销、短信营销、邮件营销、其他

### 3.2 业务模块（businessModule）

- 高企业务、家政业务、培训业务、认证业务

### 3.3 线索状态（leadStatus）

- 未处理、跟进中、已转化、无意向

### 3.4 创建方式（createMethod）

- 手动创建、系统导入、API接入

---

## 4. 错误码说明

| code | 说明           |
| ---- | -------------- |
| 0    | 成功           |
| 400  | 请求参数错误   |
| 401  | 未授权/未登录  |
| 403  | 无权限         |
| 404  | 资源不存在     |
| 500  | 服务器内部错误 |

---

如需补充导入、批量操作等接口，可根据实际业务场景扩展。若需详细字段类型、约束或示例数据，可参考数据库表结构和前端表单校验规则。

如需接口Mock数据或OpenAPI/Swagger文档格式，请告知！

---

**本接口文档已完全采用动词风格，适合非RESTful风格的前后端对接。**
