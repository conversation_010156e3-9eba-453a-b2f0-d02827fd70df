import request from '@/config/axios'

// 订单信息接口
export interface SettlementOrder {
  id: number
  orderNumber: string
  packageName: string
  agencyName: string
  orderTime: string
  completionTime: string
  practitionerName: string
  orderStatus: string
  settlementStatus: string
  orderAmount: number
  agencyAmount?: number
  platformAmount?: number
  paymentMethod?: string
  specialRequirements?: string
  customerName?: string
  customerPhone?: string
  customerAddress?: string
  agencyPhone?: string
  practitionerPhone?: string
}

// 订单查询参数
export interface SettlementOrderQuery {
  startDate?: string
  endDate?: string
  agencyName?: string
  practitionerName?: string
  orderStatus?: string
  settlementStatus?: string
  page: number
  size: number
}

// 订单列表响应
export interface SettlementOrderResponse {
  list: SettlementOrder[]
  total: number
}

// 服务记录接口
export interface ServiceRecord {
  id: number
  time: string
  title: string
  description: string
  status: string
}

// 订单详情接口
export interface OrderDetail {
  basicInfo: {
    orderNumber: string
    orderStatus: string
    orderTime: string
    completionTime: string
    settlementStatus: string
  }
  serviceInfo: {
    packageName: string
    serviceType: string
    serviceDuration: string
    serviceAddress: string
  }
  agencyInfo: {
    agencyName: string
    agencyPhone: string
    practitionerName: string
    practitionerPhone: string
  }
  customerInfo: {
    customerName: string
    customerPhone: string
    customerAddress: string
    specialRequirements: string
  }
  costInfo: {
    orderAmount: number
    agencyAmount: number
    platformAmount: number
    paymentMethod: string
  }
  serviceRecords: ServiceRecord[]
}

/**
 * 获取结算中心订单列表
 */
export const getSettlementOrderList = (params: SettlementOrderQuery) => {
  return request.get<SettlementOrderResponse>({
    url: '/settlement/orders',
    params
  })
}

/**
 * 获取订单详情
 */
export const getOrderDetail = (orderNumber: string) => {
  return request.get<OrderDetail>({
    url: `/settlement/orders/${orderNumber}/detail`
  })
}

/**
 * 生成对账单
 */
export const generateReconciliationBill = (orderIds: number[]) => {
  return request.post({
    url: '/settlement/reconciliation/generate',
    data: { orderIds }
  })
}

/**
 * 获取结算状态选项
 */
export const getSettlementStatusOptions = () => {
  return request.get({
    url: '/settlement/status-options'
  })
}

/**
 * 获取订单状态选项
 */
export const getOrderStatusOptions = () => {
  return request.get({
    url: '/settlement/order-status-options'
  })
}
