<template>
  <el-drawer
    :model-value="visible"
    size="900px"
    direction="rtl"
    :close-on-click-modal="false"
    :show-close="false"
    class="teacher-detail-drawer"
    @close="$emit('close')"
  >
    <template #header>
      <div class="detail-title">{{ detailData?.name || '讲师' }} - 师资详情</div>
    </template>
    <div class="detail-section first-section">
      <div class="section-title">基本信息</div>
      <el-row :gutter="16">
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">讲师姓名</div
            ><div class="value">{{ detailData?.name || '未填写' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">讲师类型</div
            ><div class="value">{{ detailData?.type || '未填写' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">合作状态</div
            ><div class="value">{{ detailData?.status || '未填写' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">签约状态</div
            ><div class="value">{{ detailData?.signStatus || '未设置' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">签约日期</div
            ><div class="value">{{ formatDate(detailData?.signDate) || '未签署' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">联系电话</div
            ><div class="value">{{ detailData?.phone || '未填写' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">关联机构</div
            ><div class="value">{{ detailData?.org || '无关联机构' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">擅长领域</div
            ><div class="value">{{ detailData?.field || '未填写' }}</div></div
          ></el-col
        >
        <el-col :span="24"
          ><div class="detail-item"
            ><div class="label">讲师简介</div
            ><div class="value">{{ detailData?.description || '未填写' }}</div></div
          ></el-col
        >
      </el-row>
    </div>
    <div class="detail-section">
      <div class="section-title">资质信息</div>
      <el-row :gutter="16">
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">资质类型</div
            ><div class="value">{{ detailData?.certFiles?.[0]?.certType || '未填写' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">资质名称</div
            ><div class="value">{{ detailData?.certFiles?.[0]?.certName || '未填写' }}</div></div
          ></el-col
        >
        <el-col :span="24">
          <div class="detail-item"
            ><div class="label">资质文件</div>
            <div class="value file-list">
              <template v-if="detailData?.certFiles && detailData.certFiles.length">
                <div v-for="(file, idx) in detailData.certFiles" :key="idx" class="file-item">
                  <el-icon style="color: #f56c6c"><i class="el-icon-document"></i></el-icon>
                  <span>{{ file.fileName || file.certName }}</span>
                  <span
                    v-if="file.validStartDate && file.validEndDate"
                    style="color: #909399; font-size: 12px"
                  >
                    (有效期: {{ formatDate(file.validStartDate) }} ~
                    {{ formatDate(file.validEndDate) }})
                  </span>
                  <el-link type="primary" @click="onViewFile(file)">查看</el-link>
                </div>
              </template>
              <template v-else>
                <span>无</span>
              </template>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="detail-section">
      <div class="section-title">合同管理</div>
      <el-row :gutter="16">
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">合同类型</div
            ><div class="value">{{ detailData?.contractType || '未设置' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">合同模板</div
            ><div class="value">{{ detailData?.contractTemplate || '未选择' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">合同编号</div
            ><div class="value">{{ detailData?.contractNo || '未填写' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">合同名称</div
            ><div class="value">{{ detailData?.contractName || '未填写' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">合同周期</div><div class="value">{{ contractPeriodText }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">签署日期</div
            ><div class="value">{{ formatDate(detailData?.signDate) || '未签署' }}</div></div
          ></el-col
        >
        <el-col :span="8"
          ><div class="detail-item"
            ><div class="label">合同金额</div
            ><div class="value">{{
              formatAmount(detailData?.contractAmount) || '未设置'
            }}</div></div
          ></el-col
        >
        <el-col :span="24">
          <div class="detail-item"
            ><div class="label">合同文件</div>
            <div class="value file-list">
              <template v-if="detailData?.contractFileUrl">
                <div class="file-item">
                  <el-icon style="color: #f56c6c"><i class="el-icon-document"></i></el-icon>
                  <span>{{ detailData.contractFileName || '合同文件' }}</span>
                  <el-link
                    type="primary"
                    @click="onViewFile({ fileUrl: detailData.contractFileUrl })"
                    >查看</el-link
                  >
                </div>
              </template>
              <template v-else>
                <span>无</span>
              </template>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="detail-section">
      <div class="section-title">操作日志</div>
      <div v-if="loadingLogs" class="loading-logs">
        <el-icon class="is-loading"><i class="el-icon-loading"></i></el-icon>
        <span>加载中...</span>
      </div>
      <el-timeline v-else>
        <el-timeline-item
          v-for="(log, idx) in logsToShow"
          :key="idx"
          :timestamp="formatLogTime(log.createTime) + ' by ' + log.userName"
        >
          {{ log.action }}
        </el-timeline-item>
        <el-empty v-if="logsToShow.length === 0" description="暂无操作日志" />
      </el-timeline>
    </div>
    <div class="drawer-footer">
      <el-button @click="$emit('close')">关闭</el-button>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import request from '@/config/axios'

const props = defineProps({
  visible: Boolean,
  detailData: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['close', 'edit'])

const operationLogs = ref<any[]>([])
const loadingLogs = ref(false)
const contractPeriodText = computed(() => {
  if (props.detailData?.contractPeriodStart && props.detailData?.contractPeriodEnd) {
    return (
      formatDate(props.detailData.contractPeriodStart) +
      ' ~ ' +
      formatDate(props.detailData.contractPeriodEnd)
    )
  }
  return '未设置'
})

const logsToShow = computed(() => {
  return operationLogs.value.length > 0 ? operationLogs.value : []
})

// 监听详情数据变化和抽屉显示状态，获取操作日志
watch(
  [() => props.detailData, () => props.visible],
  async ([newData, visible]) => {
    if (newData && newData.id && visible) {
      await fetchOperationLogs(newData.id)
    }
  },
  { immediate: true }
)

// 获取操作日志
const fetchOperationLogs = async (teacherId: number) => {
  if (!teacherId) return

  try {
    loadingLogs.value = true

    const res = await request.get({
      url: '/system/operate-log/page',
      params: {
        bizId: teacherId,
        type: 'TEACHER 讲师',
        pageNo: 1,
        pageSize: 100
      }
    })

    // API直接返回 {list: [...], total: ...} 格式
    operationLogs.value = res?.list || []
  } catch (error) {
    console.error('获取操作日志失败:', error)
    operationLogs.value = []
  } finally {
    loadingLogs.value = false
  }
}

function onViewFile(file: any) {
  // 可根据实际情况实现文件预览/下载
  window.open(file.fileUrl || file.url || '#', '_blank')
}

// 格式化日期
function formatDate(dateValue: any): string {
  if (!dateValue) return ''
  try {
    // 处理时间戳（毫秒）
    let timestamp = dateValue
    if (typeof dateValue === 'number' && dateValue > 1000000000000) {
      // 如果是毫秒级时间戳，直接使用
      timestamp = dateValue
    } else if (typeof dateValue === 'number' && dateValue > 1000000000) {
      // 如果是秒级时间戳，转换为毫秒
      timestamp = dateValue * 1000
    } else if (typeof dateValue === 'string') {
      // 如果是字符串，尝试解析
      timestamp = new Date(dateValue).getTime()
    }

    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (error) {
    console.error('日期格式化错误:', error, '原始值:', dateValue)
    return dateValue?.toString() || ''
  }
}

// 格式化金额
function formatAmount(amount: any): string {
  if (!amount && amount !== 0) return ''
  try {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(amount) // 金额以元为单位
  } catch (error) {
    return amount?.toString() || ''
  }
}

// 格式化日志时间
function formatLogTime(timeStr: string) {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return timeStr
  }
}
</script>

<style scoped lang="scss">
.teacher-detail-drawer {
  .el-drawer__body {
    max-height: 70vh;
    overflow-y: auto;
    padding-top: 0;
  }
}
.detail-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
}
.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 12px 0 8px 0;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 4px;
}
.detail-section {
  margin-bottom: 8px;
}
.first-section {
  margin-top: 0;
}
.detail-item {
  margin-bottom: 12px;
  .label {
    color: #888;
    font-size: 13px;
    margin-bottom: 2px;
  }
  .value {
    font-size: 15px;
    color: #222;
    word-break: break-all;
  }
}
.file-list {
  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
  }
}
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 8px;
}
.loading-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #909399;
}
</style>
