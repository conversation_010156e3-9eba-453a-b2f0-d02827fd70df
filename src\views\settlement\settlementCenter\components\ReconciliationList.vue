<!--
  页面名称：对账单列表
  功能描述：展示对账单列表，支持搜索筛选、分页、确认对账、查看详情、导出等操作
-->
<template>
  <div class="reconciliation-list">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="对账单号">
          <el-input
            v-model="searchForm.statementNo"
            placeholder="请输入对账单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="机构名称">
          <el-input
            v-model="searchForm.agencyName"
            placeholder="请输入机构名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="所有状态" value="" />
            <el-option label="待对账确认" value="pending" />
            <el-option label="已确认" value="confirmed" />
            <el-option label="已支付" value="paid" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="生成时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            筛选
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        @row-click="handleRowClick"
      >
        <el-table-column prop="statementNo" label="对账单号" width="180" />
        <el-table-column prop="agencyName" label="机构名称" min-width="150" />
        <el-table-column prop="generationTime" label="生成时间" width="160" />
        <el-table-column prop="totalAmount" label="总金额" width="120">
          <template #default="{ row }">
            <span class="amount-text total-amount">¥{{ formatAmount(row.totalAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="agencyAmount" label="机构分成" width="120">
          <template #default="{ row }">
            <span class="amount-text agency-amount">¥{{ formatAmount(row.agencyAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="platformAmount" label="平台分成" width="120">
          <template #default="{ row }">
            <span class="amount-text platform-amount">¥{{ formatAmount(row.platformAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'pending'"
              type="primary"
              size="small"
              @click.stop="handleConfirmReconciliation(row)"
            >
              确认对账
            </el-button>
            <el-button type="info" size="small" @click.stop="handleViewDetail(row)">
              查看详情
            </el-button>
            <el-button type="success" size="small" @click.stop="handleExport(row)">
              导出
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 对账确认弹窗 -->
    <ReconciliationConfirmDialog
      v-model:visible="confirmDialogVisible"
      :reconciliation-data="currentReconciliation"
      @success="handleConfirmSuccess"
    />

    <!-- 详情抽屉 -->
    <SettlementCenterView
      v-model:visible="detailDrawerVisible"
      :reconciliation-data="currentReconciliation"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import ReconciliationConfirmDialog from './ReconciliationConfirmDialog.vue'
import SettlementCenterView from './settlementCenterView.vue'
// 暂时注释掉接口调用，使用静态数据
// import { getReconciliationList, exportReconciliation } from '@/api/settlement/reconciliation'

/** 搜索表单数据 */
const searchForm = reactive({
  statementNo: '',
  agencyName: '',
  status: '',
  dateRange: []
})

/** 表格数据 */
const tableData = ref([])

/** 加载状态 */
const loading = ref(false)

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 对账确认弹窗显示状态 */
const confirmDialogVisible = ref(false)

/** 详情抽屉显示状态 */
const detailDrawerVisible = ref(false)

/** 当前选中的对账单数据 */
const currentReconciliation = ref(null)

/** 获取对账单列表 */
const fetchList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      size: pagination.size,
      startDate: searchForm.dateRange?.[0] || '',
      endDate: searchForm.dateRange?.[1] || ''
    }

    // 使用静态数据，暂时不需要调用接口
    // const response = await getReconciliationList(params)
    const response = {
      data: {
        list: [
          {
            id: 1,
            statementNo: 'ST20241201001',
            agencyName: '北京家政服务有限公司',
            generationTime: '2024-12-01 10:00:00',
            totalAmount: 15000.0,
            agencyAmount: 12000.0,
            platformAmount: 3000.0,
            status: 'pending'
          },
          {
            id: 2,
            statementNo: 'ST20241201002',
            agencyName: '上海保洁服务公司',
            generationTime: '2024-12-01 11:00:00',
            totalAmount: 8000.0,
            agencyAmount: 6400.0,
            platformAmount: 1600.0,
            status: 'confirmed'
          },
          {
            id: 3,
            statementNo: 'ST20241201003',
            agencyName: '广州月嫂服务中心',
            generationTime: '2024-12-01 12:00:00',
            totalAmount: 25000.0,
            agencyAmount: 20000.0,
            platformAmount: 5000.0,
            status: 'paid'
          }
        ],
        total: 3
      }
    }
    tableData.value = response.data?.list || []
    pagination.total = response.data?.total || 0
  } catch (error) {
    ElMessage.error('获取对账单列表失败')
    console.error('获取对账单列表失败:', error)
  } finally {
    loading.value = false
  }
}

/** 处理搜索 */
const handleSearch = () => {
  pagination.page = 1
  fetchList()
}

/** 处理重置 */
const handleReset = () => {
  Object.assign(searchForm, {
    statementNo: '',
    agencyName: '',
    status: '',
    dateRange: []
  })
  pagination.page = 1
  fetchList()
}

/** 处理行点击 */
const handleRowClick = (row: any) => {
  currentReconciliation.value = row
}

/** 处理确认对账 */
const handleConfirmReconciliation = (row: any) => {
  currentReconciliation.value = row
  confirmDialogVisible.value = true
}

/** 处理查看详情 */
const handleViewDetail = (row: any) => {
  currentReconciliation.value = row
  detailDrawerVisible.value = true
}

/** 处理导出 */
const handleExport = async (row: any) => {
  try {
    // 暂时注释掉导出功能，使用静态数据
    // await exportReconciliation(row.statementNo)
    console.log('导出对账单:', row.statementNo)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出失败:', error)
  }
}

/** 处理确认成功 */
const handleConfirmSuccess = () => {
  confirmDialogVisible.value = false
  fetchList()
  ElMessage.success('对账确认成功')
}

/** 处理分页大小变化 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

/** 处理当前页变化 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

/** 获取状态类型 */
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    confirmed: 'success',
    paid: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取状态文本 */
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待对账确认',
    confirmed: '已确认',
    paid: '已支付',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount?.toFixed(2) || '0.00'
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.reconciliation-list {
  .search-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .search-form {
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 20px;
      }
    }
  }

  .table-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .amount-text {
      font-weight: 600;

      &.total-amount {
        color: #f56c6c;
      }

      &.agency-amount {
        color: #67c23a;
      }

      &.platform-amount {
        color: #409eff;
      }
    }

    .pagination-section {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
