-- 1. 师资库主表（publicbiz_teacher）

CREATE TABLE `publicbiz_teacher` ( `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键', `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '头像', `name` varchar(64) NOT NULL DEFAULT '' COMMENT '讲师姓名', `description` varchar(255) NOT NULL DEFAULT '' COMMENT '简介', `type` varchar(32) NOT NULL DEFAULT '' COMMENT '讲师类型（内部讲师/外部讲师）', `biz` varchar(32) NOT NULL DEFAULT '' COMMENT '业务模块', `org_id` varchar(128) DEFAULT '' COMMENT '关联机构ID', `org` varchar(128) NOT NULL DEFAULT '' COMMENT '关联机构', `field` varchar(128) NOT NULL DEFAULT '' COMMENT '擅长领域', `phone` varchar(32) NOT NULL DEFAULT '' COMMENT '联系电话', `status` varchar(32) NOT NULL DEFAULT '' COMMENT '合作状态', `sign_status` varchar(32) NOT NULL DEFAULT '' COMMENT '电子签约状态', `sign_date` date DEFAULT NULL COMMENT '签约日期', `contract_type` varchar(64) DEFAULT '' COMMENT '合同类型', `contract_template` varchar(128) DEFAULT '' COMMENT '合同模板', `contract_no` varchar(64) DEFAULT '' COMMENT '合同编号', `contract_name` varchar(128) DEFAULT '' COMMENT '合同名称', `contract_period_start` date DEFAULT NULL COMMENT '合同周期开始', `contract_period_end` date DEFAULT NULL COMMENT '合同周期结束', `contract_amount` decimal(18,2) DEFAULT NULL COMMENT '合同金额', `contract_file_name` varchar(255) DEFAULT '' COMMENT '纸质合同附件文件名', `contract_file_url` varchar(512) DEFAULT '' COMMENT '纸质合同附件文件URL', `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `creator` varchar(64) DEFAULT '' COMMENT '创建者', `updater` varchar(64) DEFAULT '' COMMENT '更新者', `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除', `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号', PRIMARY KEY (`id`) ) COMMENT='师资库';

-- 2. 讲师资质表（publicbiz_teacher_cert）

-- 更新后的讲师资质表结构

CREATE TABLE `publicbiz_teacher_cert` ( `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键', `teacher_id` bigint(20) NOT NULL COMMENT '讲师ID', `cert_type` varchar(64) NOT NULL DEFAULT '' COMMENT '资质类型', `cert_name` varchar(128) NOT NULL DEFAULT '' COMMENT '资质名称', `file_name` varchar(255) NOT NULL DEFAULT '' COMMENT '文件名', `file_url` varchar(512) NOT NULL DEFAULT '' COMMENT '文件URL', `valid_start_date` date DEFAULT NULL COMMENT '证件有效期开始时间(yyyy-MM-dd)', `valid_end_date` date DEFAULT NULL COMMENT '证件有效期结束时间(yyyy-MM-dd)', `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `creator` varchar(64) DEFAULT '' COMMENT '创建者', `updater` varchar(64) DEFAULT '' COMMENT '更新者', `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除', `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号', PRIMARY KEY (`id`), KEY `idx_teacher_id` (`teacher_id`) ) COMMENT='讲师资质文件';

-- 为 publicbiz_teacher_cert 表添加证件有效期字段

ALTER TABLE `publicbiz_teacher_cert` ADD COLUMN `valid_start_date` date DEFAULT NULL COMMENT '证件有效期开始时间(yyyy-MM-dd)' AFTER `file_url`, ADD COLUMN `valid_end_date` date DEFAULT NULL COMMENT '证件有效期结束时间(yyyy-MM-dd)' AFTER `valid_start_date`;
