<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="企业培训订单详情"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <!-- 错误边界 -->
    <div v-if="hasError" class="error-boundary">
      <el-alert
        title="组件加载失败"
        :description="errorMessage || '页面出现错误，请刷新重试'"
        type="error"
        show-icon
        :closable="false"
      />
      <div class="error-actions">
        <el-button type="primary" @click="retryLoad">重试</el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </div>

    <!-- 正常内容 -->
    <div v-else class="order-detail-container">
      <!-- 培训项目头部 -->
      <div class="detail-section">
        <div class="project-header">
          <div class="project-title">{{ orderData.trainingProject || '' }}</div>
          <el-tag :type="getOrderStatusType(orderData.orderStatus || '')" size="large">
            {{ getOrderStatusText(orderData.orderStatus || '') }}
          </el-tag>
        </div>

        <div class="detail-list">
          <div class="detail-item">
            <span class="label">订单号：</span>
            <span class="value">{{ orderData.orderNumber || '' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合作企业：</span>
            <span class="value">{{ orderData.companyName || '' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">培训周期：</span>
            <span class="value">{{ formatDateRange(orderData.trainingPeriod) || '' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">培训人数：</span>
            <span class="value">{{ orderData.traineeCount || 0 }}人</span>
          </div>
          <div class="detail-item">
            <span class="label">订单金额：</span>
            <span class="value amount">¥{{ orderData.orderAmount?.toLocaleString() || '0' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">支付状态：</span>
            <el-tag :type="getPaymentStatusType(orderData.paymentStatus || '')" class="status-tag">
              {{ getPaymentStatusText(orderData.paymentStatus || '') }}
            </el-tag>
          </div>
          <div class="detail-item">
            <span class="label">我方负责人：</span>
            <span class="value">{{ orderData.manager || '' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDateTime(orderData.createTime) || '-' }}</span>
          </div>
          <div class="detail-item" v-if="orderData.businessOpportunity">
            <span class="label">关联商机：</span>
            <span class="value">{{
              getBusinessOpportunityNumber(orderData.businessOpportunity)
            }}</span>
          </div>
          <div class="detail-item" v-if="orderData.lead">
            <span class="label">关联线索：</span>
            <span class="value">{{ getLeadNumber(orderData.lead) }}</span>
          </div>
        </div>
      </div>

      <!-- 合同信息区域 -->
      <div class="detail-section">
        <div class="section-title">
          <el-icon><Edit /></el-icon>
          合同信息
        </div>

        <!-- 合同详细信息 -->
        <div
          v-if="
            (mockContractInfo &&
              mockContractInfo.contractStatus !== 'not_uploaded' &&
              mockContractInfo.contractStatus !== 'none') ||
            orderData.contractFileUrl
          "
          class="detail-list"
          v-loading="contractLoading"
        >
          <div class="detail-item">
            <span class="label">合同类型：</span>
            <span class="value">{{
              getContractTypeText(mockContractInfo?.contractType || orderData.contractType || '')
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同编号：</span>
            <span class="value">{{
              mockContractInfo?.contractNumber || orderData.orderNumber || '-'
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同名称：</span>
            <span class="value">{{
              mockContractInfo?.contractName || orderData.trainingProject || '-'
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同周期：</span>
            <span class="value">{{
              mockContractInfo?.startDate && mockContractInfo?.endDate
                ? formatDateRange([mockContractInfo.startDate, mockContractInfo.endDate])
                : formatDateRange(orderData.trainingPeriod) || '-'
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">签署日期：</span>
            <span class="value">{{
              formatDate(mockContractInfo?.startDate) || formatDate(orderData.createTime) || '-'
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同金额：</span>
            <span class="value amount"
              >¥{{
                (mockContractInfo?.amount || orderData.orderAmount)?.toLocaleString() || '0'
              }}</span
            >
          </div>
          <div class="detail-item">
            <span class="label">签约人：</span>
            <span class="value">{{ mockContractInfo?.signer || '-' }}</span>
          </div>
          <div
            class="detail-item"
            v-if="mockContractInfo?.contractFileUrl || orderData.contractFileUrl"
          >
            <span class="label">合同附件：</span>
            <div class="file-item">
              <el-icon><Document /></el-icon>
              <span class="file-name">{{
                getFileNameFromUrl(
                  mockContractInfo?.contractFileUrl || orderData.contractFileUrl || ''
                )
              }}</span>
              <el-button size="small" type="info" @click="handleDownload">下载</el-button>
            </div>
          </div>
          <div
            class="detail-item"
            v-if="!mockContractInfo?.contractFileUrl && !orderData.contractFileUrl"
          >
            <span class="label">合同附件：</span>
            <span class="value">暂无合同附件</span>
          </div>
          <div class="detail-item">
            <span class="label">合同状态：</span>
            <span class="value">{{
              getContractStatusText(
                mockContractInfo?.contractStatus || orderData.contractStatus || ''
              )
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDateTime(orderData.createTime) || '-' }}</span>
          </div>
        </div>

        <!-- 合同操作按钮 -->
        <div
          class="contract-actions"
          v-if="
            (mockContractInfo?.contractFileUrl || orderData.contractFileUrl) &&
            mockContractInfo?.contractStatus !== 'not_uploaded' &&
            mockContractInfo?.contractStatus !== 'none'
          "
        >
          <el-button size="small" type="success" @click="showPaperContractDialog">
            <el-icon><Upload /></el-icon>
            重新上传合同
          </el-button>
        </div>

        <!-- 当没有合同信息或状态为"暂未上传纸质合同"或"none"时显示提示和上传按钮 -->
        <div
          v-if="
            !mockContractInfo ||
            mockContractInfo?.contractStatus === 'not_uploaded' ||
            mockContractInfo?.contractStatus === 'none' ||
            (!mockContractInfo?.contractFileUrl && !orderData.contractFileUrl)
          "
          class="no-contract-info"
        >
          <!-- 上传按钮区域 - 直接显示，不使用el-empty -->
          <div class="upload-actions" style="text-align: center; margin: 20px 0">
            <el-button type="primary" @click="showElectronicContractDialog">
              <el-icon><Document /></el-icon>
              发起电子合同
            </el-button>
            <el-button type="info" @click="showPaperContractDialog">
              <el-icon><Upload /></el-icon>
              上传纸质合同
            </el-button>
          </div>

          <!-- 空状态提示 - 简化版本 -->
          <div
            class="empty-state"
            style="text-align: center; color: #909399; font-size: 14px; margin: 16px 0"
          >
            暂未上传纸质合同
          </div>
        </div>

        <!-- 当有合同信息但状态为"待签署"时显示签署相关按钮 -->
        <div
          class="contract-actions"
          v-if="
            mockContractInfo &&
            mockContractInfo.contractStatus === 'pending' &&
            mockContractInfo.contractFileUrl
          "
        >
          <el-button size="small" type="warning" @click="showPaperContractDialog">
            <el-icon><Edit /></el-icon>
            修改合同
          </el-button>
          <el-button size="small" type="success" @click="showPaperContractDialog">
            <el-icon><Upload /></el-icon>
            重新上传
          </el-button>
        </div>

        <!-- 当有合同信息但状态为"已拒绝"时显示重新上传按钮 -->
        <div
          class="contract-actions"
          v-if="
            mockContractInfo &&
            mockContractInfo.contractStatus === 'rejected' &&
            mockContractInfo.contractFileUrl
          "
        >
          <el-button size="small" type="danger" @click="showPaperContractDialog">
            <el-icon><Upload /></el-icon>
            重新上传合同
          </el-button>
        </div>

        <!-- 当有合同信息但状态为"已取消"时显示重新创建按钮 -->
        <div
          class="contract-actions"
          v-if="
            mockContractInfo &&
            mockContractInfo.contractStatus === 'cancelled' &&
            mockContractInfo.contractFileUrl
          "
        >
          <el-button size="small" type="primary" @click="showElectronicContractDialog">
            <el-icon><Document /></el-icon>
            重新创建合同
          </el-button>
          <el-button size="small" type="info" @click="showPaperContractDialog">
            <el-icon><Upload /></el-icon>
            上传新合同
          </el-button>
        </div>
      </div>

      <!-- 三方协议签署状态 -->
      <div class="detail-section">
        <div class="section-title">三方协议签署状态</div>

        <div class="sign-status-list">
          <div class="sign-status-item">
            <el-icon class="status-icon"><Platform /></el-icon>
            <span class="party-name">平台方</span>
            <el-tag type="success" size="small">已签署</el-tag>
          </div>
          <div class="sign-status-item">
            <el-icon class="status-icon"><School /></el-icon>
            <span class="party-name">培训方</span>
            <el-tag type="success" size="small">已签署</el-tag>
          </div>
          <div class="sign-status-item">
            <el-icon class="status-icon"><OfficeBuilding /></el-icon>
            <span class="party-name">企业方</span>
            <el-tag type="warning" size="small">待签署</el-tag>
          </div>
        </div>
      </div>

      <!-- 纸质合同附件 -->
      <div
        class="detail-section"
        v-if="
          mockContractInfo &&
          mockContractInfo.contractStatus !== 'not_uploaded' &&
          mockContractInfo.contractStatus !== 'none' &&
          (mockContractInfo.contractFileUrl || orderData.contractFileUrl) &&
          (mockContractInfo.contractType === 'paper' || orderData.contractType === 'paper')
        "
      >
        <div class="section-title">纸质合同附件</div>

        <div class="contract-attachment">
          <div class="file-item">
            <el-icon><Document /></el-icon>
            <span class="file-name">{{
              getFileNameFromUrl(
                mockContractInfo?.contractFileUrl || orderData.contractFileUrl || ''
              )
            }}</span>
            <el-button size="small" type="info" @click="handleDownload">下载</el-button>
          </div>
        </div>
      </div>

      <!-- 收款信息 -->
      <div class="detail-section" v-if="shouldShowReceiptInfo">
        <div class="section-title">
          <el-icon><Money /></el-icon>
          收款信息
        </div>

        <div class="receipt-info">
          <div class="receipt-item">
            <span class="receipt-label">收款金额：</span>
            <span class="receipt-value amount"
              >¥{{ orderData.collectionAmount?.toLocaleString() || '0' }}</span
            >
          </div>
          <div class="receipt-item">
            <span class="receipt-label">收款方式：</span>
            <span class="receipt-value">{{
              getCollectionMethodText(orderData.collectionMethod || '')
            }}</span>
          </div>
          <div class="receipt-item">
            <span class="receipt-label">收款日期：</span>
            <span class="receipt-value">{{ formatDate(orderData.collectionDate) || '-' }}</span>
          </div>
          <div class="receipt-item">
            <span class="receipt-label">操作人：</span>
            <span class="receipt-value">{{ orderData.operatorName || '-' }}</span>
          </div>
          <div class="receipt-item">
            <span class="receipt-label">收款备注：</span>
            <span class="receipt-value">{{ orderData.collectionRemark || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 审批流程 -->
      <div class="detail-section">
        <div class="section-title">审批流程</div>

        <!-- 审批流程 -->
        <div class="section-container">
          <div class="section-header">
            <el-icon class="section-icon"><List /></el-icon>
            <span class="section-title">审批流程</span>
          </div>
          <div class="approval-timeline" v-if="approvalList && approvalList.length > 0">
            <div class="timeline-item" v-for="item in approvalList" :key="item.id">
              <div class="timeline-dot" :class="getApprovalStatusClass(item.status)"></div>
              <div class="timeline-content">
                <div class="timeline-time">{{ formatDateTime(item.createTime) }}</div>
                <div class="timeline-action">
                  <strong>{{ item.operatorName }}({{ item.operatorRole }})</strong>
                  {{ item.action }}
                </div>

                <div class="timeline-status">
                  <el-tag :type="getApprovalStatusType(item.status)" size="small">
                    {{ getApprovalStatusText(item.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="!approvalLoading" class="no-approval"> 无审批记录 </div>
          <div v-else class="loading-approval">
            <el-icon class="is-loading"><Loading /></el-icon>
            正在加载审批记录...
          </div>
        </div>

        <!-- 操作日志 -->
        <div class="section-container">
          <div class="section-header">
            <el-icon class="section-icon"><Refresh /></el-icon>
            <span class="section-title">操作日志</span>
            <el-button
              v-if="allOperationLogs && allOperationLogs.length > 0"
              size="small"
              type="text"
              class="view-full-log-btn"
            >
              查看完整日志
            </el-button>
          </div>
          <div class="operation-log-list" v-if="allOperationLogs && allOperationLogs.length > 0">
            <div class="log-item" v-for="log in allOperationLogs.slice(0, 5)" :key="log.id">
              <div class="log-dot"></div>
              <div class="log-content">
                <div class="log-time">{{ formatDateTimeFromTimestamp(log.createTime) }}</div>
                <div class="log-action">
                  <strong>{{ log.operatorName }}({{ log.operatorRole }})</strong>
                  {{ log.logTitle || log.logType }}
                </div>
              </div>
            </div>
            <div v-if="allOperationLogs.length > 5" class="log-more">
              还有 {{ allOperationLogs.length - 5 }} 条记录...
            </div>
          </div>
          <div v-else class="no-logs"> 暂无操作日志 </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <!-- 发起审批按钮 -->
          <div
            class="approval-actions"
            v-if="shouldShowApprovalButton && !shouldShowCollectionButton"
          >
            <el-button size="small" type="primary" @click="showApprovalDialog">
              发起审批
            </el-button>
          </div>

          <!-- 确认收款按钮 -->
          <div class="collection-actions" v-if="shouldShowCollectionButton">
            <el-button size="small" type="success" @click="showCollectionDialog">
              <el-icon><Check /></el-icon>
              确认收款
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 发起电子合同弹窗 -->
  <ElectronicContractDialog
    v-if="electronicContractVisible"
    v-model:visible="electronicContractVisible"
    :order-data="orderData as any"
    @success="handleElectronicContractSuccess"
  />

  <!-- 上传纸质合同弹窗 -->
  <PaperContractDialog
    v-show="paperContractVisible"
    v-model:visible="paperContractVisible"
    :key="`paper-contract-${paperContractKey}`"
    :order-data="mapToUniversityPracticeOrder(orderData)"
    :order-type="'enterprise_training'"
    @success="handlePaperContractSuccess"
    data-testid="paper-contract-dialog"
  />

  <!-- 发起审批弹窗 -->
  <OrderApprovalDialog
    v-if="approvalVisible"
    :key="approvalKey"
    v-model:visible="approvalVisible"
    :order-data="orderData as any"
    :order-type="'enterprise_training'"
    @success="handleApprovalSuccess"
  />

  <!-- 确认收款弹窗 -->
  <CollectionDialog
    v-if="collectionVisible"
    v-model:visible="collectionVisible"
    :order-data="orderData as any"
    :order-type="'enterprise_training'"
    @success="handleCollectionSuccess"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import {
  Document,
  OfficeBuilding,
  Money,
  Tools,
  Refresh,
  List,
  Upload,
  Loading,
  Check,
  Edit,
  Platform,
  School
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import 'element-plus/es/components/message/style/css'
import {
  EnterpriseTrainingOrderApi,
  type EnterpriseTrainingOrder as ApiEnterpriseTrainingOrder
} from '@/api/OrderCenter/EnterpriseTraining'

import {
  getEnterpriseTrainingApprovalRecords,
  type ApprovalRecord,
  getEnterpriseTrainingOperationLogs,
  type OperationLog
} from '@/api/OrderCenter/enterprise-training'

// 直接导入组件，避免异步导入问题
import ElectronicContractDialog from '@/views/OrderCenter/UniversityPracticeCenter/components/ElectronicContractDialog.vue'
import PaperContractDialog from '@/views/OrderCenter/EnterpriseTraining/components/PaperContractDialog.vue'
import OrderApprovalDialog from '@/views/OrderCenter/UniversityPracticeCenter/components/OrderApprovalDialog.vue'
import CollectionDialog from '@/views/OrderCenter/UniversityPracticeCenter/components/CollectionDialog.vue'

// 本地类型定义
interface EnterpriseTrainingOrder {
  id?: number
  orderNumber?: string
  companyName?: string
  trainingProject?: string
  trainingPeriod?: string | string[]
  traineeCount?: number
  orderAmount?: number
  orderStatus?: string
  paymentStatus?: string
  manager?: string
  createTime?: string
  businessOpportunity?: string
  lead?: string
  contractFile?: string
  contractFileUrl?: string // 添加合同文件URL字段
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  operatorName?: string
  collectionRemark?: string
  contractType?: string // 新增合同类型
  contractStatus?: string // 新增合同状态
}

// 合同信息接口
interface ContractInfo {
  contractId: number
  contractName: string
  contractNumber: string
  contractType: string
  contractStatus: string
  contractFileUrl: string
  startDate: string
  endDate: string
  amount: number
  signer: string
  attachmentPath: string
}

// Props
interface Props {
  visible: boolean
  orderData?: EnterpriseTrainingOrder | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  edit: [orderData: EnterpriseTrainingOrder]
  viewFullLog: [orderData: EnterpriseTrainingOrder | null]
  'contract-updated': [data: any]
  'payment-status-changed': [
    data: { orderId: number | undefined; paymentStatus: string; collectionData: any }
  ]
}>()

// 响应式数据
const activeContractTab = ref('electronic')
const paperContractVisible = ref(false)
const approvalVisible = ref(false)
const collectionVisible = ref(false)
const electronicContractVisible = ref(false) // 新增电子合同弹窗

// 合同信息相关
const contractInfo = ref<ContractInfo | null>(null)
const contractLoading = ref(false)

// 弹窗的key，用于强制重新渲染
const paperContractKey = ref(0)

// 审批弹窗的key，用于强制重新渲染
const approvalKey = ref(Date.now())

// 审批流程列表
const approvalList = ref<any[]>([])

// 审批记录加载状态
const approvalLoading = ref(false)

// 存储所有操作日志（用于调试）
const allOperationLogs = ref<OperationLog[]>([])

// 组件错误状态
const hasError = ref(false)
const errorMessage = ref('')

// 计算属性
const orderData = computed(() => {
  return (
    props.orderData || {
      orderNumber: 'ET202406001',
      companyName: 'ABC科技有限公司',
      trainingProject: '数字化转型管理培训',
      trainingPeriod: '2024.07.01 - 2024.07.15',
      traineeCount: 25,
      orderAmount: 125000,
      orderStatus: 'fulfilling',
      paymentStatus: 'paid',
      manager: '李四',
      createTime: '2024-06-15',
      businessOpportunity: 'OPP202406005',
      lead: 'LEAD202406005',
      contractType: 'electronic',
      contractFileUrl: '',
      collectionAmount: 125000,
      collectionMethod: 'bank_transfer',
      collectionDate: '2024-06-15',
      operatorName: '系统',
      collectionRemark: '银行转账'
    }
  )
})

// 合同信息数据
const mockContractInfo = computed(() => {
  // 如果接口返回了空数据（所有字段都是null），则不显示数据
  if (contractInfo.value && typeof contractInfo.value === 'object') {
    const hasValidData = Object.values(contractInfo.value).some(
      (value) => value !== null && value !== undefined && value !== ''
    )
    if (!hasValidData) {
      return null
    }
  }
  return contractInfo.value
})

// 工具函数
const formatDateTime = (dateString: string | undefined) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return ''
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}`
  } catch (error) {
    return ''
  }
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return ''
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (error) {
    return ''
  }
}

// 格式化日期范围为友好格式
const formatDateRange = (dateRange: string[] | string | undefined) => {
  if (!dateRange) return ''

  try {
    let startDate: Date
    let endDate: Date

    // 处理数组格式：["2025-09-01", "2025-09-30"]
    if (Array.isArray(dateRange) && dateRange.length === 2) {
      startDate = new Date(dateRange[0])
      endDate = new Date(dateRange[1])
    }
    // 处理字符串格式：已经格式化的日期范围
    else if (typeof dateRange === 'string') {
      // 如果已经是格式化后的字符串，直接返回
      if (dateRange.includes(' - ')) {
        return dateRange
      }
      // 如果是单个日期，返回该日期
      const singleDate = new Date(dateRange)
      if (!isNaN(singleDate.getTime())) {
        const year = singleDate.getFullYear()
        const month = (singleDate.getMonth() + 1).toString().padStart(2, '0')
        const day = singleDate.getDate().toString().padStart(2, '0')
        return `${year}.${month}.${day}`
      }
      return dateRange
    } else {
      return ''
    }

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return ''

    const startYear = startDate.getFullYear()
    const startMonth = (startDate.getMonth() + 1).toString().padStart(2, '0')
    const startDay = startDate.getDate().toString().padStart(2, '0')

    const endYear = endDate.getFullYear()
    const endMonth = (endDate.getMonth() + 1).toString().padStart(2, '0')
    const endDay = endDate.getDate().toString().padStart(2, '0')

    // 如果开始和结束日期是同一年，显示完整年月日格式
    if (startYear === endYear) {
      return `${startYear}.${startMonth}.${startDay} - ${startYear}.${endMonth}.${endDay}`
    } else {
      return `${startYear}.${startMonth}.${startDay} - ${endYear}.${endMonth}.${endDay}`
    }
  } catch (error) {
    console.error('日期范围格式化失败:', error)
    return ''
  }
}

// 格式化时间戳为日期时间字符串
const formatDateTimeFromTimestamp = (timestamp: number | undefined) => {
  if (!timestamp) return ''
  try {
    const date = new Date(timestamp)
    if (isNaN(date.getTime())) return ''
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    console.error('时间戳格式化失败:', error)
    return ''
  }
}

// 获取文件名
const getFileNameFromUrl = (url: string) => {
  if (!url) return ''
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const fileName = pathname.split('/').pop()
    return fileName || '合同文件'
  } catch (error) {
    console.error('获取文件名失败:', error)
    return '合同文件'
  }
}

// 获取审批状态类型
const getApprovalStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    pending: 'warning',
    approving: 'warning',
    approved: 'success',
    rejected: 'danger',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取审批状态文本
const getApprovalStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待审批',
    approving: '审批中',
    approved: '已通过',
    rejected: '已拒绝',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取审批状态样式类
const getApprovalStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    pending: 'status-pending',
    approved: 'status-approved',
    rejected: 'status-rejected'
  }
  return classMap[status] || ''
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    pending_payment: '待支付',
    pending_fulfillment: '待履约',
    fulfilling: '履约中',
    completed: '已完成',
    closed: '已关闭',
    approval_rejected: '审批驳回'
  }
  return statusMap[status] || status
}

// 获取支付状态类型
const getPaymentStatusType = (status: string): 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'danger'> = {
    unpaid: 'warning',
    pending: 'warning', // 添加pending状态
    paid: 'success',
    refunded: 'info',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    unpaid: '未支付',
    pending: '待支付', // 添加pending状态
    paid: '已支付',
    refunded: '已退款',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取订单状态类型
const getOrderStatusType = (status: string): 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'danger'> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'warning',
    pending_payment: 'warning',
    pending_fulfillment: 'warning',
    fulfilling: 'warning',
    completed: 'success',
    closed: 'danger',
    approval_rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

const getContractTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    electronic: '电子合同',
    paper: '纸质合同'
  }
  return typeMap[type] || type
}

const getCollectionMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    bank_transfer: '银行转账',
    wechat_pay: '微信支付',
    alipay: '支付宝',
    cash: '现金'
  }
  return methodMap[method] || method
}

// 获取合同状态文本
const getContractStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待签署',
    unsigned: '未签署',
    signed: '已签署',
    rejected: '已拒绝',
    cancelled: '已取消',
    not_uploaded: '暂未上传纸质合同',
    none: '无合同' // 新增none状态
  }
  return statusMap[status] || status
}

// 获取商机单号
const getBusinessOpportunityNumber = (businessOpportunity: any) => {
  if (!businessOpportunity) return ''

  // 如果已经是字符串（单号），直接返回
  if (typeof businessOpportunity === 'string') {
    return businessOpportunity
  }

  // 如果是对象，尝试获取单号字段
  if (typeof businessOpportunity === 'object') {
    return (
      businessOpportunity.opportunityNumber ||
      businessOpportunity.opportunityNo ||
      businessOpportunity.id ||
      businessOpportunity
    )
  }

  return businessOpportunity
}

// 获取线索单号
const getLeadNumber = (lead: any) => {
  if (!lead) return ''

  // 如果已经是字符串（单号），直接返回
  if (typeof lead === 'string') {
    return lead
  }

  // 如果是对象，尝试获取单号字段
  if (typeof lead === 'object') {
    return lead.leadNumber || lead.leadNo || lead.id || lead
  }

  return lead
}

// 计算属性
const shouldShowReceiptInfo = computed(() => {
  return orderData.value.paymentStatus === 'paid' && orderData.value.collectionAmount
})

const shouldShowApprovalButton = computed(() => {
  // 草稿、待审批、审批驳回时显示发起审批按钮
  return (
    orderData.value.orderStatus === 'draft' ||
    orderData.value.orderStatus === 'pending_approval' ||
    orderData.value.orderStatus === 'approval_rejected'
  )
})

const shouldShowCollectionButton = computed(() => {
  // 根据审批列表的最后一条数据判断是否显示收款按钮
  if (approvalList.value && approvalList.value.length > 0) {
    const lastApproval = approvalList.value[0] // 最新的审批记录在数组开头

    // 检查最后一条审批记录是否为审批通过
    const isLastApprovalPassed =
      lastApproval.action &&
      (lastApproval.action.includes('通过') ||
        lastApproval.action.includes('审批通过') ||
        lastApproval.action.includes('approved') ||
        lastApproval.status === 'approved' ||
        lastApproval.status === '通过')

    // 最后一条是审批通过且未收款时显示收款按钮
    return isLastApprovalPassed && orderData.value.paymentStatus === 'pending'
  }

  // 如果没有审批记录，则回退到订单状态判断
  return orderData.value.orderStatus === 'approved' && orderData.value.paymentStatus === 'pending'
})

const mappedOrderData = computed(() => {
  return {
    id: orderData.value.id || 0,
    orderNo: orderData.value.orderNumber || '',
    projectName: orderData.value.trainingProject || '',
    universityName: orderData.value.companyName || '',
    enterpriseName: orderData.value.companyName || '',
    startDate: '',
    endDate: '',
    totalAmount: orderData.value.orderAmount || 0,
    managerId: 1,
    managerName: orderData.value.manager || '',
    createTime: orderData.value.createTime || '',
    businessOpportunity: orderData.value.businessOpportunity || '',
    lead: orderData.value.lead || '',
    contractType: orderData.value.contractType || 'electronic',
    contractFileUrl: orderData.value.contractFile || '',
    collectionAmount: orderData.value.collectionAmount || 0,
    collectionMethod: orderData.value.collectionMethod || 'bank_transfer',
    collectionDate: orderData.value.collectionDate || '',
    operatorName: orderData.value.operatorName || '',
    collectionRemark: orderData.value.collectionRemark || '',
    contractStatus: orderData.value.contractStatus || 'pending' // 新增合同状态
  }
})

// 数据映射函数，将 EnterpriseTrainingOrder 转换为 UniversityPracticeOrder 兼容格式
const mapToUniversityPracticeOrder = (order: EnterpriseTrainingOrder) => {
  return {
    id: order.id || 0,
    orderNo: order.orderNumber || '',
    projectName: order.trainingProject || '',
    universityName: order.companyName || '', // 企业名称作为高校名称
    enterpriseName: order.companyName || '',
    startDate: order.trainingPeriod ? order.trainingPeriod.toString().split(' - ')[0] : '',
    endDate: order.trainingPeriod ? order.trainingPeriod.toString().split(' - ')[1] : '',
    totalAmount: order.orderAmount || 0,
    managerId: 1,
    managerName: order.manager || '',
    contractType: order.contractType || 'electronic',
    contractFileUrl: order.contractFileUrl || '',
    contractStatus: order.contractStatus || 'pending',
    createTime: order.createTime || '',
    collectionAmount: order.collectionAmount || 0,
    collectionMethod: order.collectionMethod || 'bank_transfer',
    collectionDate: order.collectionDate || '',
    operatorName: order.operatorName || '',
    collectionRemark: order.collectionRemark || ''
  }
}

// 方法
const showElectronicContractDialog = () => {
  electronicContractVisible.value = true
}

const showPaperContractDialog = () => {
  // 直接设置弹窗状态为 true
  paperContractVisible.value = true

  // 更新 key 值，强制重新渲染
  paperContractKey.value++
}

const showApprovalDialog = () => {
  approvalKey.value = Date.now()
  approvalVisible.value = true
}

const showCollectionDialog = () => {
  collectionVisible.value = true
}

// 电子合同创建成功回调
const handleElectronicContractSuccess = (contractData: any) => {
  electronicContractVisible.value = false
  ElMessage.success('电子合同创建成功')
  // 这里可以更新订单的合同状态
  emit('contract-updated', contractData)
}

// 纸质合同上传成功回调
const handlePaperContractSuccess = (contractData: any) => {
  paperContractVisible.value = false
  ElMessage.success('纸质合同上传成功，合同信息已更新')
  // 这里可以更新订单的合同状态
  emit('contract-updated', contractData)
}

// 审批成功回调
const handleApprovalSuccess = (approvalData: any) => {
  approvalVisible.value = false
  ElMessage.success('审批发起成功')
  // 这里可以刷新审批列表
  initApprovalList()
}

// 收款成功回调
const handleCollectionSuccess = (collectionData: any) => {
  collectionVisible.value = false
  ElMessage.success('收款确认成功')

  // 更新本地订单数据的支付状态
  if (orderData.value) {
    orderData.value.paymentStatus = 'paid'
    orderData.value.collectionAmount = collectionData.amount
    orderData.value.collectionMethod = collectionData.method
    orderData.value.collectionDate = collectionData.date
    orderData.value.operatorName = collectionData.operatorName
    orderData.value.collectionRemark = collectionData.remark
  }

  // 通知父组件更新支付状态
  emit('payment-status-changed', {
    orderId: orderData.value?.id,
    paymentStatus: 'paid',
    collectionData: collectionData
  })
}

// 下载合同文件
const handleDownload = async () => {
  const fileUrl = mockContractInfo.value?.contractFileUrl || orderData.value?.contractFileUrl
  if (!fileUrl) {
    ElMessage.warning('合同附件信息不完整，无法下载')
    return
  }

  try {
    ElMessage.info('正在下载合同附件...')

    // 直接使用浏览器下载，不调用API
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = getFileNameFromUrl(fileUrl)
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('合同附件下载成功')
  } catch (error) {
    console.error('下载合同附件失败:', error)
    ElMessage.error('下载合同附件失败，请重试')
  }
}

// 初始化审批列表
const initApprovalList = async () => {
  try {
    approvalLoading.value = true
    if (orderData.value?.orderNumber) {
      // 调用企业培训订单操作日志接口
      const result = await getEnterpriseTrainingOperationLogs(orderData.value.orderNumber, 1, 1, 50)

      // 检查数据格式，兼容两种返回格式
      let records: OperationLog[] = []
      if (
        result &&
        typeof result === 'object' &&
        'records' in result &&
        Array.isArray(result.records)
      ) {
        // 直接返回格式：{total: 8, records: Array(8)}
        records = result.records
        allOperationLogs.value = result.records
      } else if (
        result &&
        typeof result === 'object' &&
        'code' in result &&
        result.code === 0 &&
        'data' in result &&
        result.data &&
        'records' in result.data &&
        Array.isArray(result.data.records)
      ) {
        // 标准格式：{code: 0, data: {records: [...]}}
        records = result.data.records
        allOperationLogs.value = result.data.records
      } else {
        approvalList.value = []
        return
      }

      // 筛选出审批相关的记录
      const approvalRecords = records.filter((log: OperationLog) => {
        const logType = log.logType || ''
        const logTitle = log.logTitle || ''

        // 只筛选审批相关的记录
        return (
          logType.includes('审批') ||
          logType.includes('通过') ||
          logType.includes('拒绝') ||
          logTitle.includes('审批') ||
          logTitle.includes('通过') ||
          logTitle.includes('拒绝')
        )
      })

      // 将操作日志转换为审批列表需要的格式
      approvalList.value = approvalRecords.map((log: OperationLog) => ({
        id: log.id,
        operatorName: log.operatorName || '未知',
        operatorRole: log.operatorRole || '未知',
        action: log.logTitle || log.logType || '操作',
        status: log.newStatus || '处理中',
        createTime: formatDateTimeFromTimestamp(log.createTime),
        approvalId: `AP${log.id}`,
        approvalNo: `AP${log.id}`,
        approvalType: log.logType || '审批',
        operatorId: log.operatorId || '',
        updateTime: formatDateTimeFromTimestamp(log.createTime)
      }))
    } else {
      // 如果没有订单号，使用空数组
      approvalList.value = []
    }
  } catch (error) {
    console.error('获取操作日志失败:', error)
    // 如果API调用失败，使用空数组
    approvalList.value = []
  } finally {
    approvalLoading.value = false
  }
}

// 获取订单详情
const fetchOrderDetail = async (orderId: number) => {
  try {
    const result = await EnterpriseTrainingOrderApi.getOrderDetail(orderId, 1) // 这里应该从用户信息或store中获取租户ID
    // 这里可以根据需要更新订单数据
    return result
  } catch (error) {
    ElMessage.error('获取订单详情失败')
    return null
  }
}

// 获取合同信息
const fetchContractInfo = async (orderId: number) => {
  try {
    contractLoading.value = true
    const result = await EnterpriseTrainingOrderApi.getContractInfo(orderId, 1) // 这里应该从用户信息或store中获取租户ID

    // 检查返回的数据是否为空（所有字段都是null）
    if (result && typeof result === 'object') {
      const hasValidData = Object.values(result).some(
        (value) => value !== null && value !== undefined && value !== ''
      )

      if (!hasValidData) {
        contractInfo.value = null
        return null
      }
    }

    contractInfo.value = result
    return result
  } catch (error: any) {
    // 404错误是正常的，表示该订单没有合同信息
    if (error?.response?.status === 404) {
      contractInfo.value = null
      return null
    }
    contractInfo.value = null
    return null
  } finally {
    contractLoading.value = false
  }
}

// 获取操作日志
const fetchOperationLogs = async (orderNo: string) => {
  try {
    const result = await EnterpriseTrainingOrderApi.getOperationLogs(orderNo, 1, 1, 50) // 这里应该从用户信息或store中获取租户ID
    return result
  } catch (error) {
    // 不显示错误消息，因为日志可能不存在
    return null
  }
}

// 在组件挂载时获取相关数据
const fetchRelatedData = async () => {
  if (orderData.value?.id) {
    // 并行获取相关数据，使用 Promise.allSettled 确保即使某个接口失败也不影响其他功能
    const results = await Promise.allSettled([
      fetchContractInfo(orderData.value.id),
      fetchOperationLogs(orderData.value.orderNumber || '')
    ])

    // 处理合同信息获取结果
    if (results[0].status === 'rejected') {
      // 合同信息获取失败，但不影响其他功能
    }
  }
}

// 监听订单数据变化，初始化审批列表
watch(
  () => props.orderData,
  (newVal) => {
    if (newVal) {
      // 使用nextTick确保DOM更新完成
      nextTick(() => {
        initApprovalList()
        // 获取相关数据
        fetchRelatedData()
      })
    } else {
      // 如果没有订单数据，清空审批列表
      approvalList.value = []
      approvalLoading.value = false
    }
  },
  { immediate: true } // 改为true，确保立即执行
)

// 重试加载
const retryLoad = () => {
  hasError.value = false
  errorMessage.value = ''
  // 重新初始化数据
  initApprovalList()
}

// 编辑订单
const handleEdit = () => {
  emit('edit', orderData.value)
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 全局错误处理器
const handleError = (error: any) => {
  console.error('组件错误:', error)
}

// 组件错误处理器
const handleComponentError = (error: any, instance: any, info: string) => {
  console.error('组件错误:', error, instance, info)
}

// 在组件挂载时设置错误处理器
onMounted(() => {
  // 如果已经有订单数据，主动初始化审批列表
  if (props.orderData?.id) {
    nextTick(() => {
      initApprovalList()
    })
  }
})

// 在组件卸载时清理错误处理器
onUnmounted(() => {
  // 组件卸载清理
})
</script>

<style scoped lang="scss">
.order-detail-container {
  padding: 20px;
  height: calc(100vh - 200px);
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.detail-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:last-child {
    margin-bottom: 0;
  }
}

.project-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

  .project-title {
    font-size: 18px;
    font-weight: bold;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.detail-list {
  .detail-item {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;

    &.full-width {
      flex-direction: column;
      align-items: flex-start;
    }

    .label {
      font-weight: 500;
      color: #606266;
      min-width: 100px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 16px;
      }

      &.link {
        color: #409eff;
        cursor: pointer;
        text-decoration: underline;
      }
    }
  }
}

.contract-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  justify-content: flex-start;
}

.sign-status-list {
  .sign-status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .status-icon {
      font-size: 20px;
      color: #909399;
    }

    .party-name {
      flex: 1;
      font-weight: 500;
      color: #303133;
    }
  }
}

.contract-attachment {
  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e4e7ed;

    .file-name {
      flex: 1;
      color: #303133;
      font-weight: 500;
    }
  }
}

.receipt-info {
  background: #f0f9ff;
  border: 1px solid #67c23a;
  border-radius: 8px;
  padding: 16px;

  .receipt-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    .receipt-label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      flex-shrink: 0;
    }

    .receipt-value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
}

.approval-timeline {
  .timeline-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;

    .timeline-dot {
      width: 8px;
      height: 8px;
      background: #409eff;
      border-radius: 50%;
      margin-top: 6px;
      flex-shrink: 0;

      &.status-pending {
        background: #e6a23c;
      }

      &.status-approving {
        background: #409eff;
      }

      &.status-approved {
        background: #67c23a;
      }

      &.status-rejected {
        background: #f56c6c;
      }

      &.status-cancelled {
        background: #909399;
      }
    }

    .timeline-content {
      flex: 1;

      .timeline-time {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }

      .timeline-action {
        font-size: 14px;
        color: #303133;
        margin-bottom: 4px;
      }

      .timeline-status {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }
  }

  .no-approval {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 20px 0;
  }

  .loading-approval {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

/* 区域容器样式 */
.section-container {
  margin-bottom: 24px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;

    .section-icon {
      color: #409eff;
      margin-right: 8px;
      font-size: 16px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      flex: 1;
    }

    .view-full-log-btn {
      color: #409eff;
      font-size: 12px;
      padding: 4px 8px;

      &:hover {
        background: #ecf5ff;
        border-radius: 4px;
      }
    }
  }
}

/* 操作日志样式 */
.operation-log-list {
  padding: 16px 20px;

  .log-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .log-dot {
      width: 6px;
      height: 6px;
      background: #409eff;
      border-radius: 50%;
      margin-top: 8px;
      flex-shrink: 0;
    }

    .log-content {
      flex: 1;

      .log-time {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }

      .log-action {
        font-size: 14px;
        color: #303133;
        line-height: 1.4;
      }
    }
  }

  .log-more {
    text-align: center;
    color: #909399;
    font-size: 12px;
    padding: 12px 0;
    font-style: italic;
  }
}

.no-logs {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 20px;
}

/* 操作按钮区域 */
.action-buttons {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: right;

  .approval-actions,
  .collection-actions {
    display: inline-block;
  }
}

.approval-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.collection-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

.error-boundary {
  padding: 20px;
  text-align: center;

  .error-actions {
    margin-top: 20px;
    display: flex;
    gap: 12px;
    justify-content: center;
  }
}

:deep(.el-drawer__header) {
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 0;
}

:deep(.el-drawer__body) {
  padding: 0;
}

/* 异步组件样式 */
.async-loading {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

.async-error {
  text-align: center;
  padding: 20px;
  color: #f56c6c;
  font-size: 14px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
}

/* 文件相关样式 */
.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  .file-name {
    flex: 1;
    color: #606266;
    font-size: 14px;
  }
}

/* 无合同信息状态样式 */
.no-contract-info {
  text-align: center;
  padding: 20px;

  .upload-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 16px;
  }
}

.contract-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  gap: 12px;
}

.contract-attachment {
  .file-item {
    margin-bottom: 8px;
  }
}

.receipt-info {
  .receipt-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;

    .receipt-label {
      font-weight: 500;
      color: #606266;
      min-width: 100px;
      flex-shrink: 0;
    }

    .receipt-value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
      }
    }
  }
}

.sign-status-list {
  .sign-status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;

    .status-icon {
      font-size: 20px;
      color: #409eff;
    }

    .party-name {
      flex: 1;
      font-weight: 500;
      color: #303133;
    }
  }
}
</style>
