<!--
  页面名称：上传纸质合同弹窗
  功能描述：上传纸质合同附件，支持文件选择、合同信息填写等
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="paper-contract-container">
      <!-- 上传表单 -->
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="合同附件" prop="contractFile" required>
          <el-upload
            ref="uploadRef"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
            :limit="1"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            class="contract-upload"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="upload-tip">支持格式: PDF、Word、JPG、PNG, 文件大小不超过10MB</div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="合同编号" prop="contractNumber" required>
          <el-input
            v-model="form.contractNumber"
            placeholder="请输入合同编号"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="合同名称" prop="contractName" required>
          <el-input v-model="form.contractName" placeholder="请输入合同名称" style="width: 100%" />
        </el-form-item>

        <el-form-item label="签署日期" prop="signDate" required>
          <el-date-picker
            v-model="form.signDate"
            type="date"
            placeholder="年-月-日"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="合同金额" prop="contractAmount" required>
          <el-input
            v-model="form.contractAmount"
            placeholder="请输入合同金额"
            style="width: 100%"
            type="number"
            min="0"
            step="0.01"
          >
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
      </el-form>

      <!-- 文件上传状态显示 -->
      <div v-if="fileList.length > 0" class="file-status-section">
        <div class="file-status-header">
          <span class="status-title">文件上传状态</span>
        </div>
        <div class="file-status-items">
          <div v-for="(file, index) in fileList" :key="index" class="file-status-item">
            <div class="file-info">
              <el-icon class="file-icon"><Document /></el-icon>
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
              <el-tag
                :type="
                  file.status === 'success'
                    ? 'success'
                    : file.status === 'fail'
                      ? 'danger'
                      : 'warning'
                "
                size="small"
              >
                {{
                  file.status === 'success'
                    ? '上传成功'
                    : file.status === 'fail'
                      ? '上传失败'
                      : '待上传'
                }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type UploadFile, type UploadFiles } from 'element-plus'
import { Upload, Document } from '@element-plus/icons-vue'
import {
  EnterpriseTrainingOrderApi,
  type ContractUploadParams
} from '@/api/OrderCenter/EnterpriseTraining'

// Props
interface Props {
  visible: boolean
  orderId?: number
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: 0,
  orderData: null
})

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success', data: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const uploadRef = ref()
const loading = ref(false)
const fileList = ref<UploadFile[]>([])

// 表单数据
const form = reactive({
  contractFile: null as File | null,
  contractNumber: '',
  contractName: '',
  signDate: '',
  contractAmount: ''
})

// 表单验证规则
const rules = {
  contractFile: [{ required: true, message: '请选择合同附件', trigger: 'change' }],
  contractNumber: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
  contractName: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
  signDate: [{ required: true, message: '请选择签署日期', trigger: 'change' }],
  contractAmount: [{ required: true, message: '请输入合同金额', trigger: 'blur' }]
}

// 计算属性
const dialogTitle = computed(() => {
  return props.orderData ? `上传纸质合同 - ${props.orderData.orderNumber}` : '上传纸质合同'
})

// 文件大小格式化
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.orderData) {
      // 自动填充一些字段
      form.contractName = `${props.orderData.companyName} - ${props.orderData.trainingProject} 培训合同`
      form.contractAmount = props.orderData.orderAmount?.toString() || ''
    }
  }
)

// 文件选择处理
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  console.log('文件选择:', file)
  if (file.raw) {
    form.contractFile = file.raw
  }
}

// 文件移除处理
const handleFileRemove = (file: UploadFile, files: UploadFiles) => {
  console.log('文件移除:', file)
  form.contractFile = null
}

// 提交表单
const handleSubmit = async () => {
  console.log('=== 提交表单开始 ===')
  console.log('当前文件列表:', fileList.value)
  console.log('表单中的文件:', form.contractFile)

  try {
    await formRef.value.validate()
    console.log('表单验证通过')

    // 直接使用表单中已保存的文件，不依赖fileList
    if (!form.contractFile) {
      console.error('文件验证失败: 表单中没有文件')
      ElMessage.warning('请选择合同附件')
      return
    }

    console.log('文件验证通过:', {
      fileName: form.contractFile.name,
      fileSize: form.contractFile.size,
      fileType: form.contractFile.type
    })

    console.log('文件验证通过，开始上传...')
    loading.value = true

    console.log('开始提交表单，先上传文件...')

    // 先上传文件到 /infra/file/upload 接口
    const fileUrl = await uploadFile(form.contractFile)
    console.log('文件上传完成，URL:', fileUrl)

    // 更新文件状态
    if (fileList.value.length > 0) {
      fileList.value[0].status = 'success'
      fileList.value[0].url = fileUrl
    }

    console.log('开始提交合同信息...')

    // 调用企业培训订单的合同上传API
    const contractParams: ContractUploadParams = {
      orderId: props.orderId || 0,
      tenantId: 1, // 这里应该从用户信息或store中获取
      contractFile: fileUrl,
      contractName: form.contractName,
      contractNumber: form.contractNumber,
      startDate: form.signDate,
      endDate: form.signDate,
      amount: Number(form.contractAmount),
      signer: ''
    }

    const result = await EnterpriseTrainingOrderApi.uploadContract(contractParams)
    console.log('合同上传API调用成功:', result)

    ElMessage.success('纸质合同上传成功')
    emit('success', {
      fileUrl: fileUrl,
      fileName: form.contractFile.name,
      fileSize: form.contractFile.size,
      contractNumber: form.contractNumber,
      contractName: form.contractName,
      signDate: form.signDate,
      contractAmount: form.contractAmount,
      contractId: result.contractId
    })
    handleCancel()
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败，请重试')

    // 更新文件状态为失败
    if (fileList.value.length > 0) {
      fileList.value[0].status = 'fail'
    }
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
  // 重置表单
  form.contractFile = null
  form.contractNumber = ''
  form.contractName = ''
  form.signDate = ''
  form.contractAmount = ''
  // 重置文件列表
  fileList.value = []
}

// 文件上传函数（这里需要根据实际的文件上传接口进行调整）
const uploadFile = async (file: File): Promise<string> => {
  // 模拟文件上传
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(`/uploads/contracts/${Date.now()}_${file.name}`)
    }, 1000)
  })
}
</script>

<style scoped lang="scss">
.paper-contract-container {
  .contract-upload {
    .upload-tip {
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
      margin-top: 8px;
    }
  }

  .file-status-section {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    .file-status-header {
      margin-bottom: 16px;

      .status-title {
        font-weight: 500;
        color: #303133;
        font-size: 14px;
      }
    }

    .file-status-items {
      .file-status-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e4e7ed;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .file-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;

          .file-icon {
            font-size: 16px;
            color: #909399;
          }

          .file-name {
            color: #303133;
            font-weight: 500;
            flex: 1;
          }

          .file-size {
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item.is-required .el-form-item__label:before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload-list) {
  margin-top: 8px;
}
</style>
