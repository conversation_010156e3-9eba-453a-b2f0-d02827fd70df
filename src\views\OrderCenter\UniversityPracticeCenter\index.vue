<!--
  页面名称：高校实践订单首页
  功能描述：展示高校实践订单列表，支持搜索、分页、新增、编辑、删除、查看操作日志
-->
<template>
  <div class="university-practice-index">
    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.totalOrders }}</div>
            <div class="stat-label">总订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div
            class="stat-card pending-orders"
            @click="onPendingOrdersClick"
            title="点击查看待处理订单"
          >
            <div class="stat-number">{{ statistics.pendingOrders }}</div>
            <div class="stat-label">待处理订单</div>
            <div class="stat-badge" v-if="statistics.pendingOrders > 0">
              {{ statistics.pendingOrders }}
            </div>
            <div class="stat-hint">点击查看</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">¥{{ formatAmount(statistics.monthlyAmount) }}</div>
            <div class="stat-label">本月订单金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.completionRate }}%</div>
            <div class="stat-label">订单完成率</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" @submit.prevent="onSearch">
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.orderStatus"
            placeholder="全部订单状态"
            clearable
            style="width: 180px"
          >
            <el-option label="全部订单状态" value="" />
            <el-option label="草稿" value="draft" />
            <el-option label="待审批" value="pending_approval" />
            <el-option label="审批中" value="approving" />
            <el-option label="已批准" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="待支付" value="pending_payment" />
            <el-option label="执行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付状态">
          <el-select
            v-model="searchForm.paymentStatus"
            placeholder="全部支付状态"
            clearable
            style="width: 180px"
          >
            <el-option label="全部支付状态" value="" />
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已退款" value="refunded" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索项目名称、高校、企业..."
            style="width: 300px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="action-buttons">
        <el-button @click="onExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-button type="success" @click="onAdd">
          <el-icon><Plus /></el-icon>
          + 新建高校实践订单
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderNo" label="订单号" width="150" />
        <el-table-column prop="projectUniversity" label="项目/高校" width="300">
          <template #default="scope">
            <div>{{ scope.row.projectName }}</div>
            <div style="color: #909399; font-size: 12px">{{ scope.row.universityName }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="enterpriseName" label="合作企业" width="200" />
        <el-table-column prop="projectPeriod" label="项目周期" width="200">
          <template #default="scope">
            {{ formatDate(scope.row.startDate) }} - {{ formatDate(scope.row.endDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="managerName" label="负责人" width="100" />
        <el-table-column prop="totalAmount" label="订单金额" width="120">
          <template #default="scope"> ¥{{ formatAmount(scope.row.totalAmount || 0) }} </template>
        </el-table-column>
        <el-table-column prop="orderStatus" label="订单状态" width="120">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus || '')">
              {{ getOrderStatusText(scope.row.orderStatus || '') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" label="支付状态" width="120">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus || '')">
              {{ getPaymentStatusText(scope.row.paymentStatus || '') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="350" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="onView(scope.row)">查看</el-button>
            <el-button size="small" type="warning" @click="onEdit(scope.row)">编辑</el-button>
            <el-button
              v-if="canConfirmCollection(scope.row)"
              size="small"
              type="success"
              @click="onConfirmCollection(scope.row)"
            >
              确认收款
            </el-button>
            <el-button size="small" type="danger" @click="onDelete(scope.row)">删除</el-button>
            <el-button size="small" @click="onOptLog(scope.row)">操作日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑抽屉 -->
    <AddUniversityPracticeCenter
      v-model:visible="drawerVisible"
      :is-edit="!!editData"
      :order-data="editData"
      @success="onSuccess"
    />

    <!-- 查看详情抽屉 -->
    <IndividualtrainingOrderView
      v-model:visible="viewDrawerVisible"
      :order-data="currentOrderData"
      @edit="onEditFromView"
      @contract-updated="onContractUpdated"
    />

    <!-- 操作日志抽屉 -->
    <OptLog v-model:visible="optLogVisible" :order-data="currentOrderData" />

    <!-- 收款确认对话框 -->
    <CollectionDialog
      v-model:visible="collectionDialogVisible"
      :order-data="currentOrderData || undefined"
      :is-edit="false"
      @success="onCollectionSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download } from '@element-plus/icons-vue'
import AddUniversityPracticeCenter from './components/AddUniversityPracticeCenter.vue'
import IndividualtrainingOrderView from './components/IndividualtrainingOrderView.vue'
import OptLog from './components/OptLog.vue'
import CollectionDialog from './components/CollectionDialog.vue'
import {
  UniversityPracticeOrderApi,
  type UniversityPracticeOrder,
  type OrderPageParams,
  type ExportParams
} from '@/api/OrderCenter/UniversityPracticeCenter'

// 响应式数据
/** 搜索表单数据 */
const searchForm = ref<{
  orderStatus: string
  paymentStatus: string
  keyword: string
}>({
  orderStatus: '',
  paymentStatus: '',
  keyword: ''
})

/** 表格数据 */
const tableData = ref<UniversityPracticeOrder[]>([])

/** 分页信息 */
const pagination = ref<{
  page: number
  size: number
  total: number
}>({
  page: 1,
  size: 10,
  total: 0
})

/** 加载状态 */
const loading = ref<boolean>(false)

/** 抽屉显示状态 */
const drawerVisible = ref<boolean>(false)
const viewDrawerVisible = ref<boolean>(false)
const optLogVisible = ref<boolean>(false)
const collectionDialogVisible = ref<boolean>(false)

/** 编辑数据 */
const editData = ref<UniversityPracticeOrder | null>(null)

/** 当前订单数据 */
const currentOrderData = ref<UniversityPracticeOrder | null>(null)

/** 统计数据 */
const statistics = ref<{
  totalOrders: number
  pendingOrders: number
  monthlyAmount: number
  completionRate: number
}>({
  totalOrders: 0,
  pendingOrders: 0,
  monthlyAmount: 0,
  completionRate: 0
})

// 方法
/** 格式化金额 */
const formatAmount = (amount: number | undefined | null) => {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return '0'
  }
  return amount.toLocaleString('zh-CN')
}

/** 格式化日期 */
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}

/** 获取订单状态类型 */
const getOrderStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'warning',
    approved: 'success',
    rejected: 'danger',
    pending_payment: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    approved: '已批准',
    rejected: '已拒绝',
    pending_payment: '待支付',
    in_progress: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 获取支付状态类型 */
const getPaymentStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    pending: 'warning',
    paid: 'success',
    refunded: 'info',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取支付状态文本 */
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    refunded: '已退款',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 搜索 */
const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}

/** 重置搜索 */
const onReset = () => {
  searchForm.value = {
    orderStatus: '',
    paymentStatus: '',
    keyword: ''
  }
  onSearch()
}

/** 导出 */
const onExport = async () => {
  try {
    // 构建导出参数，将现有的搜索条件映射到新的导出参数结构
    const params: ExportParams = {
      // 将keyword拆分为具体的字段查询
      projectName: searchForm.value.keyword || undefined, // 项目名称（模糊查询）
      universityName: searchForm.value.keyword || undefined, // 高校名称（模糊查询）
      enterpriseName: searchForm.value.keyword || undefined, // 企业名称（模糊查询）
      projectManager: searchForm.value.keyword || undefined, // 项目经理（模糊查询）
      orderStatus: searchForm.value.orderStatus || undefined, // 订单状态
      paymentStatus: searchForm.value.paymentStatus || undefined, // 支付状态
      exportFormat: 'excel'
    }

    console.log('导出参数:', params)
    const blob = await UniversityPracticeOrderApi.exportOrders(params)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `高校实践订单_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

/** 获取统计数据 */
const fetchStatistics = async () => {
  try {
    const res = await UniversityPracticeOrderApi.getOrderStatistics()
    // 处理接口返回的数据，映射到前端期望的字段
    statistics.value = {
      totalOrders: res.totalOrders || 0,
      pendingOrders: res.pendingApprovalOrders || 0,
      monthlyAmount: res.monthlyNewAmount || 0,
      completionRate:
        res.totalOrders > 0 ? Math.round(((res.completedOrders || 0) / res.totalOrders) * 100) : 0
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

/** 获取列表数据 */
const fetchList = async () => {
  loading.value = true
  try {
    const params: OrderPageParams = {
      page: pagination.value.page,
      size: pagination.value.size,
      ...searchForm.value
    }
    const res = await UniversityPracticeOrderApi.getOrderPage(params)
    tableData.value = res.list
    pagination.value.total = res.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

/** 新增 */
const onAdd = () => {
  editData.value = null
  drawerVisible.value = true
}

/** 编辑 */
const onEdit = async (row: UniversityPracticeOrder) => {
  console.log('编辑行数据:', row) // 调试日志

  if (!row.orderNo) {
    ElMessage.error('订单号不存在，无法编辑')
    return
  }

  try {
    // 显示加载提示
    ElMessage.info('正在获取订单详情...')

    // 调用接口获取完整的订单数据
    const fullOrderData = await UniversityPracticeOrderApi.getOrderByOrderNo(row.orderNo)
    editData.value = fullOrderData
    console.log('完整编辑数据已获取:', editData.value) // 调试日志
    drawerVisible.value = true

    ElMessage.success('订单详情获取成功')
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败，请重试')
  }
}

/** 查看 */
const onView = async (row: UniversityPracticeOrder) => {
  if (!row.orderNo) {
    ElMessage.error('订单号不存在，无法查看详情')
    return
  }

  try {
    // 显示加载提示
    ElMessage.info('正在获取订单详情...')

    // 调用接口获取完整的订单数据
    const fullOrderData = await UniversityPracticeOrderApi.getOrderByOrderNo(row.orderNo)
    currentOrderData.value = fullOrderData
    console.log('获取到的完整订单数据:', fullOrderData)

    // 打开查看详情抽屉
    viewDrawerVisible.value = true

    ElMessage.success('订单详情获取成功')
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败，请重试')
  }
}

/** 从查看详情跳转到编辑 */
const onEditFromView = async (row: UniversityPracticeOrder) => {
  if (!row.orderNo) {
    ElMessage.error('订单号不存在，无法编辑')
    return
  }

  try {
    // 显示加载提示
    ElMessage.info('正在获取订单详情...')

    // 调用接口获取完整的订单数据
    const fullOrderData = await UniversityPracticeOrderApi.getOrderByOrderNo(row.orderNo)
    editData.value = fullOrderData
    console.log('从查看详情获取完整编辑数据:', editData.value) // 调试日志
    drawerVisible.value = true

    ElMessage.success('订单详情获取成功')
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败，请重试')
  }
}

/** 合同更新方法 */
const onContractUpdated = (contractData: any) => {
  if (currentOrderData.value) {
    // 更新当前订单数据的合同信息
    currentOrderData.value.contractStatus = contractData.contractStatus
    currentOrderData.value.contractFileUrl = contractData.contractFileUrl
    currentOrderData.value.contractType = contractData.contractType

    // 同时更新表格中对应行的数据
    const index = tableData.value.findIndex((item) => item.id === currentOrderData.value?.id)
    if (index !== -1) {
      tableData.value[index].contractStatus = contractData.contractStatus
      tableData.value[index].contractFileUrl = contractData.contractFileUrl
      tableData.value[index].contractType = contractData.contractType
    }
  }
}

/** 删除 */
const onDelete = async (row: UniversityPracticeOrder) => {
  try {
    await ElMessageBox.confirm('确定要删除该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    if (row.id && row.orderNo) {
      await UniversityPracticeOrderApi.deleteOrder({
        id: row.id,
        orderNo: row.orderNo
      })
      ElMessage.success('删除成功')
      fetchList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

/** 操作日志 */
const onOptLog = (row: UniversityPracticeOrder) => {
  currentOrderData.value = row
  optLogVisible.value = true
}

/** 操作成功回调 */
const onSuccess = (data?: any) => {
  drawerVisible.value = false
  // 如果是编辑操作，刷新列表数据
  if (editData.value) {
    fetchList()
    editData.value = null
  }
  // 如果是新增操作，刷新统计数据
  else {
    fetchStatistics()
    fetchList()
  }
}

/** 分页大小改变 */
const onSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  fetchList()
}

/** 当前页改变 */
const onCurrentChange = (page: number) => {
  pagination.value.page = page
  fetchList()
}

/** 待处理订单点击事件 */
const onPendingOrdersClick = () => {
  searchForm.value.orderStatus = 'pending_approval'
  searchForm.value.paymentStatus = ''
  searchForm.value.keyword = ''
  onSearch()
}

/** 判断是否可以确认收款 */
const canConfirmCollection = (order: UniversityPracticeOrder) => {
  // 检查订单状态
  if (order.orderStatus !== 'approved' && order.orderStatus !== 'pending_payment') {
    return false
  }

  // 检查是否已收全款
  const totalAmount = order.totalAmount || 0
  const paidAmount = order.paidAmount || 0

  // 如果已收全款，则隐藏确认收款按钮
  if (paidAmount >= totalAmount && totalAmount > 0) {
    return false
  }

  return true
}

/** 确认收款 */
const onConfirmCollection = (order: UniversityPracticeOrder) => {
  currentOrderData.value = order
  collectionDialogVisible.value = true
}

/** 收款成功回调 */
const onCollectionSuccess = () => {
  fetchList()
  fetchStatistics()
}

// 生命周期
onMounted(() => {
  fetchStatistics()
  fetchList()
})
</script>

<style scoped lang="scss">
.university-practice-index {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .statistics-section {
    margin-bottom: 20px;

    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #409eff;
      text-align: center;
      position: relative;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .stat-number {
        font-size: 28px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #666;
      }

      .stat-badge {
        position: absolute;
        top: 0;
        right: 0;
        background-color: #f56c6c;
        color: white;
        border-radius: 10px;
        padding: 4px 8px;
        font-size: 12px;
        font-weight: bold;
        transform: translate(50%, -50%);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        animation: pulse 2s infinite;
      }

      .stat-hint {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        font-size: 12px;
        color: #909399;
        white-space: nowrap;
      }
    }

    .pending-orders {
      border-left: 4px solid #f56c6c;
      background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);

      .stat-number {
        color: #f56c6c;
      }

      .stat-label {
        color: #f56c6c;
        font-weight: 500;
      }

      &:hover {
        background: linear-gradient(135deg, #ffe7e7 0%, #fff5f5 100%);
        border-left-color: #e74c3c;
      }
    }
  }

  @keyframes pulse {
    0% {
      transform: translate(50%, -50%) scale(1);
    }
    50% {
      transform: translate(50%, -50%) scale(1.1);
    }
    100% {
      transform: translate(50%, -50%) scale(1);
    }
  }

  .search-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .table-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .pagination-section {
      padding: 20px;
      text-align: right;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style>
