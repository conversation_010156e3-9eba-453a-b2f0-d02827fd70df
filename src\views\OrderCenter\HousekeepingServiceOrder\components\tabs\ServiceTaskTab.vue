<!--
  页面名称：服务任务列表标签页
  功能描述：展示家政服务订单的服务任务列表，支持批量操作和状态管理
-->
<template>
  <div class="service-task-tab">
    <div class="header-actions">
      <el-select
        v-model="filterForm.taskStatus"
        placeholder="任务状态"
        style="width: 120px; margin-right: 10px"
      >
        <el-option label="全部状态" value="" />
        <el-option label="待指派" value="pending" />
        <el-option label="待执行" value="assigned" />
        <el-option label="执行中" value="in_progress" />
        <el-option label="已完成" value="completed" />
        <el-option label="已取消" value="cancelled" />
      </el-select>
      <el-select
        v-model="filterForm.executor"
        placeholder="执行人员"
        style="width: 120px; margin-right: 10px"
      >
        <el-option label="全部人员" value="" />
        <el-option label="张阿姨" value="张阿姨" />
        <el-option label="李阿姨" value="李阿姨" />
        <el-option label="王阿姨" value="王阿姨" />
        <el-option label="赵阿姨" value="赵阿姨" />
      </el-select>
      <el-date-picker
        v-model="filterForm.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="width: 240px; margin-right: 10px"
      />
      <el-button 
        type="primary" 
        size="small" 
        @click="handleBatchReassign"
        :disabled="selectedTasks.length === 0"
      >
        批量重指派
      </el-button>
    </div>

    <div class="task-table">
      <el-table 
        :data="filteredTasks" 
        style="width: 100%" 
        size="small"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="任务序号" width="150">
          <template #default="scope">
            <div class="task-sequence">
              <div class="task-id">{{ scope.row.taskId }}</div>
              <div class="task-number">{{ scope.row.taskSequence }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="plannedDate" label="计划服务日期" width="120" />
        <el-table-column prop="plannedContent" label="计划服务内容" min-width="200">
          <template #default="scope">
            <div class="service-content">{{ scope.row.plannedContent }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="taskStatus" label="任务状态" width="100">
          <template #default="scope">
            <el-tag :type="getTaskStatusType(scope.row.taskStatus)" size="small">
              {{ getTaskStatusText(scope.row.taskStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="currentPersonnel" label="当前服务人员" width="120" />
        <el-table-column prop="finalPersonnel" label="最终完成人员" width="120" />
        <el-table-column prop="completionTime" label="完成时间" width="150" />
        <el-table-column prop="punchLocation" label="打卡地点" width="200">
          <template #default="scope">
            <div class="punch-location">{{ scope.row.punchLocation }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="completionCertificate" label="完成凭证" width="100">
          <template #default="scope">
            <el-link 
              v-if="scope.row.completionCertificate" 
              type="primary" 
              size="small" 
              @click="handleViewCertificate(scope.row)"
            >
              查看凭证
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" width="120">
          <template #default="scope">
            <div class="remarks-text">{{ scope.row.remarks || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <!-- 待指派状态 -->
            <template v-if="scope.row.taskStatus === 'pending'">
              <el-button type="primary" size="small" @click="handleAssign(scope.row)">
                指派
              </el-button>
              <el-button type="danger" size="small" @click="handleCancel(scope.row)">
                取消
              </el-button>
              <el-button type="warning" size="small" @click="handleEditTask(scope.row)">
                编辑
              </el-button>
            </template>
            
            <!-- 待执行状态 -->
            <template v-else-if="scope.row.taskStatus === 'assigned'">
              <el-button type="success" size="small" @click="handleComplete(scope.row)">
                完成
              </el-button>
              <el-button type="danger" size="small" @click="handleCancel(scope.row)">
                取消
              </el-button>
              <el-button type="warning" size="small" @click="handleEditTask(scope.row)">
                编辑
              </el-button>
            </template>
            
            <!-- 执行中状态 -->
            <template v-else-if="scope.row.taskStatus === 'in_progress'">
              <el-button type="success" size="small" @click="handleComplete(scope.row)">
                完成
              </el-button>
              <el-button type="danger" size="small" @click="handleCancel(scope.row)">
                取消
              </el-button>
              <el-button type="warning" size="small" @click="handleEditTask(scope.row)">
                编辑
              </el-button>
            </template>
            
            <!-- 已完成状态 -->
            <template v-else-if="scope.row.taskStatus === 'completed'">
              <el-button type="warning" size="small" @click="handleEditTask(scope.row)">
                编辑
              </el-button>
            </template>
            
            <!-- 已取消状态 -->
            <template v-else-if="scope.row.taskStatus === 'cancelled'">
              <el-button type="warning" size="small" @click="handleEditTask(scope.row)">
                编辑
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 完成凭证弹窗 -->
    <CompletionCertificateModal
      v-model:visible="certificateModalVisible"
      :task-data="currentTask"
    />

    <!-- 任务编辑弹窗 -->
    <TaskEditModal
      v-model:visible="editModalVisible"
      :task-data="currentTask"
      @success="handleEditSuccess"
    />

    <!-- 批量重指派弹窗 -->
    <BatchReassignModal
      v-model:visible="reassignModalVisible"
      :selected-tasks="selectedTasks"
      @success="handleReassignSuccess"
    />

    <!-- 完成任务弹窗 -->
    <CompleteTaskModal
      v-model:visible="completeModalVisible"
      :task-data="currentTask"
      @success="handleCompleteSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import CompletionCertificateModal from '../modals/CompletionCertificateModal.vue'
import TaskEditModal from '../modals/TaskEditModal.vue'
import BatchReassignModal from '../modals/BatchReassignModal.vue'
import CompleteTaskModal from '../modals/CompleteTaskModal.vue'

interface Props {
  orderDetail: any
}

defineProps<Props>()

// 过滤表单
const filterForm = ref({
  taskStatus: '',
  executor: '',
  dateRange: []
})

// 选中的任务
const selectedTasks = ref<any[]>([])

// 弹窗状态
const certificateModalVisible = ref(false)
const editModalVisible = ref(false)
const reassignModalVisible = ref(false)
const completeModalVisible = ref(false)

// 当前操作的任务
const currentTask = ref<any>(null)

// 模拟任务数据
const tasks = ref([
  {
    taskId: 'DS202406001-TASK-001',
    taskSequence: '第1次/共30次',
    plannedDate: '2024-06-01',
    plannedContent: '产妇护理、新生儿护理、母乳喂养指导',
    taskStatus: 'completed',
    currentPersonnel: '张阿姨',
    finalPersonnel: '张阿姨',
    completionTime: '2024/6/1 09:00:00',
    punchLocation: '上海市浦东新区世纪大道1号',
    completionCertificate: '查看凭证',
    remarks: '111'
  },
  {
    taskId: 'DS202406001-TASK-002',
    taskSequence: '第2次/共30次',
    plannedDate: '2024-06-02',
    plannedContent: '产妇营养餐制作、新生儿护理、产后恢复指导',
    taskStatus: 'completed',
    currentPersonnel: '张阿姨',
    finalPersonnel: '张阿姨',
    completionTime: '2024/6/2 09:00:00',
    punchLocation: '上海市浦东新区世纪大道1号',
    completionCertificate: '查看凭证',
    remarks: '任务已完成'
  },
  {
    taskId: 'DS202406001-TASK-003',
    taskSequence: '第3次/共30次',
    plannedDate: '2024-06-03',
    plannedContent: '产妇护理、新生儿护理、家庭卫生清洁',
    taskStatus: 'completed',
    currentPersonnel: '张阿姨',
    finalPersonnel: '张阿姨',
    completionTime: '2024/6/3 09:00:00',
    punchLocation: '上海市浦东新区世纪大道1号',
    completionCertificate: '查看凭证',
    remarks: '任务已完成'
  },
  {
    taskId: 'DS202406001-TASK-016',
    taskSequence: '第16次/共30次',
    plannedDate: '2024-06-16',
    plannedContent: '产妇护理、新生儿护理、母乳喂养指导',
    taskStatus: 'assigned',
    currentPersonnel: '张阿姨',
    finalPersonnel: '',
    completionTime: '',
    punchLocation: '',
    completionCertificate: '',
    remarks: ''
  },
  {
    taskId: 'DS202406001-TASK-017',
    taskSequence: '第17次/共30次',
    plannedDate: '2024-06-17',
    plannedContent: '产妇营养餐制作、新生儿护理、产后恢复指导',
    taskStatus: 'assigned',
    currentPersonnel: '李阿姨',
    finalPersonnel: '',
    completionTime: '',
    punchLocation: '',
    completionCertificate: '',
    remarks: ''
  },
  {
    taskId: 'DS202406001-TASK-018',
    taskSequence: '第18次/共30次',
    plannedDate: '2024-06-18',
    plannedContent: '产妇护理、新生儿护理、家庭卫生清洁',
    taskStatus: 'assigned',
    currentPersonnel: '王阿姨',
    finalPersonnel: '',
    completionTime: '',
    punchLocation: '',
    completionCertificate: '',
    remarks: ''
  },
  {
    taskId: 'DS202406001-TASK-019',
    taskSequence: '第19次/共30次',
    plannedDate: '2024-06-19',
    plannedContent: '产妇营养餐制作、新生儿护理、育儿知识指导',
    taskStatus: 'pending',
    currentPersonnel: '张阿姨',
    finalPersonnel: '',
    completionTime: '',
    punchLocation: '',
    completionCertificate: '',
    remarks: ''
  },
  {
    taskId: 'DS202406001-TASK-022',
    taskSequence: '第22次/共30次',
    plannedDate: '2024-06-22',
    plannedContent: '产妇营养餐制作、新生儿护理、产后恢复指导',
    taskStatus: 'pending',
    currentPersonnel: '张阿姨',
    finalPersonnel: '',
    completionTime: '',
    punchLocation: '',
    completionCertificate: '',
    remarks: ''
  },
  {
    taskId: 'DS202406001-TASK-023',
    taskSequence: '第23次/共30次',
    plannedDate: '2024-06-23',
    plannedContent: '产妇护理、新生儿护理',
    taskStatus: 'pending',
    currentPersonnel: '张阿姨',
    finalPersonnel: '',
    completionTime: '',
    punchLocation: '',
    completionCertificate: '',
    remarks: ''
  },
  {
    taskId: 'DS202406001-TASK-026',
    taskSequence: '第26次/共30次',
    plannedDate: '2024-06-26',
    plannedContent: '产妇护理、新生儿护理、母乳喂养指导',
    taskStatus: 'cancelled',
    currentPersonnel: '张阿姨',
    finalPersonnel: '',
    completionTime: '',
    punchLocation: '',
    completionCertificate: '',
    remarks: '任务已取消'
  }
])

// 过滤后的任务列表
const filteredTasks = computed(() => {
  let result = tasks.value

  if (filterForm.value.taskStatus) {
    result = result.filter((task) => task.taskStatus === filterForm.value.taskStatus)
  }

  if (filterForm.value.executor) {
    result = result.filter((task) => task.currentPersonnel === filterForm.value.executor)
  }

  return result
})

const getTaskStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    assigned: 'info',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getTaskStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '待指派',
    assigned: '待执行',
    in_progress: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

// 选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedTasks.value = selection
}

// 查看完成凭证
const handleViewCertificate = (task: any) => {
  currentTask.value = task
  certificateModalVisible.value = true
}

// 编辑任务
const handleEditTask = (task: any) => {
  currentTask.value = task
  editModalVisible.value = true
}

// 编辑成功回调
const handleEditSuccess = (updatedTask: any) => {
  const index = tasks.value.findIndex(task => task.taskId === updatedTask.taskId)
  if (index !== -1) {
    tasks.value[index] = { ...tasks.value[index], ...updatedTask }
  }
}

// 批量重指派
const handleBatchReassign = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要重指派的任务')
    return
  }
  reassignModalVisible.value = true
}

// 批量重指派成功回调
const handleReassignSuccess = (auntie: any, tasks: any[]) => {
  tasks.forEach(task => {
    const index = tasks.value.findIndex(t => t.taskId === task.taskId)
    if (index !== -1) {
      tasks.value[index].currentPersonnel = auntie.name
      tasks.value[index].taskStatus = 'assigned'
    }
  })
}

// 指派任务
const handleAssign = (task: any) => {
  currentTask.value = task
  reassignModalVisible.value = true
}

// 完成任务
const handleComplete = (task: any) => {
  currentTask.value = task
  completeModalVisible.value = true
}

// 完成任务成功回调
const handleCompleteSuccess = (updatedTask: any) => {
  const index = tasks.value.findIndex(task => task.taskId === updatedTask.taskId)
  if (index !== -1) {
    tasks.value[index] = { ...tasks.value[index], ...updatedTask }
  }
}

// 取消任务
const handleCancel = async (task: any) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消这个任务吗？',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 更新任务状态
    const index = tasks.value.findIndex(t => t.taskId === task.taskId)
    if (index !== -1) {
      tasks.value[index].taskStatus = 'cancelled'
      tasks.value[index].remarks = '任务已取消'
    }
    
    ElMessage.success('任务已取消')
  } catch {
    // 用户取消操作
  }
}
</script>

<style scoped lang="scss">
.service-task-tab {
  .header-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 20px;
  }

  .task-table {
    .el-table {
      .el-table__header {
        background-color: #f5f7fa;
      }
    }

    .task-sequence {
      .task-id {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }
      
      .task-number {
        font-size: 14px;
        color: #409eff;
        font-weight: 500;
      }
    }

    .service-content {
      line-height: 1.5;
      word-break: break-all;
    }

    .punch-location {
      line-height: 1.5;
      word-break: break-all;
    }

    .remarks-text {
      line-height: 1.5;
      word-break: break-all;
    }
  }
}
</style>
