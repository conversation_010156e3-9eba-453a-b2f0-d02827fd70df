package cn.bztmaster.cnt.module.publicbiz.dto.order;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收款信息DTO
 * 用于接收和传输收款相关信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CollectionInfoDTO {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 支付状态
     */
    @NotBlank(message = "支付状态不能为空")
    @Pattern(regexp = "^(pending|paid|refunded|cancelled)$", message = "支付状态值无效")
    private String paymentStatus;

    /**
     * 收款金额
     */
    @NotNull(message = "收款金额不能为空")
    @DecimalMin(value = "0.01", message = "收款金额必须大于0")
    @Digits(integer = 10, fraction = 2, message = "收款金额格式不正确")
    private BigDecimal collectionAmount;

    /**
     * 收款方式
     */
    @NotBlank(message = "收款方式不能为空")
    @Pattern(regexp = "^(cash|wechat|alipay|bank_transfer|pos|other)$", message = "收款方式值无效")
    private String collectionMethod;

    /**
     * 收款日期
     */
    @NotNull(message = "收款日期不能为空")
    private LocalDate collectionDate;

    /**
     * 操作人姓名
     */
    @NotBlank(message = "操作人姓名不能为空")
    @Size(min = 2, max = 50, message = "操作人姓名长度必须在2-50个字符之间")
    private String operatorName;

    /**
     * 收款备注
     */
    @Size(max = 200, message = "收款备注长度不能超过200个字符")
    private String collectionRemark;
}

