import request from '@/config/axios'

/**
 * 保存开票信息
 */
export function saveInvoiceInfo(data: any) {
  return request.post({
    url: '/settlement/invoice/save',
    data
  })
}

/**
 * 获取发票详情
 */
export function getInvoiceDetail(statementNo: string) {
  return request.get({
    url: `/settlement/invoice/detail/${statementNo}`
  })
}

/**
 * 导出发票详情
 */
export function exportInvoiceDetail(statementNo: string) {
  return request.download({
    url: `/settlement/invoice/export/${statementNo}`
  })
}

/**
 * 获取发票列表
 */
export function getInvoiceList(params: any) {
  return request.get({
    url: '/settlement/invoice/list',
    params
  })
}

/**
 * 更新发票状态
 */
export function updateInvoiceStatus(data: any) {
  return request.post({
    url: '/settlement/invoice/status/update',
    data
  })
}

/**
 * 发票信息查询参数
 */
export interface InvoiceQuery {
  statementNo?: string
  agencyName?: string
  invoiceStatus?: string
  startDate?: string
  endDate?: string
  page: number
  size: number
}

/**
 * 发票信息
 */
export interface InvoiceInfo {
  id: number
  statementId: number
  statementNo: string
  agencyId: number
  agencyName: string
  reconciliationAmount: number
  invoiceNo?: string
  invoiceDate?: string
  invoiceType?: string
  invoiceAmount?: number
  taxRate?: number
  taxAmount?: number
  invoiceStatus: string
  invoicingStatus: string
  invoicingRemark?: string
  operatorId?: number
  operatorName?: string
  remark?: string
  createTime: string
  updateTime: string
}

/**
 * 保存开票信息参数
 */
export interface SaveInvoiceParams {
  statementNo: string
  invoiceStatus: string
  invoiceNo?: string
  invoiceDate?: string
  invoiceType?: string
  invoiceAmount?: string
  taxRate?: string
  remark?: string
}
