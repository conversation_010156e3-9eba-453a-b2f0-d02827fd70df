// 模拟API实现，避免linter错误
// import request from '@/utils/request'

/**
 * 获取待结算数量
 */
export function getPendingSettlementCount() {
  // 模拟API调用
  return Promise.resolve({
    data: 15,
    code: 200,
    message: 'success'
  })
}

/**
 * 获取待结算列表
 */
export function getPendingSettlementList(params: any) {
  // 模拟API调用
  const mockData = [
    {
      id: 1,
      orderNumber: 'DD20240815001',
      packageName: '金牌月嫂26天服务',
      agencyName: '阳光家政',
      orderTime: '2024-08-01 10:30',
      completionTime: '2024-08-15 18:00',
      practitionerName: '王阿姨',
      orderStatus: 'completed',
      settlementStatus: 'pending',
      orderAmount: 8800.0
    },
    {
      id: 2,
      orderNumber: 'DD20240814002',
      packageName: '深度保洁4小时',
      agencyName: '专业保洁',
      orderTime: '2024-08-14 09:00',
      completionTime: '2024-08-14 15:00',
      practitionerName: '李阿姨',
      orderStatus: 'completed',
      settlementStatus: 'settling',
      orderAmount: 280.0
    },
    {
      id: 3,
      orderNumber: 'DD20240813003',
      packageName: '收纳整理6小时',
      agencyName: '整理专家',
      orderTime: '2024-08-13 08:30',
      completionTime: '2024-08-13 16:30',
      practitionerName: '张阿姨',
      orderStatus: 'completed',
      settlementStatus: 'settled',
      orderAmount: 450.0
    },
    {
      id: 4,
      orderNumber: 'DD20240812004',
      packageName: '家电清洗套餐',
      agencyName: '清洗专家',
      orderTime: '2024-08-12 14:00',
      completionTime: '2024-08-12 18:00',
      practitionerName: '赵师傅',
      orderStatus: 'in_service',
      settlementStatus: 'pending',
      orderAmount: 320.0
    },
    {
      id: 5,
      orderNumber: 'DD20240811005',
      packageName: '育儿嫂30天服务',
      agencyName: '母婴护理',
      orderTime: '2024-07-12 11:00',
      completionTime: '2024-08-11 20:00',
      practitionerName: '孙阿姨',
      orderStatus: 'completed',
      settlementStatus: 'failed',
      orderAmount: 12000.0
    }
  ]

  return Promise.resolve({
    data: {
      list: mockData,
      total: mockData.length,
      page: params.page || 1,
      size: params.size || 10
    },
    code: 200,
    message: 'success'
  })
}

/**
 * 生成对账单
 */
export function generateReconciliationBill(orderIds: number[]) {
  // 模拟API调用
  return Promise.resolve({
    data: {
      reconciliationId: 'ZD' + Date.now(),
      orderIds: orderIds,
      status: 'generated'
    },
    code: 200,
    message: 'success'
  })
}

/**
 * 结算中心订单查询参数
 */
export interface SettlementOrderQuery {
  startDate?: string
  endDate?: string
  agencyName?: string
  practitionerName?: string
  orderStatus?: string
  settlementStatus?: string
  page: number
  size: number
}

/**
 * 结算订单信息
 */
export interface SettlementOrder {
  id: number
  orderNumber: string
  packageName: string
  agencyName: string
  orderTime: string
  completionTime: string
  practitionerName: string
  orderStatus: string
  settlementStatus: string
  orderAmount: number
  agencyAmount?: number
  platformAmount?: number
}

/**
 * 订单详情信息
 */
export interface OrderDetail {
  basicInfo: {
    orderNumber: string
    orderStatus: string
    orderTime: string
    completionTime: string
    settlementStatus: string
  }
  serviceInfo: {
    packageName: string
    serviceType: string
    serviceDuration: string
    serviceAddress: string
  }
  agencyInfo: {
    agencyName: string
    agencyPhone: string
    practitionerName: string
    practitionerPhone: string
  }
  customerInfo: {
    customerName: string
    customerPhone: string
    customerAddress: string
    specialRequirements: string
  }
  costInfo: {
    orderAmount: number
    agencyAmount: number
    platformAmount: number
    paymentMethod: string
  }
  serviceRecords: Array<{
    id: number
    time: string
    title: string
    description: string
    status: string
  }>
}
