import request from '@/config/axios'

// ==================== 企业培训订单相关类型定义 ====================

// 企业培训订单基础信息
export interface EnterpriseTrainingOrder {
  id?: number
  orderNo?: string
  orderType?: string
  businessLine?: string
  opportunityId?: string
  leadId?: string
  projectName?: string
  projectDescription?: string
  startDate?: string
  endDate?: string
  totalAmount?: number
  paidAmount?: number
  refundAmount?: number
  paymentStatus?: string
  orderStatus?: string
  managerId?: number
  managerName?: string
  managerPhone?: string
  contractType?: string
  contractFileUrl?: string
  contractStatus?: string
  remark?: string
  enterpriseName?: string
  enterpriseContact?: string
  enterprisePhone?: string
  enterpriseEmail?: string
  enterpriseAddress?: string
  trainingProject?: string
  trainingDescription?: string
  participantsCount?: number
  trainingDuration?: string
  trainingLocation?: string
  trainingType?: string
  perPersonFee?: number
  totalFee?: number
  materialFee?: number
  certificationFee?: number
  createTime?: string
  updateTime?: string
}

// 分页查询参数
export interface EnterpriseTrainingOrderPageParams {
  tenantId: number
  page?: number
  size?: number
  orderStatus?: string
  paymentStatus?: string
  keyword?: string
  startDate?: string
  endDate?: string
}

// 分页查询结果
export interface EnterpriseTrainingOrderPageResult {
  list: EnterpriseTrainingOrder[]
  total: number
}

// 创建订单请求参数
export interface CreateEnterpriseTrainingOrderParams {
  tenantId: number
  opportunityId?: string
  leadId?: string
  enterpriseName: string
  enterpriseContact?: string
  enterprisePhone?: string
  enterpriseEmail?: string
  enterpriseAddress?: string
  trainingProject: string
  trainingDescription?: string
  participantsCount: number
  trainingDuration: string
  trainingLocation?: string
  trainingType?: string
  perPersonFee?: number
  totalFee: number
  materialFee?: number
  certificationFee?: number
  managerId: number
  managerName: string
  managerPhone?: string
  remark?: string
  contractFileUrl?: string // 附件URL
  // 收款信息字段
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  operatorName?: string
  collectionRemark?: string
}

// 更新订单请求参数
export interface UpdateEnterpriseTrainingOrderParams {
  id: number
  tenantId: number
  enterpriseName: string
  trainingProject: string
  participantsCount: number
  trainingDuration: string
  totalFee?: number
  managerId: number
  managerName: string
  contractFileUrl?: string // 附件URL
  // 收款信息字段
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  operatorName?: string
  collectionRemark?: string
}

// 收款确认请求参数
export interface PaymentConfirmParams {
  orderId: number
  tenantId: number
  paymentType: string
  paymentAmount: number
  paymentTime: string
  operatorId: number
  operatorName: string
  paymentRemark?: string
  transactionId?: string
}

// 收款更新请求参数
export interface PaymentUpdateParams {
  paymentId: number
  tenantId: number
  paymentAmount?: number
  paymentTime?: string
  paymentRemark?: string
  operatorId: number
  operatorName: string
}

// 合同上传请求参数
export interface ContractUploadParams {
  orderId: number
  tenantId: number
  contractFile: string
  contractName: string
  contractNumber?: string
  startDate?: string
  endDate?: string
  amount?: number
  signer?: string
}

// 合同提交请求参数
export interface ContractSubmitParams {
  orderId: number
  tenantId: number
  contractStatus: string
  submitRemark?: string
}

// 合同信息
export interface ContractInfo {
  contractId: number
  contractName: string
  contractNumber: string
  contractType: string
  contractStatus: string
  contractFileUrl: string
  startDate: string
  endDate: string
  amount: number
  signer: string
  attachmentPath: string
}

// 审批发起请求参数
export interface ApprovalInitiateParams {
  orderId: number
  tenantId: number
  approvalType: string
  approvalTitle: string
  approvalContent: string
  approverIds: number[]
  priority?: string
}

// 审批通过请求参数
export interface ApprovalApproveParams {
  approvalId: string
  orderId: number
  tenantId: number
  approverId: number
  approverName: string
  approvalComments?: string
  nextStatus: string
}

// 审批拒绝请求参数
export interface ApprovalRejectParams {
  approvalId: string
  orderId: number
  tenantId: number
  approverId: number
  approverName: string
  rejectReason: string
  nextStatus: string
}

// 审批记录
export interface ApprovalRecord {
  id: number
  approvalId: string
  approvalNo: string
  approvalType: string
  status: string
  operatorId: number
  operatorName: string
  operatorRole: string
  action: string
  comments: string
  createTime: string
  updateTime: string
}

// 操作日志
export interface OperationLog {
  id: number
  orderNo: string
  logType: string
  logTitle: string
  logContent: string
  oldStatus: string
  newStatus: string
  operatorId: number
  operatorName: string
  operatorRole: string
  relatedPartyType: string
  relatedPartyName: string
  createTime: string
}

// 统计数据
export interface EnterpriseTrainingOrderStatistics {
  totalOrders: number
  pendingOrders: number
  monthlyAmount: number
  completionRate: number
}

// 导出参数
export interface ExportParams {
  tenantId: number
  orderStatus?: string
  paymentStatus?: string
  keyword?: string
  startDate?: string
  endDate?: string
  exportType: string
}

// 获取支付类型列表（用于下拉选择）
export interface GetDropdownDataParams {
  orderType: string
  businessLine?: string
}

// 商机选项
export interface BusinessOption {
  id: number
  name: string
  customerName: string
  businessType: string
  totalPrice: number
  businessStage: string
  ownerUserName: string
}

// 线索选项
export interface LeadOption {
  id: number
  leadId: string
  customerName: string
  customerPhone: string
  businessModule: string
  leadSource: string
  leadStatus: string
}

// 客户选项
export interface CustomerOption {
  id: number
  customerName: string
  customerPhone: string
}

// 负责人选项
export interface ManagerOption {
  id: number
  managerName: string
  managerPhone: string
}

// 下拉数据响应结果
export interface DropdownDataResult {
  businessOptions: BusinessOption[]
  leadOptions: LeadOption[]
  customerOptions: CustomerOption[]
  managerOptions: ManagerOption[]
}

// 企业培训订单API
export const EnterpriseTrainingOrderApi = {
  // ==================== 基础CRUD接口 ====================

  // 分页查询企业培训订单
  getOrderPage: async (
    params: EnterpriseTrainingOrderPageParams
  ): Promise<EnterpriseTrainingOrderPageResult> => {
    return await request.get({ url: '/publicbiz/enterprise-training-order/page', params })
  },

  // 获取订单详情
  getOrderDetail: async (id: number, tenantId: number): Promise<EnterpriseTrainingOrder> => {
    return await request.get({
      url: `/publicbiz/enterprise-training-order/${id}`,
      params: { tenantId }
    })
  },

  // 创建订单
  createOrder: async (
    data: CreateEnterpriseTrainingOrderParams
  ): Promise<{ id: number; orderNo: string }> => {
    return await request.post({ url: '/publicbiz/enterprise-training-order/create', data })
  },

  // 更新订单
  updateOrder: async (data: UpdateEnterpriseTrainingOrderParams): Promise<boolean> => {
    return await request.put({ url: '/publicbiz/enterprise-training-order/update', data })
  },

  // 删除订单
  deleteOrder: async (id: number, tenantId: number): Promise<boolean> => {
    return await request.delete({
      url: `/publicbiz/enterprise-training-order/${id}`,
      params: { tenantId }
    })
  },

  // ==================== 收款管理接口 ====================

  // 确认收款
  confirmPayment: async (
    data: PaymentConfirmParams
  ): Promise<{ success: boolean; paymentNo: string; message: string }> => {
    return await request.post({ url: '/publicbiz/enterprise-training-order/payment/confirm', data })
  },

  // 更新收款信息
  updatePayment: async (data: PaymentUpdateParams): Promise<boolean> => {
    return await request.put({ url: '/publicbiz/enterprise-training-order/payment/update', data })
  },

  // ==================== 合同管理接口 ====================

  // 上传合同
  uploadContract: async (
    data: ContractUploadParams
  ): Promise<{ contractId: number; success: boolean; message: string }> => {
    return await request.post({ url: '/publicbiz/enterprise-training-order/contract/upload', data })
  },

  // 提交合同
  submitContract: async (data: ContractSubmitParams): Promise<boolean> => {
    return await request.post({ url: '/publicbiz/enterprise-training-order/contract/submit', data })
  },

  // 获取合同信息
  getContractInfo: async (orderId: number, tenantId: number): Promise<ContractInfo> => {
    return await request.get({
      url: '/publicbiz/enterprise-training-order/contract-info',
      params: { orderId, tenantId }
    })
  },

  // ==================== 审批流程接口 ====================

  // 发起审批
  initiateApproval: async (
    data: ApprovalInitiateParams
  ): Promise<{ success: boolean; approvalId: string; message: string }> => {
    return await request.post({
      url: '/publicbiz/enterprise-training-order/approval/initiate',
      data
    })
  },

  // 审批通过
  approveOrder: async (data: ApprovalApproveParams): Promise<boolean> => {
    return await request.post({
      url: '/publicbiz/enterprise-training-order/approval/approve',
      data
    })
  },

  // 审批拒绝
  rejectOrder: async (data: ApprovalRejectParams): Promise<boolean> => {
    return await request.post({ url: '/publicbiz/enterprise-training-order/approval/reject', data })
  },

  // 获取审批记录
  getApprovalRecords: async (
    orderId: number,
    tenantId: number
  ): Promise<{ list: ApprovalRecord[]; total: number }> => {
    return await request.get({
      url: `/publicbiz/enterprise-training-order/approval/records/${orderId}`,
      params: { tenantId }
    })
  },

  // ==================== 操作日志接口 ====================

  // 获取操作日志
  getOperationLogs: async (
    orderNo: string,
    tenantId: number,
    page?: number,
    size?: number
  ): Promise<{ list: OperationLog[]; total: number }> => {
    return await request.get({
      url: '/publicbiz/enterprise-training-order/operation-logs',
      params: { orderNo, tenantId, page, size }
    })
  },

  // ==================== 数据导出接口 ====================

  // 导出订单数据
  exportOrders: async (
    data: ExportParams
  ): Promise<{ success: boolean; downloadUrl: string; fileName: string; message: string }> => {
    return await request.post({ url: '/publicbiz/enterprise-training-order/export', data })
  },

  // ==================== 统计分析接口 ====================

  // 获取统计数据
  getStatistics: async (
    tenantId: number,
    startDate?: string,
    endDate?: string
  ): Promise<EnterpriseTrainingOrderStatistics> => {
    return await request.get({
      url: '/publicbiz/enterprise-training-order/statistics',
      params: { tenantId, startDate, endDate }
    })
  },

  // ==================== 其他辅助接口 ====================

  // 获取订单状态列表（用于下拉选择）
  getOrderStatusList: async (): Promise<{ value: string; label: string }[]> => {
    // 这里可以根据实际需求返回状态列表
    return [
      { value: 'draft', label: '草稿' },
      { value: 'pending_approval', label: '待审批' },
      { value: 'approving', label: '审批中' },
      { value: 'approved', label: '已批准' },
      { value: 'rejected', label: '已拒绝' },
      { value: 'pending_payment', label: '待支付' },
      { value: 'pending_fulfillment', label: '待履约' },
      { value: 'fulfilling', label: '履约中' },
      { value: 'completed', label: '已完成' },
      { value: 'closed', label: '已关闭' },
      { value: 'approval_rejected', label: '审批驳回' }
    ]
  },

  // 获取支付状态列表（用于下拉选择）
  getPaymentStatusList: async (): Promise<{ value: string; label: string }[]> => {
    // 这里可以根据实际需求返回状态列表
    return [
      { value: 'pending', label: '待支付' },
      { value: 'paid', label: '已支付' },
      { value: 'refunded', label: '已退款' },
      { value: 'cancelled', label: '已取消' }
    ]
  },

  // 获取培训类型列表（用于下拉选择）
  getTrainingTypeList: async (): Promise<{ value: string; label: string }[]> => {
    // 这里可以根据实际需求返回类型列表
    return [
      { value: 'skill_training', label: '技能培训' },
      { value: 'management_training', label: '管理培训' },
      { value: 'certification_training', label: '认证培训' }
    ]
  },

  // 获取支付类型列表（用于下拉选择）
  getPaymentTypeList: async (): Promise<{ value: string; label: string }[]> => {
    // 这里可以根据实际需求返回类型列表
    return [
      { value: 'cash', label: '现金' },
      { value: 'wechat', label: '微信支付' },
      { value: 'alipay', label: '支付宝' },
      { value: 'bank_transfer', label: '银行转账' },
      { value: 'pos', label: 'POS机刷卡' },
      { value: 'other', label: '其他' }
    ]
  },

  // 获取下拉数据
  getDropdownData: async (params: GetDropdownDataParams): Promise<DropdownDataResult> => {
    return await request.post({ url: '/publicbiz/order/dropdown-data', data: params })
  }
}

export default EnterpriseTrainingOrderApi
