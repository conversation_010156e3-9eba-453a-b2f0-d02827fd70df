<!--
  页面名称：机构业务数据
  功能描述：展示机构的业务指标、图表等数据
-->
<template>
  <div class="agency-data">
    <!-- 数据筛选 -->
    <div class="data-filter">
      <div class="filter-buttons">
        <el-button
          v-for="period in dataPeriods"
          :key="period.value"
          :type="currentPeriod === period.value ? 'primary' : ''"
          size="small"
          @click="changePeriod(period.value)"
        >
          {{ period.label }}
        </el-button>
      </div>
      <span class="update-time">数据更新于: {{ formatDateTime(updateTime) }}</span>
    </div>

    <!-- 核心业务指标 -->
    <div class="kpi-section">
      <div class="section-title">核心业务指标</div>
      <div class="kpi-grid">
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-file-signature"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.serviceOrders || 0 }}</div>
            <div class="kpi-label">服务订单数</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-handshake"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.interviewSuccessRate || 0 }}%</div>
            <div class="kpi-label">面试成功率</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-star"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.customerSatisfaction || 0 }}%</div>
            <div class="kpi-label">客户好评率</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.complaintRate || 0 }}%</div>
            <div class="kpi-label">客户投诉率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 阿姨相关指标 -->
    <div class="kpi-section">
      <div class="section-title">阿姨相关指标</div>
      <div class="kpi-grid">
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-user-check"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.totalPractitioners || 0 }}</div>
            <div class="kpi-label">在职阿姨总数</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-user-plus"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.newPractitioners || 0 }}</div>
            <div class="kpi-label">期间新增阿姨</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-user-slash"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.lostPractitioners || 0 }}</div>
            <div class="kpi-label">期间流失阿姨</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.activePractitioners || 0 }}</div>
            <div class="kpi-label">期间上单阿姨数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 财务指标 -->
    <div class="kpi-section">
      <div class="section-title">财务指标</div>
      <div class="kpi-grid">
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.totalOrders) }}</div>
            <div class="kpi-label">期间订单总额</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-wallet"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.ourIncome) }}</div>
            <div class="kpi-label">期间我方收入</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-money-check-alt"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.settledAmount) }}</div>
            <div class="kpi-label">期间已结算</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-hourglass-half"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.pendingAmount) }}</div>
            <div class="kpi-label">期间待结算</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <div class="chart-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单趋势 (近6个月)</h3>
          </div>
          <div class="chart-body">
            <div ref="ordersChart" class="chart-container"></div>
          </div>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>服务类型分布 (近30天)</h3>
          </div>
          <div class="chart-body">
            <div ref="categoryChart" class="chart-container"></div>
          </div>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>服务质量趋势 (近6个月)</h3>
          </div>
          <div class="chart-body">
            <div ref="qualityChart" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

/** 机构数据 */
const props = defineProps<{
  agency: any
}>()

/** 数据周期选项 */
const dataPeriods = [
  { label: '近30天', value: '30' },
  { label: '近90天', value: '90' },
  { label: '本年度', value: 'year' },
  { label: '全部', value: 'all' }
]

/** 当前选中的周期 */
const currentPeriod = ref('30')

/** 更新时间 */
const updateTime = ref(new Date())

/** 业务数据 */
const businessData = reactive({
  serviceOrders: 88,
  interviewSuccessRate: 75.6,
  customerSatisfaction: 98.5,
  complaintRate: 1.2,
  totalPractitioners: 125,
  newPractitioners: 12,
  lostPractitioners: 3,
  activePractitioners: 89,
  totalOrders: 450800,
  ourIncome: 45080,
  settledAmount: 35800,
  pendingAmount: 9280
})

/** 图表引用 */
const ordersChart = ref()
const categoryChart = ref()
const qualityChart = ref()

/** 图表实例 */
let ordersChartInstance: echarts.ECharts | null = null
let categoryChartInstance: echarts.ECharts | null = null
let qualityChartInstance: echarts.ECharts | null = null

/** 切换数据周期 */
const changePeriod = (period: string) => {
  currentPeriod.value = period
  fetchBusinessData()
}

/** 更新图表数据 */
const updateChartData = () => {
  // 根据当前周期更新图表数据
  if (ordersChartInstance) {
    const orderData = getOrderDataByPeriod()
    ordersChartInstance.setOption({
      series: [{ data: orderData }]
    })
  }

  if (categoryChartInstance) {
    const categoryData = getCategoryDataByPeriod()
    categoryChartInstance.setOption({
      series: [{ data: categoryData }]
    })
  }

  if (qualityChartInstance) {
    const qualityData = getQualityDataByPeriod()
    qualityChartInstance.setOption({
      series: [{ data: qualityData }]
    })
  }
}

/** 根据周期获取订单数据 */
const getOrderDataByPeriod = () => {
  switch (currentPeriod.value) {
    case '30':
      return [65, 60, 80, 78, 55, 95]
    case '90':
      return [45, 52, 68, 75, 82, 88]
    case 'year':
      return [120, 135, 150, 165, 180, 195]
    case 'all':
      return [200, 220, 240, 260, 280, 300]
    default:
      return [65, 60, 80, 78, 55, 95]
  }
}

/** 根据周期获取服务类型分布数据 */
const getCategoryDataByPeriod = () => {
  switch (currentPeriod.value) {
    case '30':
      return [
        { value: 35, name: '月嫂' },
        { value: 25, name: '育儿嫂' },
        { value: 20, name: '保洁' },
        { value: 15, name: '护工' },
        { value: 5, name: '其他' }
      ]
    case '90':
      return [
        { value: 40, name: '月嫂' },
        { value: 30, name: '育儿嫂' },
        { value: 18, name: '保洁' },
        { value: 10, name: '护工' },
        { value: 2, name: '其他' }
      ]
    case 'year':
      return [
        { value: 45, name: '月嫂' },
        { value: 35, name: '育儿嫂' },
        { value: 15, name: '保洁' },
        { value: 8, name: '护工' },
        { value: 2, name: '其他' }
      ]
    case 'all':
      return [
        { value: 50, name: '月嫂' },
        { value: 40, name: '育儿嫂' },
        { value: 12, name: '保洁' },
        { value: 5, name: '护工' },
        { value: 1, name: '其他' }
      ]
    default:
      return [
        { value: 35, name: '月嫂' },
        { value: 25, name: '育儿嫂' },
        { value: 20, name: '保洁' },
        { value: 15, name: '护工' },
        { value: 5, name: '其他' }
      ]
  }
}

/** 根据周期获取服务质量数据 */
const getQualityDataByPeriod = () => {
  switch (currentPeriod.value) {
    case '30':
      return [98.2, 98.5, 99.2, 98.8, 98.5, 99.5]
    case '90':
      return [97.5, 97.8, 98.2, 98.5, 98.8, 99.0]
    case 'year':
      return [96.8, 97.2, 97.8, 98.2, 98.5, 98.8]
    case 'all':
      return [95.5, 96.2, 96.8, 97.2, 97.5, 97.8]
    default:
      return [98.2, 98.5, 99.2, 98.8, 98.5, 99.5]
  }
}

/** 获取业务数据 */
const fetchBusinessData = async () => {
  try {
    // 根据时间周期更新数据
    console.log('获取业务数据，周期:', currentPeriod.value)

    // 模拟不同时间周期的数据
    switch (currentPeriod.value) {
      case '30':
        // 近30天数据
        Object.assign(businessData, {
          serviceOrders: 88,
          interviewSuccessRate: 75.6,
          customerSatisfaction: 98.5,
          complaintRate: 1.2,
          totalPractitioners: 125,
          newPractitioners: 12,
          lostPractitioners: 3,
          activePractitioners: 89,
          totalOrders: 450800,
          ourIncome: 45080,
          settledAmount: 35800,
          pendingAmount: 9280
        })
        break
      case '90':
        // 近90天数据
        Object.assign(businessData, {
          serviceOrders: 245,
          interviewSuccessRate: 78.2,
          customerSatisfaction: 97.8,
          complaintRate: 1.5,
          totalPractitioners: 125,
          newPractitioners: 28,
          lostPractitioners: 8,
          activePractitioners: 245,
          totalOrders: 1250000,
          ourIncome: 125000,
          settledAmount: 98000,
          pendingAmount: 27000
        })
        break
      case 'year':
        // 本年度数据
        Object.assign(businessData, {
          serviceOrders: 1250,
          interviewSuccessRate: 82.1,
          customerSatisfaction: 96.5,
          complaintRate: 2.1,
          totalPractitioners: 125,
          newPractitioners: 45,
          lostPractitioners: 12,
          activePractitioners: 1250,
          totalOrders: 5800000,
          ourIncome: 580000,
          settledAmount: 450000,
          pendingAmount: 130000
        })
        break
      case 'all':
        // 全部数据
        Object.assign(businessData, {
          serviceOrders: 3200,
          interviewSuccessRate: 85.3,
          customerSatisfaction: 95.8,
          complaintRate: 2.8,
          totalPractitioners: 125,
          newPractitioners: 120,
          lostPractitioners: 35,
          activePractitioners: 3200,
          totalOrders: 15800000,
          ourIncome: 1580000,
          settledAmount: 1200000,
          pendingAmount: 380000
        })
        break
    }

    // 更新图表数据
    updateChartData()

    // 模拟数据更新
    updateTime.value = new Date()
  } catch (error) {
    console.error('获取业务数据失败:', error)
  }
}

/** 初始化订单趋势图表 */
const initOrdersChart = () => {
  if (!ordersChart.value) return

  ordersChartInstance = echarts.init(ordersChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: [15, 22, 18, 25, 30, 28],
        type: 'line',
        smooth: true
      }
    ]
  }
  ordersChartInstance.setOption(option)
}

/** 初始化服务类型分布图表 */
const initCategoryChart = () => {
  if (!categoryChart.value) return

  categoryChartInstance = echarts.init(categoryChart.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35, name: '月嫂' },
          { value: 25, name: '育儿嫂' },
          { value: 20, name: '保洁' },
          { value: 15, name: '护工' },
          { value: 5, name: '其他' }
        ]
      }
    ]
  }
  categoryChartInstance.setOption(option)
}

/** 初始化服务质量趋势图表 */
const initQualityChart = () => {
  if (!qualityChart.value) return

  qualityChartInstance = echarts.init(qualityChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      min: 90,
      max: 100
    },
    series: [
      {
        data: [95, 96, 97, 98, 98.5, 98.5],
        type: 'line',
        smooth: true
      }
    ]
  }
  qualityChartInstance.setOption(option)
}

/** 格式化金额 */
const formatMoney = (amount: number) => {
  if (!amount) return '¥0.00'
  return `¥${amount.toLocaleString()}`
}

/** 格式化日期时间 */
const formatDateTime = (date: Date) => {
  return date.toLocaleString()
}

/** 监听窗口大小变化 */
const handleResize = () => {
  ordersChartInstance?.resize()
  categoryChartInstance?.resize()
  qualityChartInstance?.resize()
}

onMounted(() => {
  fetchBusinessData()

  // 延迟初始化图表，确保DOM已渲染
  setTimeout(() => {
    initOrdersChart()
    initCategoryChart()
    initQualityChart()
  }, 100)

  window.addEventListener('resize', handleResize)
})

// 监听机构变化
watch(
  () => props.agency,
  () => {
    if (props.agency) {
      fetchBusinessData()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.agency-data {
  .data-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
  }

  .filter-buttons {
    display: flex;
    gap: 8px;
  }

  .update-time {
    font-size: 12px;
    color: #6c757d;
  }

  .kpi-section {
    margin-bottom: 30px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #343a40;
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
  }

  .kpi-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }

  .kpi-item {
    display: flex;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .kpi-icon {
    font-size: 24px;
    color: #3498db;
    margin-right: 15px;
    flex-shrink: 0;
  }

  .kpi-info {
    .kpi-value {
      font-size: 22px;
      font-weight: 700;
      color: #343a40;
      display: block;
    }

    .kpi-label {
      font-size: 13px;
      color: #6c757d;
    }
  }

  .chart-section {
    margin-top: 30px;
  }

  .chart-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  .chart-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .chart-header {
    padding: 12px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #343a40;
    }
  }

  .chart-body {
    padding: 15px;
  }

  .chart-container {
    width: 100%;
    height: 200px;
  }
}
</style>
