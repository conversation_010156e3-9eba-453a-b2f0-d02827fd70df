package cn.bztmaster.cnt.module.publicbiz.service.order.impl;

import cn.bztmaster.cnt.module.publicbiz.dto.order.CollectionInfoDTO;
import cn.bztmaster.cnt.module.publicbiz.dto.order.CollectionResultDTO;
import cn.bztmaster.cnt.module.publicbiz.entity.order.UniversityPracticeOrder;
import cn.bztmaster.cnt.module.publicbiz.enums.CollectionMethodEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants;
import cn.bztmaster.cnt.module.publicbiz.mapper.order.UniversityPracticeOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.order.CollectionService;
import cn.bztmaster.cnt.module.system.service.log.LogRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 收款服务实现类
 * 实现订单收款相关的业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CollectionServiceImpl implements CollectionService {

    private final UniversityPracticeOrderMapper universityPracticeOrderMapper;
    private final LogRecordService logRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CollectionResultDTO confirmCollection(CollectionInfoDTO collectionInfo) {
        log.info("开始确认收款，订单ID: {}, 订单号: {}", collectionInfo.getOrderId(), collectionInfo.getOrderNo());

        try {
            // 验证收款信息
            if (!validateCollectionInfo(collectionInfo)) {
                return createErrorResult("收款信息验证失败", "VALIDATION_ERROR", "收款信息不合法");
            }

            // 查询订单信息
            UniversityPracticeOrder order = universityPracticeOrderMapper.selectById(collectionInfo.getOrderId());
            if (order == null) {
                return createErrorResult("订单不存在", "ORDER_NOT_FOUND", "订单ID: " + collectionInfo.getOrderId());
            }

            // 验证订单状态
            if (!canConfirmCollection(order)) {
                return createErrorResult("订单状态不允许确认收款", "INVALID_ORDER_STATUS", 
                    "当前订单状态: " + order.getOrderStatus());
            }

            // 验证收款金额
            if (collectionInfo.getCollectionAmount().compareTo(order.getTotalAmount()) > 0) {
                return createErrorResult("收款金额不能超过订单总金额", "INVALID_AMOUNT", 
                    "收款金额: " + collectionInfo.getCollectionAmount() + ", 订单金额: " + order.getTotalAmount());
            }

            // 更新订单信息
            updateOrderForCollection(order, collectionInfo);

            // 记录操作日志
            recordCollectionLog(order, collectionInfo, "确认收款");

            // 创建成功结果
            CollectionResultDTO result = createSuccessResult(order, collectionInfo);
            result.setOrderStatus("in_progress"); // 订单状态变为执行中
            result.setMessage("收款确认成功");

            log.info("收款确认成功，订单ID: {}, 订单号: {}", order.getId(), order.getOrderNo());
            return result;

        } catch (Exception e) {
            log.error("确认收款失败，订单ID: {}, 订单号: {}", collectionInfo.getOrderId(), collectionInfo.getOrderNo(), e);
            return createErrorResult("确认收款失败", "SYSTEM_ERROR", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CollectionResultDTO updateCollection(CollectionInfoDTO collectionInfo) {
        log.info("开始更新收款信息，订单ID: {}, 订单号: {}", collectionInfo.getOrderId(), collectionInfo.getOrderNo());

        try {
            // 验证收款信息
            if (!validateCollectionInfo(collectionInfo)) {
                return createErrorResult("收款信息验证失败", "VALIDATION_ERROR", "收款信息不合法");
            }

            // 查询订单信息
            UniversityPracticeOrder order = universityPracticeOrderMapper.selectById(collectionInfo.getOrderId());
            if (order == null) {
                return createErrorResult("订单不存在", "ORDER_NOT_FOUND", "订单ID: " + collectionInfo.getOrderId());
            }

            // 验证订单是否已支付
            if (!"paid".equals(order.getPaymentStatus())) {
                return createErrorResult("订单未支付，无法更新收款信息", "ORDER_NOT_PAID", 
                    "当前支付状态: " + order.getPaymentStatus());
            }

            // 验证收款金额
            if (collectionInfo.getCollectionAmount().compareTo(order.getTotalAmount()) > 0) {
                return createErrorResult("收款金额不能超过订单总金额", "INVALID_AMOUNT", 
                    "收款金额: " + collectionInfo.getCollectionAmount() + ", 订单金额: " + order.getTotalAmount());
            }

            // 更新订单收款信息
            updateOrderCollectionInfo(order, collectionInfo);

            // 记录操作日志
            recordCollectionLog(order, collectionInfo, "更新收款信息");

            // 创建成功结果
            CollectionResultDTO result = createSuccessResult(order, collectionInfo);
            result.setMessage("收款信息更新成功");

            log.info("收款信息更新成功，订单ID: {}, 订单号: {}", order.getId(), order.getOrderNo());
            return result;

        } catch (Exception e) {
            log.error("更新收款信息失败，订单ID: {}, 订单号: {}", collectionInfo.getOrderId(), collectionInfo.getOrderNo(), e);
            return createErrorResult("更新收款信息失败", "SYSTEM_ERROR", e.getMessage());
        }
    }

    @Override
    public CollectionResultDTO getCollectionInfo(Long orderId) {
        log.info("开始查询收款信息，订单ID: {}", orderId);

        try {
            // 查询订单信息
            UniversityPracticeOrder order = universityPracticeOrderMapper.selectById(orderId);
            if (order == null) {
                return createErrorResult("订单不存在", "ORDER_NOT_FOUND", "订单ID: " + orderId);
            }

            // 创建收款信息结果
            CollectionResultDTO result = new CollectionResultDTO();
            result.setSuccess(true);
            result.setOrderId(order.getId());
            result.setOrderNo(order.getOrderNo());
            result.setPaymentStatus(order.getPaymentStatus());
            result.setCollectionAmount(order.getCollectionAmount());
            result.setCollectionMethod(order.getCollectionMethod());
            result.setCollectionMethodText(CollectionMethodEnum.getNameByCode(order.getCollectionMethod()));
            result.setCollectionDate(order.getCollectionDate());
            result.setOperatorName(order.getOperatorName());
            result.setCollectionRemark(order.getCollectionRemark());
            result.setUpdateTime(order.getUpdateTime());
            result.setMessage("查询成功");

            log.info("收款信息查询成功，订单ID: {}", orderId);
            return result;

        } catch (Exception e) {
            log.error("查询收款信息失败，订单ID: {}", orderId, e);
            return createErrorResult("查询收款信息失败", "SYSTEM_ERROR", e.getMessage());
        }
    }

    @Override
    public boolean validateCollectionInfo(CollectionInfoDTO collectionInfo) {
        if (collectionInfo == null) {
            return false;
        }

        // 验证必填字段
        if (collectionInfo.getOrderId() == null || 
            !StringUtils.hasText(collectionInfo.getOrderNo()) ||
            !StringUtils.hasText(collectionInfo.getPaymentStatus()) ||
            collectionInfo.getCollectionAmount() == null ||
            !StringUtils.hasText(collectionInfo.getCollectionMethod()) ||
            collectionInfo.getCollectionDate() == null ||
            !StringUtils.hasText(collectionInfo.getOperatorName())) {
            return false;
        }

        // 验证收款金额
        if (collectionInfo.getCollectionAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 验证收款方式
        if (!CollectionMethodEnum.isValidCode(collectionInfo.getCollectionMethod())) {
            return false;
        }

        // 验证支付状态
        if (!collectionInfo.getPaymentStatus().matches("^(pending|paid|refunded|cancelled)$")) {
            return false;
        }

        return true;
    }

    /**
     * 检查订单是否可以确认收款
     */
    private boolean canConfirmCollection(UniversityPracticeOrder order) {
        String orderStatus = order.getOrderStatus();
        String paymentStatus = order.getPaymentStatus();
        
        return ("approved".equals(orderStatus) || "pending_payment".equals(orderStatus)) &&
               ("pending".equals(paymentStatus) || "pending_payment".equals(paymentStatus));
    }

    /**
     * 更新订单收款信息（确认收款）
     */
    private void updateOrderForCollection(UniversityPracticeOrder order, CollectionInfoDTO collectionInfo) {
        // 更新支付状态
        order.setPaymentStatus("paid");
        
        // 更新订单状态
        order.setOrderStatus("in_progress");
        
        // 更新收款信息
        updateOrderCollectionInfo(order, collectionInfo);
        
        // 更新已支付金额
        order.setPaidAmount(collectionInfo.getCollectionAmount());
        
        // 更新结算状态
        order.setSettlementStatus("processing");
        order.setSettlementTime(LocalDateTime.now());
        order.setSettlementMethod(collectionInfo.getCollectionMethod());
        
        // 更新更新人和更新时间
        order.setUpdater(collectionInfo.getOperatorName());
        order.setUpdateTime(LocalDateTime.now());
        
        // 保存到数据库
        universityPracticeOrderMapper.updateById(order);
    }

    /**
     * 更新订单收款信息（更新收款信息）
     */
    private void updateOrderCollectionInfo(UniversityPracticeOrder order, CollectionInfoDTO collectionInfo) {
        // 更新收款信息
        order.setCollectionAmount(collectionInfo.getCollectionAmount());
        order.setCollectionMethod(collectionInfo.getCollectionMethod());
        order.setCollectionDate(collectionInfo.getCollectionDate());
        order.setOperatorName(collectionInfo.getOperatorName());
        order.setCollectionRemark(collectionInfo.getCollectionRemark());
        
        // 更新已支付金额
        order.setPaidAmount(collectionInfo.getCollectionAmount());
        
        // 更新结算方式
        order.setSettlementMethod(collectionInfo.getCollectionMethod());
        
        // 更新更新人和更新时间
        order.setUpdater(collectionInfo.getOperatorName());
        order.setUpdateTime(LocalDateTime.now());
        
        // 保存到数据库
        universityPracticeOrderMapper.updateById(order);
    }

    /**
     * 记录收款操作日志
     */
    private void recordCollectionLog(UniversityPracticeOrder order, CollectionInfoDTO collectionInfo, String action) {
        try {
            Map<String, Object> logData = new HashMap<>();
            logData.put("orderId", order.getId());
            logData.put("orderNo", order.getOrderNo());
            logData.put("action", action);
            logData.put("collectionAmount", collectionInfo.getCollectionAmount());
            logData.put("collectionMethod", collectionInfo.getCollectionMethod());
            logData.put("operatorName", collectionInfo.getOperatorName());
            logData.put("remark", collectionInfo.getCollectionRemark());

            logRecordService.recordLog(
                LogRecordConstants.UNIVERSITY_PRACTICE_ORDER_COLLECTION_CONFIRM,
                action,
                logData,
                collectionInfo.getOperatorName()
            );
        } catch (Exception e) {
            log.warn("记录收款操作日志失败", e);
        }
    }

    /**
     * 创建成功结果
     */
    private CollectionResultDTO createSuccessResult(UniversityPracticeOrder order, CollectionInfoDTO collectionInfo) {
        CollectionResultDTO result = new CollectionResultDTO();
        result.setSuccess(true);
        result.setOrderId(order.getId());
        result.setOrderNo(order.getOrderNo());
        result.setPaymentStatus(order.getPaymentStatus());
        result.setCollectionAmount(collectionInfo.getCollectionAmount());
        result.setCollectionMethod(collectionInfo.getCollectionMethod());
        result.setCollectionMethodText(CollectionMethodEnum.getNameByCode(collectionInfo.getCollectionMethod()));
        result.setCollectionDate(collectionInfo.getCollectionDate());
        result.setOperatorName(collectionInfo.getOperatorName());
        result.setCollectionRemark(collectionInfo.getCollectionRemark());
        result.setUpdateTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建错误结果
     */
    private CollectionResultDTO createErrorResult(String message, String errorCode, String errorDetail) {
        CollectionResultDTO result = new CollectionResultDTO();
        result.setSuccess(false);
        result.setMessage(message);
        result.setErrorCode(errorCode);
        result.setErrorDetail(errorDetail);
        return result;
    }
}

