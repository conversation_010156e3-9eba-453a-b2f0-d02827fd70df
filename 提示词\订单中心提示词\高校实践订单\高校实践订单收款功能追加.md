# 高校实践订单收款信息功能说明

## 功能概述

本功能为高校实践订单提供收款信息管理能力，包括确认收款、更新收款信息、查询收款信息等核心功能。**重要说明：本功能不修改现有数据库表结构，只使用现有的字段。**

## 功能特性

### 1. 收款确认

- 支持确认高校实践订单的收款
- 自动更新订单状态和支付状态
- 记录操作日志，便于审计追踪

### 2. 收款信息更新

- 支持更新已收款订单的收款信息
- 验证收款金额的合理性
- 记录更新操作日志

### 3. 收款信息查询

- 查询订单的收款信息详情
- 显示收款方式的中文名称
- 支持多种收款方式

## 技术实现

### 1. 使用的现有字段

本功能使用以下现有数据库字段，不新增字段：

- `payment_status`: 支付状态
- `paid_amount`: 已支付金额
- `order_status`: 订单状态
- `settlement_status`: 结算状态
- `settlement_time`: 结算时间
- `settlement_method`: 结算方式
- `updater`: 更新人
- `update_time`: 更新时间

### 2. 收款方式映射

收款方式通过枚举类管理，支持以下方式：

| 编码          | 名称      | 说明          |
| ------------- | --------- | ------------- |
| cash          | 现金      | 现金收款      |
| wechat        | 微信支付  | 微信支付收款  |
| alipay        | 支付宝    | 支付宝收款    |
| bank_transfer | 银行转账  | 银行转账收款  |
| pos           | POS机刷卡 | POS机刷卡收款 |
| other         | 其他      | 其他收款方式  |

### 3. 状态流转逻辑

```
订单创建 → 审批通过 → 确认收款 → 订单执行 → 订单完成
   ↓           ↓         ↓         ↓         ↓
  草稿     待支付/已批准  已支付     执行中     已完成
```

## 接口说明

### 1. 确认收款接口

- **路径**: `POST /publicbiz/order/confirm-collection`
- **功能**: 确认订单收款，更新订单状态
- **权限**: 需要"收款"权限

### 2. 更新收款信息接口

- **路径**: `POST /publicbiz/order/update-collection`
- **功能**: 更新已收款订单的收款信息
- **权限**: 需要"收款"权限

### 3. 查询收款信息接口

- **路径**: `GET /publicbiz/order/collection-info`
- **功能**: 查询订单的收款信息详情
- **权限**: 需要"查询"权限

## 业务规则

### 1. 收款确认规则

- 只有"待支付"或"已批准"状态的订单才能确认收款
- 收款金额不能超过订单总金额
- 确认收款后，订单状态变为"执行中"，支付状态变为"已支付"

### 2. 收款信息更新规则

- 只有已支付的订单才能更新收款信息
- 更新收款金额时需要进行合理性验证
- 所有更新操作都会记录操作日志

### 3. 数据验证规则

- 收款金额必须大于0
- 收款方式必须在系统支持的范围内
- 收款日期不能为空
- 操作人信息必须填写

## 使用示例

### 1. 确认收款

```json
POST /publicbiz/order/confirm-collection
{
  "orderId": 1,
  "orderNo": "HP202406001",
  "paymentStatus": "paid",
  "collectionAmount": 580000.00,
  "collectionMethod": "bank_transfer",
  "collectionDate": "2024-06-21",
  "operatorName": "李四",
  "collectionRemark": "银行转账收款，已确认到账"
}
```

### 2. 更新收款信息

```json
POST /publicbiz/order/update-collection
{
  "orderId": 1,
  "orderNo": "HP202406001",
  "paymentStatus": "paid",
  "collectionAmount": 600000.00,
  "collectionMethod": "wechat",
  "collectionDate": "2024-06-21",
  "operatorName": "王五",
  "collectionRemark": "微信支付收款，金额调整"
}
```

### 3. 查询收款信息

```http
GET /publicbiz/order/collection-info?id=1
```

## 注意事项

### 1. 数据库兼容性

- 本功能不修改现有表结构
- 使用现有字段存储收款信息
- 确保与现有系统的兼容性

### 2. 权限控制

- 收款操作需要专门的"收款"权限
- 查询操作需要"查询"权限
- 权限配置需要在系统中进行相应设置

### 3. 日志记录

- 所有收款操作都会记录详细的操作日志
- 日志包含操作人、操作时间、操作内容等信息
- 便于后续的审计和问题排查

### 4. 错误处理

- 系统会进行完善的参数验证
- 提供清晰的错误提示信息
- 支持事务回滚，确保数据一致性
