<!--
  页面名称：收支记录标签页
  功能描述：展示家政服务订单的收支记录
-->
<template>
  <div class="income-expense-tab">
    <div class="section-header">
      <div class="section-title">
        <el-icon><TrendCharts /></el-icon>
        收支记录
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="handleAddRecord">
          <el-icon><Plus /></el-icon>
          新增收支记录
        </el-button>
      </div>
    </div>

    <div class="record-content">
      <div v-if="incomeExpenseRecords.length > 0">
        <el-table :data="incomeExpenseRecords" style="width: 100%" size="small">
          <el-table-column prop="type" label="类型" width="120">
            <template #default="scope">
              <el-tag :type="getRecordTypeTag(scope.row.type)" size="small">
                {{ scope.row.type }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="100">
            <template #default="scope">
              <span :class="getAmountClass(scope.row.type)">¥{{ scope.row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="scope">
              <el-button type="warning" size="small" @click="handleEditRecord(scope.row)">
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="no-records">
        <el-empty description="暂无收支记录" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { TrendCharts, Plus } from '@element-plus/icons-vue'

interface Props {
  orderDetail: any
  records?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  orderDetail: {},
  records: () => []
})

// Emits
const emit = defineEmits<{
  'add-record': []
  'edit-record': [record: any]
  'update-records': [records: any[]]
}>()

// 收支记录数据
const incomeExpenseRecords = ref<any[]>([])

// 监听外部数据变化
watch(
  () => props.records,
  (newRecords) => {
    if (newRecords) {
      incomeExpenseRecords.value = [...newRecords]
    }
  },
  { immediate: true, deep: true }
)

// 添加新记录的方法
const addRecord = (record: any) => {
  const newRecord = {
    ...record,
    id: Date.now() // 生成唯一ID
  }
  console.log('添加新收支记录:', newRecord)
  incomeExpenseRecords.value.push(newRecord)
  emit('update-records', incomeExpenseRecords.value)
}

// 更新记录的方法
const updateRecord = (record: any) => {
  const index = incomeExpenseRecords.value.findIndex((item) => item.id === record.id)
  if (index !== -1) {
    incomeExpenseRecords.value[index] = { ...record }
    emit('update-records', incomeExpenseRecords.value)
  }
}

// 暴露方法给父组件
defineExpose({
  addRecord,
  updateRecord
})

const getRecordTypeTag = (type: string): 'success' | 'danger' | 'primary' | 'info' => {
  const typeMap: Record<string, 'success' | 'danger' | 'primary' | 'info'> = {
    额外收入: 'success',
    额外支出: 'danger',
    服务收入: 'primary'
  }
  return typeMap[type] || 'info'
}

const getAmountClass = (type: string): string => {
  return type.includes('收入') ? 'income-amount' : 'expense-amount'
}

const handleAddRecord = () => {
  emit('add-record')
}

const handleEditRecord = (record: any) => {
  emit('edit-record', record)
}
</script>

<style scoped lang="scss">
.income-expense-tab {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
    }
  }

  .record-content {
    .el-table {
      .el-table__header {
        background-color: #f5f7fa;
      }

      .income-amount {
        color: #67c23a;
        font-weight: 500;
      }

      .expense-amount {
        color: #f56c6c;
        font-weight: 500;
      }
    }

    .no-records {
      padding: 40px 0;
    }
  }
}
</style>
