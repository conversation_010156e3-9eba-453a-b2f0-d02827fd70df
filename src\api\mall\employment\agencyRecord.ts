import request from '@/config/axios'

/**
 * 机构激励/处罚记录管理 API（按接口文档 /publicbiz/agencyRecord/...）
 */

// 分页查询参数
export interface AgencyRecordPageParams {
  pageNum: number
  pageSize: number
  recordType?: string
  startDate?: string
  endDate?: string
  agencyId: number
}

// 机构记录详情
export interface AgencyRecordDetailVO {
  id: number
  recordId: string
  agencyId: number
  recordType: 'incentive' | 'penalty' | 'communication'
  recordDate: string
  title: string
  description: string
  creditImpact: number
  amountImpact: number
  otherImpact?: string
  relatedInfo?: string
  status: string
  followUpDate?: string
  followUpItem?: string
  remarks?: string
  effectiveTime?: string
  recorderName: string
  recorderId: number
  recordTime: string
  creator: string
  createTime: string
  updater: string
  updateTime: string
  attachments?: Attachment[]
}

// 附件信息
export interface Attachment {
  id?: number
  fileName: string
  fileType: string
  fileSize: number
  fileUrl: string
  uploadTime?: string
}

// 跟进记录
export interface FollowUpRecord {
  id: number
  title: string
  description: string
  followUpDate: string
  operatorId: number
  operatorName: string
  createTime: string
}

// 沟通记录
export interface CommunicationRecord {
  id: number
  recordId: string
  recordType: 'communication'
  communicationType: 'call' | 'message' | 'email'
  communicationTitle: string
  communicationContent: string
  participants?: string
  agencyId?: number
  followUpDate?: string
  followUpItem?: string
  attachments?: Attachment[]
  createTime: string
  recorderName: string
}

// 分页返回的通用结构
export interface PageResult<T> {
  records: T[]
  total: number
  pageNum?: number
  pageSize?: number
}

// 分页查询激励/处罚记录
export function getAgencyRecordPage(params: AgencyRecordPageParams) {
  return request.post<PageResult<AgencyRecordDetailVO>>({
    url: '/publicbiz/agencyRecord/page',
    data: params
  })
}

// 创建激励/处罚记录
export function createAgencyRecord(params: Partial<AgencyRecordDetailVO>) {
  return request.post<void>({
    url: '/publicbiz/agencyRecord/create',
    data: params
  })
}

// 更新激励/处罚记录
export function updateAgencyRecord(params: Partial<AgencyRecordDetailVO>) {
  return request.post<void>({
    url: '/publicbiz/agencyRecord/update',
    data: params
  })
}

// 获取记录详情
export function getAgencyRecordDetail(id: number) {
  return request.get<AgencyRecordDetailVO>({
    url: `/publicbiz/agencyRecord/detail/${id}`
  })
}

// 获取跟进记录列表
export function getAgencyRecordFollowUpList(recordId: number) {
  return request.get<FollowUpRecord[]>({
    url: `/publicbiz/agencyRecord/followUpList/${recordId}`
  })
}

// 新增跟进记录
export function createAgencyRecordFollowUp(params: Partial<FollowUpRecord>) {
  return request.post<void>({
    url: '/publicbiz/agencyRecord/followUp/create',
    data: params
  })
}

// 新增沟通日志记录
export function createAgencyRecordCommunication(params: Partial<CommunicationRecord>) {
  return request.post<void>({
    url: '/publicbiz/agencyRecord/communicationcreate',
    data: params
  })
}

// 兼容旧方法名（如代码中曾经引用）
export const agencyRecordApi = {
  getPage: getAgencyRecordPage,
  create: createAgencyRecord,
  update: updateAgencyRecord,
  getDetail: getAgencyRecordDetail,
  getFollowUpList: getAgencyRecordFollowUpList,
  createFollowUp: createAgencyRecordFollowUp,
  createCommunication: createAgencyRecordCommunication
}
