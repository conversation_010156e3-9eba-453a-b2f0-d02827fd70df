<!--
  页面名称：服务评价标签页
  功能描述：展示家政服务订单的服务评价
-->
<template>
  <div class="service-evaluation-tab">
    <div class="section-header">
      <div class="section-title">
        <el-icon><Star /></el-icon>
        服务评价
      </div>
      <div class="header-actions" v-if="serviceEvaluation">
        <el-button type="primary" size="small" @click="handleEditEvaluation">
          <el-icon><Edit /></el-icon>
          编辑评价
        </el-button>
      </div>
    </div>

    <div class="evaluation-content">
      <div v-if="serviceEvaluation" class="evaluation-detail">
        <!-- 服务评分 -->
        <div class="rating-section">
          <h4 class="rating-title">总体满意度</h4>
          <div class="rating-display">
            <el-rate v-model="serviceEvaluation.overallRating" disabled />
            <span class="rating-text">{{ serviceEvaluation.overallRating }}.0分</span>
          </div>
        </div>

        <!-- 评价标签 -->
        <div class="tags-section">
          <h4 class="tags-title">评价标签</h4>
          <div class="tags-display">
            <el-tag
              v-for="tag in serviceEvaluation.tags"
              :key="tag"
              type="success"
              size="small"
              class="evaluation-tag"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>

        <!-- 详细评价 -->
        <div class="comment-section">
          <h4 class="comment-title">详细评价</h4>
          <div class="comment-display">
            <div class="quote-mark">"</div>
            <div class="comment-text">{{ serviceEvaluation.comment }}</div>
          </div>
        </div>

        <!-- 评价信息 -->
        <div class="evaluation-info">
          <span>评价时间：{{ serviceEvaluation.evaluationTime }}</span>
        </div>
      </div>

      <div v-else class="no-evaluation">
        <el-empty description="客户暂未评价" />
        <div class="add-evaluation">
          <el-button type="primary" @click="handleAddEvaluation">
            <el-icon><Plus /></el-icon>
            添加评价
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Star, Edit, Plus } from '@element-plus/icons-vue'

interface Props {
  orderDetail: any
  evaluationData?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderDetail: {},
  evaluationData: null
})

// Emits
const emit = defineEmits<{
  'add-evaluation': []
  'edit-evaluation': [evaluation: any]
}>()

// 服务评价数据
const serviceEvaluation = ref<any>({
  overallRating: 5,
  tags: ['非常专业', '准时到达', '清洁彻底'],
  comment: '非常满意的一次服务!团队很专业,干活利索,边边角角都处理得很干净,下次有需要还会再来。',
  evaluationTime: '2024-06-15 16:30:00'
})

// 监听外部数据变化
watch(
  () => props.evaluationData,
  (newData) => {
    if (newData) {
      serviceEvaluation.value = { ...newData }
    }
  },
  { immediate: true, deep: true }
)

const handleAddEvaluation = () => {
  emit('add-evaluation')
}

const handleEditEvaluation = () => {
  emit('edit-evaluation', serviceEvaluation.value)
}
</script>

<style scoped lang="scss">
.service-evaluation-tab {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
    }
  }

  .evaluation-content {
    .evaluation-detail {
      .rating-section {
        margin-bottom: 24px;

        .rating-title {
          font-size: 14px;
          font-weight: 500;
          color: #606266;
          margin-bottom: 12px;
        }

        .rating-display {
          display: flex;
          align-items: center;
          gap: 12px;

          .rating-text {
            font-size: 16px;
            font-weight: 500;
            color: #f56c6c;
          }
        }
      }

      .tags-section {
        margin-bottom: 24px;

        .tags-title {
          font-size: 14px;
          font-weight: 500;
          color: #606266;
          margin-bottom: 12px;
        }

        .tags-display {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .evaluation-tag {
            background-color: #f0f9ff;
            border-color: #67c23a;
            color: #67c23a;
          }
        }
      }

      .comment-section {
        margin-bottom: 20px;

        .comment-title {
          font-size: 14px;
          font-weight: 500;
          color: #606266;
          margin-bottom: 12px;
        }

        .comment-display {
          position: relative;
          padding: 16px;
          background: #f8f9fa;
          border-radius: 8px;
          border-left: 4px solid #409eff;

          .quote-mark {
            position: absolute;
            top: -8px;
            left: 12px;
            font-size: 48px;
            color: #dcdfe6;
            font-family: serif;
            line-height: 1;
          }

          .comment-text {
            color: #303133;
            line-height: 1.6;
            font-size: 14px;
            padding-left: 8px;
          }
        }
      }

      .evaluation-info {
        color: #909399;
        font-size: 12px;
        padding-top: 12px;
        border-top: 1px solid #f0f0f0;
      }
    }

    .no-evaluation {
      padding: 40px 0;
      text-align: center;

      .add-evaluation {
        margin-top: 20px;
      }
    }
  }
}
</style>
