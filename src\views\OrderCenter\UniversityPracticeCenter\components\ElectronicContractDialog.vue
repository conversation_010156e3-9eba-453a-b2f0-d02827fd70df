<!--
  页面名称：发起电子合同弹窗
  功能描述：发起电子合同，支持选择合同模板、填写合同信息、预览合同等
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="发起电子合同"
    width="800px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="electronic-contract-container">
      <!-- 合同基本信息 -->
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同模板" prop="templateId">
              <el-select v-model="form.templateId" placeholder="请选择合同模板" style="width: 100%">
                <el-option
                  v-for="template in contractTemplates"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同类型" prop="contractType">
              <el-select
                v-model="form.contractType"
                placeholder="请选择合同类型"
                style="width: 100%"
              >
                <el-option label="电子合同" value="electronic" />
                <el-option label="纸质合同" value="paper" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同名称" prop="contractName">
              <el-input v-model="form.contractName" placeholder="请输入合同名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同编号" prop="contractNo">
              <el-input v-model="form.contractNo" placeholder="系统自动生成" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="签署日期" prop="signDate">
              <el-date-picker
                v-model="form.signDate"
                type="date"
                placeholder="选择签署日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-date-picker
                v-model="form.effectiveDate"
                type="date"
                placeholder="选择生效日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="合同备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入合同备注信息"
          />
        </el-form-item>
      </el-form>

      <!-- 合同预览 -->
      <div class="contract-preview" v-if="selectedTemplate">
        <div class="preview-header">
          <h4>合同预览</h4>
          <el-button size="small" type="primary" @click="previewContract">预览合同</el-button>
        </div>
        <div class="preview-content">
          <div class="template-info">
            <p><strong>模板名称：</strong>{{ selectedTemplate.name }}</p>
            <p><strong>模板描述：</strong>{{ selectedTemplate.description }}</p>
            <p><strong>适用场景：</strong>{{ selectedTemplate.scenario }}</p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          发起电子合同
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { UniversityPracticeOrder } from '@/api/OrderCenter/UniversityPracticeCenter'

// Props
interface Props {
  visible: boolean
  orderData?: UniversityPracticeOrder
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)

// 表单数据
const form = ref({
  templateId: '',
  contractType: 'electronic',
  contractName: '',
  contractNo: '',
  signDate: '',
  effectiveDate: '',
  remark: ''
})

// 表单校验规则
const rules = {
  templateId: [{ required: true, message: '请选择合同模板', trigger: 'change' }],
  contractName: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
  signDate: [{ required: true, message: '请选择签署日期', trigger: 'change' }],
  effectiveDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }]
}

// 合同模板列表
const contractTemplates = ref([
  {
    id: 'template_001',
    name: '高校实践项目标准合同模板',
    description: '适用于高校与企业合作的实践项目',
    scenario: '高校实践、校企合作'
  },
  {
    id: 'template_002',
    name: '短期培训项目合同模板',
    description: '适用于短期培训和实践项目',
    scenario: '短期培训、技能提升'
  },
  {
    id: 'template_003',
    name: '长期合作项目合同模板',
    description: '适用于长期校企合作项目',
    scenario: '长期合作、深度合作'
  }
])

// 选中的模板
const selectedTemplate = computed(() => {
  return contractTemplates.value.find((t) => t.id === form.value.templateId)
})

// 生成合同编号
const generateContractNo = () => {
  const date = new Date()
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, '0')
  return `HT-${year}${month}${day}-${random}`
}

// 初始化表单数据
const initFormData = () => {
  if (props.orderData) {
    form.value.contractName = `${props.orderData.projectName}合同`
    form.value.contractNo = generateContractNo()
    form.value.signDate = new Date().toISOString().split('T')[0]
    form.value.effectiveDate = props.orderData.startDate
  }
}

// 预览合同
const previewContract = () => {
  if (!selectedTemplate.value) {
    ElMessage.warning('请先选择合同模板')
    return
  }

  // TODO: 实现合同预览功能
  ElMessage.info('合同预览功能开发中...')
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    loading.value = true

    // TODO: 调用发起电子合同接口
    await new Promise((resolve) => setTimeout(resolve, 1000))

    ElMessage.success('电子合同发起成功')
    emit('success', {
      contractNo: form.value.contractNo,
      contractName: form.value.contractName
    })
    handleCancel()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
  // 重置表单
  form.value = {
    templateId: '',
    contractType: 'electronic',
    contractName: '',
    contractNo: '',
    signDate: '',
    effectiveDate: '',
    remark: ''
  }
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      initFormData()
    }
  }
)
</script>

<style scoped lang="scss">
.electronic-contract-container {
  .contract-preview {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        color: #303133;
      }
    }

    .preview-content {
      .template-info {
        p {
          margin: 8px 0;
          color: #606266;
          line-height: 1.5;

          strong {
            color: #303133;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
