import request from '@/config/axios'

/**
 * 阿姨管理API接口
 */

// 阿姨信息接口
export interface Practitioner {
  id: number
  name: string
  phone: string
  idCard: string
  hometown?: string
  age?: number
  gender?: string
  avatar?: string
  serviceType: string
  experienceYears: number
  platformStatus: string
  rating: number
  agencyId?: number
  agencyName?: string
  status?: string
  currentStatus?: string
  currentOrderId?: string
  totalOrders: number
  totalIncome: number
  customerSatisfaction?: number
  createTime?: string
  updateTime?: string
}

// 阿姨资质文件接口
export interface PractitionerQualification {
  id?: number
  practitionerId?: number
  fileType: string
  fileName: string
  fileUrl: string
  fileSize?: number
  fileExtension?: string
  sortOrder?: number
  status?: number
  createTime?: string
  updateTime?: string
}

// 阿姨服务记录接口
export interface PractitionerServiceRecord {
  id?: number
  practitionerId?: number
  orderId: string
  customerId?: number
  customerName?: string
  serviceType: string
  serviceStartTime?: string
  serviceEndTime?: string
  serviceDuration?: string
  serviceAddress?: string
  serviceAmount: number
  practitionerIncome?: number
  platformIncome?: number
  orderStatus: string
  customerRating?: number
  customerComment?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 阿姨评级记录接口
export interface PractitionerRatingRecord {
  id?: number
  practitionerId?: number
  ratingType: string
  oldRating?: number
  newRating: number
  ratingChange?: number
  ratingReason?: string
  evaluatorId?: number
  evaluatorName?: string
  evaluatorType?: string
  relatedOrderId?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 阿姨详情响应接口
export interface PractitionerDetail extends Practitioner {
  qualifications?: PractitionerQualification[]
  recentServiceRecords?: PractitionerServiceRecord[]
  ratingRecords?: PractitionerRatingRecord[]
}

// 查询参数接口
export interface PractitionerQueryParams {
  pageNo: number
  pageSize: number
  keyword?: string
  serviceType?: string
  platformStatus?: string
  rating?: string
  agencyId?: string
}

// 新增阿姨参数接口
export interface CreatePractitionerParams {
  name: string
  phone: string
  idCard: string
  hometown?: string
  age?: number
  gender?: string
  avatar?: string
  serviceType: string
  experienceYears: number
  platformStatus?: string
  rating: number
  agencyId?: number
  agencyName?: string
  status?: string
  currentStatus?: string
  currentOrderId?: string
  totalOrders?: number
  totalIncome?: number
  customerSatisfaction?: number
  qualifications?: PractitionerQualification[]
}

// 更新阿姨参数接口
export interface UpdatePractitionerParams extends CreatePractitionerParams {
  id: number
}

// 阿姨状态更新参数接口
export interface PractitionerStatusUpdateParams {
  id: number
  platformStatus: string
  reason?: string
}

// 阿姨评级更新参数接口
export interface PractitionerRatingUpdateParams {
  id: number
  newRating: number
  ratingReason?: string
  ratingType?: string
}

// 分页响应接口
export interface PageResult<T> {
  list: T[]
  total: number
}

/**
 * 获取阿姨分页列表
 * @param params 查询参数
 * @returns Promise<PageResult<Practitioner>>
 */
export function getPractitionerPage(params: PractitionerQueryParams) {
  return request.get({
    url: '/publicbiz/employment/practitioner/page',
    params
  })
}

/**
 * 获取阿姨详情
 * @param id 阿姨ID
 * @returns Promise<PractitionerDetail>
 */
export function getPractitionerDetail(id: number) {
  return request.get({
    url: `/publicbiz/employment/practitioner/${id}`
  })
}

/**
 * 新增阿姨
 * @param data 阿姨信息
 * @returns Promise<number> 返回阿姨ID
 */
export function createPractitioner(data: CreatePractitionerParams) {
  return request.post({
    url: '/publicbiz/employment/practitioner/create',
    data
  })
}

/**
 * 更新阿姨信息
 * @param data 更新信息
 * @returns Promise<boolean>
 */
export function updatePractitioner(data: UpdatePractitionerParams) {
  return request.put({
    url: '/publicbiz/employment/practitioner/update',
    data
  })
}

/**
 * 删除阿姨（软删除）
 * @param id 阿姨ID
 * @returns Promise<boolean>
 */
export function deletePractitioner(id: number) {
  return request.delete({
    url: `/publicbiz/employment/practitioner/${id}`
  })
}

/**
 * 更新阿姨平台状态
 * @param id 阿姨ID
 * @param data 状态更新参数
 * @returns Promise<boolean>
 */
export function updatePractitionerStatus(id: number, data: PractitionerStatusUpdateParams) {
  return request.put({
    url: `/publicbiz/employment/practitioner/${id}/status`,
    data
  })
}

/**
 * 更新阿姨评级
 * @param id 阿姨ID
 * @param data 评级更新参数
 * @returns Promise<boolean>
 */
export function updatePractitionerRating(id: number, data: PractitionerRatingUpdateParams) {
  return request.put({
    url: `/publicbiz/employment/practitioner/${id}/rating`,
    data
  })
}

/**
 * 导出阿姨列表Excel
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export function exportPractitionerExcel(
  params: Omit<PractitionerQueryParams, 'pageNo' | 'pageSize'>
) {
  return request.download({
    url: '/publicbiz/employment/practitioner/export-excel',
    params
  })
}

/**
 * 生成阿姨简历海报
 * @param id 阿姨ID
 * @returns Promise<{ url: string }>
 */
export function generatePractitionerResume(id: number) {
  return request.post({
    url: `/publicbiz/employment/practitioner/${id}/resume-poster`
  })
}
