<!--
  页面名称：售后工单表单
  功能描述：新建家政服务订单的售后工单
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="isEdit ? '编辑售后工单' : '新建售后工单'"
    direction="rtl"
    size="50%"
    :before-close="handleClose"
  >
    <div class="form-container">
      <div class="form-section">
        <h3 class="section-title">售后信息</h3>

        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
          <el-form-item label="工单类型" prop="workOrderType" required>
            <el-select
              v-model="form.workOrderType"
              placeholder="请选择工单类型"
              style="width: 100%"
            >
              <el-option label="物品损坏" value="物品损坏" />
              <el-option label="服务不满意" value="服务不满意" />
              <el-option label="人员问题" value="人员问题" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>

          <el-form-item label="问题描述" prop="problemDescription" required>
            <el-input
              v-model="form.problemDescription"
              type="textarea"
              :rows="4"
              placeholder="请详细描述问题"
            />
          </el-form-item>

          <el-form-item label="工单状态" prop="workOrderStatus" required>
            <el-select
              v-model="form.workOrderStatus"
              placeholder="请选择工单状态"
              style="width: 100%"
            >
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>

          <el-form-item label="处理结果" prop="processingResult">
            <el-input
              v-model="form.processingResult"
              type="textarea"
              :rows="4"
              placeholder="请输入处理结果"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  visible: boolean
  orderId?: string
  recordData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: '',
  recordData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data?: any]
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()
const isEdit = ref(false)

// 表单数据
const form = reactive({
  workOrderType: '物品损坏',
  problemDescription: '',
  workOrderStatus: 'pending',
  processingResult: ''
})

// 表单校验规则
const rules: FormRules = {
  workOrderType: [{ required: true, message: '请选择工单类型', trigger: 'change' }],
  problemDescription: [{ required: true, message: '请输入问题描述', trigger: 'blur' }],
  workOrderStatus: [{ required: true, message: '请选择工单状态', trigger: 'change' }]
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.recordData) {
        // 编辑模式
        isEdit.value = true
        Object.assign(form, props.recordData)
      } else {
        // 新增模式
        isEdit.value = false
        resetForm()
      }
    }
  }
)

// 监听recordData变化
watch(
  () => props.recordData,
  (newVal) => {
    if (newVal && props.visible) {
      isEdit.value = true
      Object.assign(form, newVal)
    }
  }
)

const resetForm = () => {
  form.workOrderType = '物品损坏'
  form.problemDescription = ''
  form.workOrderStatus = 'pending'
  form.processingResult = ''
  formRef.value?.clearValidate()
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
  isEdit.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 构建提交的数据
    const submitData = {
      workOrderType: form.workOrderType,
      problemDescription: form.problemDescription,
      workOrderStatus: form.workOrderStatus,
      processingResult: form.processingResult
    }

    console.log('售后记录表单提交数据:', submitData)
    ElMessage.success(isEdit.value ? '售后工单更新成功' : '售后工单创建成功')
    emit('success', submitData)
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.form-container {
  padding: 20px;

  .form-section {
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
}

.drawer-footer {
  text-align: right;
}
</style>
