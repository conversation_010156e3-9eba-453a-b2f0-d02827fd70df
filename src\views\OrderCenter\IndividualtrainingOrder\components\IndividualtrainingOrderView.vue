<!--
  页面名称：个人培训与认证订单详情
  功能描述：查看个人培训与认证订单详情，支持数据展示、操作日志查看
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="个人培训订单详情"
    direction="rtl"
    size="600px"
    :before-close="handleClose"
    :append-to-body="true"
    :modal="true"
    :z-index="1000"
  >
    <div class="drawer-content">
      <!-- 课程标题横幅 -->
      <div class="course-banner">
        <div class="course-title">{{ orderData?.courseName || '项目管理PMP认证课程' }}</div>
        <el-tag type="primary" size="small">{{ getOrderTypeText(orderData?.orderType) }}</el-tag>
      </div>

      <!-- 订单基本信息 -->
      <div class="info-section">
        <div class="info-item">
          <span class="label">订单号：</span>
          <span class="value">{{ orderData?.orderNumber || 'PT202406001' }}</span>
        </div>
        <div class="info-item">
          <span class="label">学员姓名：</span>
          <span class="value">{{ orderData?.studentName || '王小明' }}</span>
        </div>
        <div class="info-item">
          <span class="label">课程/考试项目：</span>
          <span class="value">{{ orderData?.courseName || '项目管理PMP认证课程' }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单来源：</span>
          <span class="value">{{ orderData?.orderSource || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单金额：</span>
          <span class="value amount">¥{{ orderData?.orderAmount || '4,500' }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单状态：</span>
          <el-tag :type="getOrderStatusType(orderData?.orderStatus)" size="small">
            {{ getOrderStatusText(orderData?.orderStatus) }}
          </el-tag>
        </div>
        <div class="info-item">
          <span class="label">支付状态：</span>
          <el-tag :type="getPaymentStatusType(orderData?.paymentStatus)" size="small">
            {{ getPaymentStatusText(orderData?.paymentStatus) }}
          </el-tag>
        </div>
        <div class="info-item">
          <span class="label">学习/考试状态：</span>
          <el-tag :type="getLearningStatusType(orderData?.learningStatus)" size="small">
            {{ getLearningStatusText(orderData?.learningStatus) }}
          </el-tag>
        </div>
        <div class="info-item">
          <span class="label">报名时间：</span>
          <span class="value">{{ orderData?.registrationTime || '2024-06-10' }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联商机：</span>
          <span class="value">{{ orderData?.businessOpportunity || 'OPP202406009' }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联线索：</span>
          <span class="value">{{ orderData?.lead || 'LEAD202406009' }}</span>
        </div>
      </div>

      <!-- 收款信息模块 -->
      <div
        v-if="orderData?.orderStatus !== 'pending_payment' || paymentInfo.actualAmount"
        class="detail-module payment-info"
      >
        <div class="module-header">
          <el-icon><Money /></el-icon>
          <span class="module-title">收款信息</span>
        </div>
        <div class="module-content">
          <div class="info-item">
            <span class="label">收款金额：</span>
            <span class="value amount"
              >¥{{ paymentInfo.actualAmount || orderData?.orderAmount || '0' }}</span
            >
          </div>
          <div class="info-item">
            <span class="label">收款方式：</span>
            <span class="value">{{ getPaymentMethodText(paymentInfo.paymentMethod) || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">收款日期：</span>
            <span class="value">{{ paymentInfo.paymentDate || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">操作人：</span>
            <span class="value">{{ paymentInfo.operator || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">收款备注：</span>
            <span class="value">{{ paymentInfo.paymentNotes || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 电子协议模块 -->
      <div class="detail-module">
        <div class="module-header">
          <span class="module-title">电子协议</span>
        </div>
        <div class="module-content">
          <div class="agreement-item">
            <div class="agreement-info">
              <el-icon><Document /></el-icon>
              <span class="agreement-name">个人培训/认证协议</span>
              <el-tag type="info" size="small">未签署</el-tag>
            </div>
            <el-button type="primary" size="small">发起签约</el-button>
          </div>
        </div>
      </div>

      <!-- 审批流程模块 -->
      <div class="detail-module">
        <div class="module-header">
          <el-icon><Tools /></el-icon>
          <span class="module-title">审批流程</span>
        </div>
        <div class="module-content">
          <div class="no-data">无审批记录</div>
          <!-- 当订单状态为待付款且未收款时显示确认收款按钮 -->
          <div
            v-if="orderData?.orderStatus === 'pending_payment' && !paymentInfo.actualAmount"
            class="approval-actions"
          >
            <el-button type="success" @click="onConfirmPayment">
              <el-icon><Money /></el-icon>
              确认收款
            </el-button>
          </div>
        </div>
      </div>

      <!-- 操作日志模块 -->
      <div class="detail-module">
        <div class="module-header">
          <el-icon><Refresh /></el-icon>
          <span class="module-title">操作日志</span>
          <el-button type="text" size="small" class="view-log-btn" @click="onViewOptLog">
            <el-icon><List /></el-icon>
            查看完整日志
          </el-button>
        </div>
        <div class="module-content">
          <div class="log-list">
            <div class="log-item">
              <div class="log-dot"></div>
              <div class="log-content">
                <div class="log-time">2024/6/10 09:15:00</div>
                <div class="log-message">系统自动 学员通过小程序报名</div>
              </div>
            </div>
            <div class="log-item">
              <div class="log-dot"></div>
              <div class="log-content">
                <div class="log-time">2024/6/10 14:30:00</div>
                <div class="log-message">李美丽(客服)确认支付完成</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 操作日志抽屉 -->
  <OptLog v-model:visible="optLogVisible" :order-id="orderData?.id" />

  <!-- 收款确认对话框 -->
  <PaymentConfirmDialog
    v-model:visible="paymentConfirmVisible"
    :order-data="orderData"
    @confirm="onPaymentConfirm"
  />
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Money, Document, Tools, Refresh, List } from '@element-plus/icons-vue'
import OptLog from './OptLog.vue'
import PaymentConfirmDialog from './PaymentConfirmDialog.vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  edit: [data: any]
  'payment-confirmed': [paymentData: any]
  'order-status-updated': [orderData: any]
}>()

// 响应式数据
/** 操作日志抽屉显示状态 */
const optLogVisible = ref(false)
/** 收款确认对话框显示状态 */
const paymentConfirmVisible = ref(false)
/** 收款信息数据 */
const paymentInfo = ref({
  actualAmount: '',
  paymentMethod: '',
  paymentDate: '',
  operator: '',
  paymentNotes: ''
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
/** 处理关闭 */
const handleClose = () => {
  visible.value = false
}

/** 处理编辑 */
const handleEdit = () => {
  emit('edit', props.orderData)
  visible.value = false
}

/** 查看操作日志 */
const onViewOptLog = () => {
  optLogVisible.value = true
}

/** 确认收款 */
const onConfirmPayment = () => {
  paymentConfirmVisible.value = true
}

/** 处理收款确认 */
const onPaymentConfirm = async (paymentData: any) => {
  try {
    console.log('收款确认数据:', paymentData)

    // 更新收款信息
    paymentInfo.value = {
      actualAmount: paymentData.actualAmount?.toString() || '',
      paymentMethod: paymentData.paymentMethod || '',
      paymentDate: paymentData.paymentDate || '',
      operator: '当前用户', // TODO: 从用户信息中获取
      paymentNotes: paymentData.paymentNotes || ''
    }

    // 更新订单状态（如果订单状态为待支付，则更新为已支付）
    if (props.orderData?.orderStatus === 'pending_payment') {
      // 创建更新后的订单数据
      const updatedOrderData = {
        ...props.orderData,
        orderStatus: 'in_progress',
        paymentStatus: 'paid',
        learningStatus: 'learning'
      }

      // 通知父组件更新订单状态
      emit('order-status-updated', updatedOrderData)
    }

    // 通知父组件收款确认
    emit('payment-confirmed', paymentData)

    // 显示成功提示
    ElMessage.success('收款确认成功！订单状态已更新为"执行中"')

    // TODO: 调用收款确认接口
    // 这里可以调用后端接口进行收款确认
    // 成功后可以更新订单状态
    // const response = await confirmPayment({
    //   orderId: props.orderData?.id,
    //   actualAmount: paymentData.actualAmount,
    //   paymentMethod: paymentData.paymentMethod,
    //   paymentDate: paymentData.paymentDate,
    //   paymentNotes: paymentData.paymentNotes
    // })
  } catch (error) {
    console.error('收款确认失败:', error)
    ElMessage.error('收款确认失败，请重试')
  }
}

/** 获取收款方式文本 */
const getPaymentMethodText = (method: string) => {
  const methodMap = {
    bank_transfer: '银行转账',
    alipay: '支付宝',
    wechat_pay: '微信支付',
    cash: '现金',
    check: '支票',
    other: '其他'
  }
  return methodMap[method] || method
}

/** 获取订单类型文本 */
const getOrderTypeText = (type: string) => {
  const typeMap = {
    training: '个人培训',
    certification: '考试认证'
  }
  return typeMap[type] || '个人培训'
}

/** 获取订单状态类型 */
const getOrderStatusType = (status: string) => {
  const statusMap = {
    pending_payment: 'warning',
    learning: 'primary',
    pending_exam: 'info',
    completed: 'success',
    passed: 'success',
    closed: 'danger'
  }
  return statusMap[status] || 'primary'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap = {
    pending_payment: '待支付',
    learning: '学习中',
    pending_exam: '待考试',
    completed: '已完成',
    passed: '已通过',
    closed: '已关闭'
  }
  return statusMap[status] || '学习中'
}

/** 获取支付状态类型 */
const getPaymentStatusType = (status: string) => {
  const statusMap = {
    pending: 'warning',
    paid: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'success'
}

/** 获取支付状态文本 */
const getPaymentStatusText = (status: string) => {
  const statusMap = {
    pending: '待支付',
    paid: '已支付',
    cancelled: '已取消'
  }
  return statusMap[status] || '已支付'
}

/** 获取学习状态类型 */
const getLearningStatusType = (status: string) => {
  const statusMap = {
    learning: 'primary',
    pending_exam: 'warning',
    pending_confirmation: 'info',
    passed: 'success',
    completed: 'success'
  }
  return statusMap[status] || 'primary'
}

/** 获取学习状态文本 */
const getLearningStatusText = (status: string) => {
  const statusMap = {
    learning: '学习中',
    pending_exam: '待考试',
    pending_confirmation: '待确认',
    passed: '已通过',
    completed: '已完成'
  }
  return statusMap[status] || '学习中'
}
</script>

<style scoped lang="scss">
.drawer-content {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;

  .course-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;

    .course-title {
      font-size: 18px;
      font-weight: 600;
    }
  }

  .info-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #666;
        font-size: 14px;
        min-width: 100px;
        flex-shrink: 0;
      }

      .value {
        color: #333;
        font-size: 14px;
        flex: 1;

        &.amount {
          color: #67c23a;
          font-weight: 600;
        }
      }
    }
  }

  .detail-module {
    background: white;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &.payment-info {
      border: 1px solid #e1f3d8;
      background-color: #f0f9ff;
    }

    .module-header {
      padding: 16px 20px;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      align-items: center;
      gap: 8px;

      .module-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        flex: 1;
      }

      .view-log-btn {
        color: #409eff;
      }
    }

    .module-content {
      padding: 20px;

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
          font-size: 14px;
          flex: 1;

          &.amount {
            color: #67c23a;
            font-weight: 600;
          }
        }
      }

      .agreement-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background: #f5f7fa;
        border-radius: 6px;

        .agreement-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .agreement-name {
            color: #333;
            font-size: 14px;
          }
        }
      }

      .no-data {
        color: #909399;
        font-size: 14px;
        text-align: center;
        padding: 20px 0;
      }

      .approval-actions {
        margin-top: 16px;
        text-align: center;
        padding-top: 16px;
        border-top: 1px solid #e4e7ed;
      }

      .log-list {
        .log-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .log-dot {
            width: 8px;
            height: 8px;
            background: #409eff;
            border-radius: 50%;
            margin-top: 6px;
            margin-right: 12px;
            flex-shrink: 0;
          }

          .log-content {
            flex: 1;

            .log-time {
              color: #666;
              font-size: 12px;
              margin-bottom: 4px;
            }

            .log-message {
              color: #333;
              font-size: 14px;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fff;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 20px 20px 0;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-drawer__body) {
  padding: 0;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

:deep(.el-drawer) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
}

:deep(.el-overlay) {
  background-color: rgba(0, 0, 0, 0.3);
}
</style>
