import { defineStore } from 'pinia'
import { store } from '@/store'
import {
  CertificateTemplateVO,
  CertificateField,
  CertificateFieldForApi,
  CreateCertificateTemplateParams,
  UpdateCertificateTemplateParams,
  CertificateTemplateApi
} from '@/api/infra/certificateTemplate'
import { ElMessage } from 'element-plus'
import { generateCertificateHtml, validateHtmlTemplate } from '@/utils/htmlSanitizer'

interface CertificateTemplateState {
  // 当前编辑的模板
  currentTemplate: CertificateTemplateVO | null
  // 模板列表
  templateList: CertificateTemplateVO[]
  // 最近保存的模板ID
  lastSavedTemplateId: number | null
  // 加载状态
  loading: boolean
}

export const useCertificateTemplateStore = defineStore('certificateTemplate', {
  state: (): CertificateTemplateState => ({
    currentTemplate: null,
    templateList: [],
    lastSavedTemplateId: null,
    loading: false
  }),

  getters: {
    // 获取当前模板
    getCurrentTemplate(): CertificateTemplateVO | null {
      return this.currentTemplate
    },

    // 根据ID获取模板
    getTemplateById:
      (state) =>
      (id: number): CertificateTemplateVO | null => {
        return state.templateList.find((template) => template.id === id) || null
      },

    // 获取最近保存的模板
    getLastSavedTemplate(): CertificateTemplateVO | null {
      if (!this.lastSavedTemplateId) return null
      return this.getTemplateById(this.lastSavedTemplateId)
    },

    // 获取模板列表
    getTemplateList(): CertificateTemplateVO[] {
      return this.templateList
    }
  },

  actions: {
    // 设置当前编辑的模板
    setCurrentTemplate(template: CertificateTemplateVO | null) {
      this.currentTemplate = template
    },

    // 创建新模板
    async createTemplate(templateData: CreateCertificateTemplateParams): Promise<number | null> {
      this.loading = true
      try {
        // 生成HTML模板内容
        let htmlContent = templateData.htmlContent
        if (!htmlContent && templateData.fields && templateData.fields.length > 0) {
          // 转换字段格式用于生成HTML
          const fieldsForHtml = templateData.fields.map((field) => ({
            id: field.fieldId,
            type: field.fieldType || field.type || '',
            label: field.fieldLabel || field.label || '',
            x: field.positionX !== undefined ? field.positionX : field.x || 0,
            y: field.positionY !== undefined ? field.positionY : field.y || 0,
            fontSize: field.fontSize,
            color: field.fontColor || field.color || '#333',
            fontFamily: field.fontFamily
          }))
          htmlContent = generateCertificateHtml(fieldsForHtml, templateData.backgroundUrl)
        }

        // 验证HTML模板安全性
        if (htmlContent && !validateHtmlTemplate(htmlContent)) {
          ElMessage.error('HTML模板包含不安全的内容，请检查后重试')
          return null
        }

        // 调用真实的API
        const result = await CertificateTemplateApi.create({
          ...templateData,
          htmlContent
        })

        // 设置最近保存的模板ID
        this.lastSavedTemplateId = result.id

        ElMessage.success('证书模板保存成功')
        return result.id
      } catch (error) {
        console.error('创建证书模板失败:', error)
        ElMessage.error('保存失败，请重试')
        return null
      } finally {
        this.loading = false
      }
    },

    // 更新模板
    async updateTemplate(templateData: UpdateCertificateTemplateParams): Promise<boolean> {
      this.loading = true
      try {
        // 生成HTML模板内容
        let htmlContent = templateData.htmlContent
        if (!htmlContent && templateData.fields && templateData.fields.length > 0) {
          // 转换字段格式用于生成HTML
          const fieldsForHtml = templateData.fields.map((field) => ({
            id: field.fieldId,
            type: field.fieldType || field.type || '',
            label: field.fieldLabel || field.label || '',
            x: field.positionX !== undefined ? field.positionX : field.x || 0,
            y: field.positionY !== undefined ? field.positionY : field.y || 0,
            fontSize: field.fontSize,
            color: field.fontColor || field.color || '#333',
            fontFamily: field.fontFamily
          }))
          htmlContent = generateCertificateHtml(fieldsForHtml, templateData.backgroundUrl)
        }

        // 验证HTML模板安全性
        if (htmlContent && !validateHtmlTemplate(htmlContent)) {
          ElMessage.error('HTML模板包含不安全的内容，请检查后重试')
          return false
        }

        // 调用真实的API
        await CertificateTemplateApi.update({
          ...templateData,
          htmlContent
        })

        // 设置最近保存的模板ID
        this.lastSavedTemplateId = templateData.id

        ElMessage.success('证书模板更新成功')
        return true
      } catch (error) {
        console.error('更新证书模板失败:', error)
        ElMessage.error('更新失败，请重试')
        return false
      } finally {
        this.loading = false
      }
    },

    // 删除模板
    async deleteTemplate(id: number): Promise<boolean> {
      this.loading = true
      try {
        // 调用真实的API
        await CertificateTemplateApi.delete(id)

        // 如果删除的是当前模板，清空当前模板
        if (this.currentTemplate?.id === id) {
          this.currentTemplate = null
        }

        // 如果删除的是最近保存的模板，清空记录
        if (this.lastSavedTemplateId === id) {
          this.lastSavedTemplateId = null
        }

        ElMessage.success('证书模板删除成功')
        return true
      } catch (error) {
        console.error('删除证书模板失败:', error)
        ElMessage.error('删除失败，请重试')
        return false
      } finally {
        this.loading = false
      }
    },

    // 获取模板详情
    async getTemplateDetail(id: number): Promise<CertificateTemplateVO | null> {
      this.loading = true
      try {
        // 调用真实的API
        const template = await CertificateTemplateApi.getDetail(id)
        if (template) {
          this.currentTemplate = template
          return template
        }
        return null
      } catch (error) {
        console.error('获取证书模板详情失败:', error)
        ElMessage.error('获取模板详情失败')
        return null
      } finally {
        this.loading = false
      }
    },

    // 清空当前模板
    clearCurrentTemplate() {
      this.currentTemplate = null
    },

    // 重置store
    resetStore() {
      this.currentTemplate = null
      this.templateList = []
      this.lastSavedTemplateId = null
      this.loading = false
    }
  },

  // 启用持久化
  persist: {
    key: 'certificate-template-store',
    storage: localStorage,
    paths: ['templateList', 'lastSavedTemplateId']
  }
})

// 在setup外使用
export const useCertificateTemplateStoreWithOut = () => {
  return useCertificateTemplateStore(store)
}
