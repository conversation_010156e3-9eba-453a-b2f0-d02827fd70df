<!--
  页面名称：售后记录标签页
  功能描述：展示家政服务订单的售后记录
-->
<template>
  <div class="after-sales-tab">
    <div class="section-header">
      <div class="section-title">
        <el-icon><Tools /></el-icon>
        售后记录
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="handleAddAfterSales">
          <el-icon><Plus /></el-icon>
          新建售后工单
        </el-button>
      </div>
    </div>

    <div class="after-sales-content">
      <div v-if="afterSalesRecords.length > 0" class="records-list">
        <el-table :data="afterSalesRecords" style="width: 100%" size="small">
          <el-table-column prop="workOrderType" label="工单类型" width="120" />
          <el-table-column prop="problemDescription" label="问题描述" min-width="200" />
          <el-table-column prop="workOrderStatus" label="工单状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.workOrderStatus)" size="small">
                {{ getStatusText(scope.row.workOrderStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="processingResult" label="处理结果" min-width="200" />
          <el-table-column prop="createTime" label="创建时间" width="150" />
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleEditRecord(scope.row)">
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div v-else class="no-records">
        <el-empty description="暂无售后记录" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Tools, Plus } from '@element-plus/icons-vue'

interface Props {
  orderDetail: any
  records?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  orderDetail: {},
  records: () => []
})

// Emits
const emit = defineEmits<{
  'add-after-sales': []
  'edit-record': [record: any]
  'update-records': [records: any[]]
}>()

// 售后记录数据
const afterSalesRecords = ref<any[]>([])

// 监听外部数据变化
watch(
  () => props.records,
  (newRecords) => {
    if (newRecords) {
      afterSalesRecords.value = [...newRecords]
    }
  },
  { immediate: true, deep: true }
)

// 添加新记录的方法
const addRecord = (record: any) => {
  const newRecord = {
    ...record,
    id: Date.now(), // 生成唯一ID
    createTime: new Date().toLocaleString('zh-CN')
  }
  console.log('添加新售后记录:', newRecord)
  afterSalesRecords.value.push(newRecord)
  emit('update-records', afterSalesRecords.value)
}

// 更新记录的方法
const updateRecord = (record: any) => {
  console.log('更新售后记录:', record)
  const index = afterSalesRecords.value.findIndex((item) => item.id === record.id)
  if (index !== -1) {
    afterSalesRecords.value[index] = { ...record }
    emit('update-records', afterSalesRecords.value)
  }
}

// 暴露方法给父组件
defineExpose({
  addRecord,
  updateRecord
})

const getStatusType = (status: string): 'warning' | 'primary' | 'success' | 'info' => {
  const statusMap: Record<string, 'warning' | 'primary' | 'success' | 'info'> = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string): string => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const handleAddAfterSales = () => {
  emit('add-after-sales')
}

const handleEditRecord = (record: any) => {
  emit('edit-record', record)
}
</script>

<style scoped lang="scss">
.after-sales-tab {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
    }
  }

  .after-sales-content {
    .records-list {
      .el-table {
        .el-table__header {
          background-color: #f5f7fa;
        }
      }
    }

    .no-records {
      padding: 40px 0;
    }
  }
}
</style>
