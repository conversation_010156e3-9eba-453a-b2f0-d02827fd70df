<template>
  <div class="test-page">
    <h1>企业培训订单测试页面</h1>

    <el-button type="primary" @click="showView = true"> 打开企业培训订单详情 </el-button>

    <!-- 测试数据 -->
    <div class="test-data">
      <h3>测试数据：</h3>
      <pre>{{ JSON.stringify(testOrderData, null, 2) }}</pre>
    </div>

    <!-- 企业培训订单详情组件 -->
    <EnterpriseTrainingView
      v-model:visible="showView"
      :order-data="testOrderData"
      @edit="handleEdit"
      @view-full-log="handleViewLog"
      @contract-updated="handleContractUpdate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import EnterpriseTrainingView from './components/EnterpriseTrainingView.vue'

// 测试数据
const testOrderData = {
  id: 1,
  orderNumber: 'ET202406001',
  companyName: 'ABC科技有限公司',
  trainingProject: '数字化转型管理培训',
  trainingPeriod: '2024.07.01 - 2024.07.15',
  traineeCount: 25,
  orderAmount: 125000,
  orderStatus: 'fulfilling',
  paymentStatus: 'paid',
  manager: '李四',
  createTime: '2024-06-15',
  businessOpportunity: 'OPP202406005',
  lead: 'LEAD202406005'
}

const showView = ref(false)

const handleEdit = (orderData: any) => {
  console.log('编辑订单:', orderData)
}

const handleViewLog = (orderData: any) => {
  console.log('查看日志:', orderData)
}

const handleContractUpdate = (data: any) => {
  console.log('合同更新:', data)
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-data {
  margin: 20px 0;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

pre {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>

