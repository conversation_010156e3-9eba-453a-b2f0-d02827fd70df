<!--
  页面名称：收支记录表单
  功能描述：新增/编辑家政服务订单的收支记录
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="isEdit ? '编辑收支记录' : '记一笔'"
    direction="rtl"
    size="50%"
    :before-close="handleClose"
  >
    <div class="form-container">
      <div class="form-section">
        <h3 class="section-title">收支信息</h3>

        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
          <el-form-item label="类型" prop="type" required>
            <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
              <el-option label="额外收入" value="额外收入" />
              <el-option label="额外支出" value="额外支出" />
              <el-option label="服务收入" value="服务收入" />
            </el-select>
          </el-form-item>

          <el-form-item label="金额" prop="amount" required>
            <el-input v-model="form.amount" placeholder="请输入金额" type="number">
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>

          <el-form-item label="发生日期" prop="date" required>
            <el-date-picker
              v-model="form.date"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>

          <el-form-item label="描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="4"
              placeholder="请输入描述信息"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  visible: boolean
  orderId?: string
  recordData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: '',
  recordData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()
const isEdit = ref(false)

// 表单数据
const form = reactive({
  type: '额外收入',
  amount: '',
  date: '',
  description: ''
})

// 表单校验规则
const rules: FormRules = {
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
  ],
  date: [{ required: true, message: '请选择日期', trigger: 'change' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }]
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.recordData) {
        // 编辑模式
        isEdit.value = true
        Object.assign(form, props.recordData)
      } else {
        // 新增模式
        isEdit.value = false
        resetForm()
      }
    }
  }
)

// 监听recordData变化
watch(
  () => props.recordData,
  (newVal) => {
    if (newVal && props.visible) {
      isEdit.value = true
      Object.assign(form, newVal)
    }
  }
)

const resetForm = () => {
  form.type = '额外收入'
  form.amount = ''
  form.date = ''
  form.description = ''
  formRef.value?.clearValidate()
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 构建提交的数据
    const submitData = {
      type: form.type,
      amount: parseFloat(form.amount),
      date: form.date,
      description: form.description
    }

    console.log('收支记录表单提交数据:', submitData)
    ElMessage.success(isEdit.value ? '编辑成功' : '添加成功')
    emit('success', submitData)
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.form-container {
  padding: 20px;

  .form-section {
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
}

.drawer-footer {
  text-align: right;
}
</style>
