## 新增讲师

**接口地址**:`/publicbiz/teacher/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "avatar": "",
  "name": "",
  "desc": "",
  "type": "",
  "biz": "",
  "org": "",
  "field": "",
  "phone": "",
  "status": "",
  "signStatus": "",
  "signDate": "",
  "contractType": "",
  "contractTemplate": "",
  "contractNo": "",
  "contractName": "",
  "contractPeriodStart": "",
  "contractPeriodEnd": "",
  "contractAmount": 0,
  "contractFileName": "",
  "contractFileUrl": "",
  "certFiles": [
    {
      "teacherId": 0,
      "certType": "",
      "certName": "",
      "fileName": "",
      "fileUrl": ""
    }
  ]
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| teacherSaveReqVO | 师资库 - 新增讲师 Request VO | body | true | TeacherSaveReqVO | TeacherSaveReqVO |
| &emsp;&emsp;avatar | 头像URL |  | false | string |  |
| &emsp;&emsp;name | 讲师姓名 |  | false | string |  |
| &emsp;&emsp;desc | 简介 |  | false | string |  |
| &emsp;&emsp;type | 讲师类型 |  | false | string |  |
| &emsp;&emsp;biz | 业务模块 |  | false | string |  |
| &emsp;&emsp;org | 关联机构 |  | false | string |  |
| &emsp;&emsp;field | 擅长领域 |  | false | string |  |
| &emsp;&emsp;phone | 联系电话 |  | false | string |  |
| &emsp;&emsp;status | 合作状态 |  | false | string |  |
| &emsp;&emsp;signStatus | 电子签约状态 |  | false | string |  |
| &emsp;&emsp;signDate | 签约日期 |  | false | string(date-time) |  |
| &emsp;&emsp;contractType | 合同类型 |  | false | string |  |
| &emsp;&emsp;contractTemplate | 合同模板 |  | false | string |  |
| &emsp;&emsp;contractNo | 合同编号 |  | false | string |  |
| &emsp;&emsp;contractName | 合同名称 |  | false | string |  |
| &emsp;&emsp;contractPeriodStart | 合同周期开始 |  | false | string(date-time) |  |
| &emsp;&emsp;contractPeriodEnd | 合同周期结束 |  | false | string(date-time) |  |
| &emsp;&emsp;contractAmount | 合同金额 |  | false | number |  |
| &emsp;&emsp;contractFileName | 纸质合同附件文件名 |  | false | string |  |
| &emsp;&emsp;contractFileUrl | 纸质合同附件文件URL |  | false | string |  |
| &emsp;&emsp;certFiles | 讲师资质文件列表 |  | false | array | TeacherCertSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;teacherId |  |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;certType |  |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;certName |  |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;fileName |  |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;fileUrl |  |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```

## 删除讲师

**接口地址**:`/publicbiz/teacher/delete`

**请求方式**:`DELETE`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:<p>根据讲师ID删除讲师（逻辑删除）</p>

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 讲师详情

**接口地址**:`/publicbiz/teacher/detail`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema                    |
| ------ | ---- | ------------------------- |
| 200    | OK   | CommonResultTeacherRespVO |

**响应参数**:

| 参数名称                          | 参数说明 | 类型              | schema            |
| --------------------------------- | -------- | ----------------- | ----------------- |
| code                              |          | integer(int32)    | integer(int32)    |
| data                              |          | TeacherRespVO     | TeacherRespVO     |
| &emsp;&emsp;id                    |          | integer(int64)    |                   |
| &emsp;&emsp;avatar                |          | string            |                   |
| &emsp;&emsp;name                  |          | string            |                   |
| &emsp;&emsp;desc                  |          | string            |                   |
| &emsp;&emsp;type                  |          | string            |                   |
| &emsp;&emsp;biz                   |          | string            |                   |
| &emsp;&emsp;org                   |          | string            |                   |
| &emsp;&emsp;field                 |          | string            |                   |
| &emsp;&emsp;phone                 |          | string            |                   |
| &emsp;&emsp;status                |          | string            |                   |
| &emsp;&emsp;signStatus            |          | string            |                   |
| &emsp;&emsp;signDate              |          | string(date-time) |                   |
| &emsp;&emsp;contractType          |          | string            |                   |
| &emsp;&emsp;contractTemplate      |          | string            |                   |
| &emsp;&emsp;contractNo            |          | string            |                   |
| &emsp;&emsp;contractName          |          | string            |                   |
| &emsp;&emsp;contractPeriodStart   |          | string(date-time) |                   |
| &emsp;&emsp;contractPeriodEnd     |          | string(date-time) |                   |
| &emsp;&emsp;contractAmount        |          | number            |                   |
| &emsp;&emsp;contractFileName      |          | string            |                   |
| &emsp;&emsp;contractFileUrl       |          | string            |                   |
| &emsp;&emsp;certFiles             |          | array             | TeacherCertRespVO |
| &emsp;&emsp;&emsp;&emsp;id        |          | integer(int64)    |                   |
| &emsp;&emsp;&emsp;&emsp;teacherId |          | integer(int64)    |                   |
| &emsp;&emsp;&emsp;&emsp;certType  |          | string            |                   |
| &emsp;&emsp;&emsp;&emsp;certName  |          | string            |                   |
| &emsp;&emsp;&emsp;&emsp;fileName  |          | string            |                   |
| &emsp;&emsp;&emsp;&emsp;fileUrl   |          | string            |                   |
| &emsp;&emsp;createTime            |          | string(date-time) |                   |
| &emsp;&emsp;updateTime            |          | string(date-time) |                   |
| msg                               |          | string            |                   |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"id": 0,
		"avatar": "",
		"name": "",
		"desc": "",
		"type": "",
		"biz": "",
		"org": "",
		"field": "",
		"phone": "",
		"status": "",
		"signStatus": "",
		"signDate": "",
		"contractType": "",
		"contractTemplate": "",
		"contractNo": "",
		"contractName": "",
		"contractPeriodStart": "",
		"contractPeriodEnd": "",
		"contractAmount": 0,
		"contractFileName": "",
		"contractFileUrl": "",
		"certFiles": [
			{
				"id": 0,
				"teacherId": 0,
				"certType": "",
				"certName": "",
				"fileName": "",
				"fileUrl": ""
			}
		],
		"createTime": "",
		"updateTime": ""
	},
	"msg": ""
}
```

## 批量导入讲师

**接口地址**:`/publicbiz/teacher/import`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

暂无

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 讲师分页列表

**接口地址**:`/publicbiz/teacher/page`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo   | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize | 每页条数，最大值为 100 | query    | true     | string   |        |
| type     |                        | query    | false    | string   |        |
| biz      |                        | query    | false    | string   |        |
| status   |                        | query    | false    | string   |        |
| keyword  |                        | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                              |
| ------ | ---- | ----------------------------------- |
| 200    | OK   | CommonResultPageResultTeacherRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultTeacherRespVO | PageResultTeacherRespVO |
| &emsp;&emsp;list | 数据 | array | TeacherRespVO |
| &emsp;&emsp;&emsp;&emsp;id |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;avatar |  | string |  |
| &emsp;&emsp;&emsp;&emsp;name |  | string |  |
| &emsp;&emsp;&emsp;&emsp;desc |  | string |  |
| &emsp;&emsp;&emsp;&emsp;type |  | string |  |
| &emsp;&emsp;&emsp;&emsp;biz |  | string |  |
| &emsp;&emsp;&emsp;&emsp;org |  | string |  |
| &emsp;&emsp;&emsp;&emsp;field |  | string |  |
| &emsp;&emsp;&emsp;&emsp;phone |  | string |  |
| &emsp;&emsp;&emsp;&emsp;status |  | string |  |
| &emsp;&emsp;&emsp;&emsp;signStatus |  | string |  |
| &emsp;&emsp;&emsp;&emsp;signDate |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;contractType |  | string |  |
| &emsp;&emsp;&emsp;&emsp;contractTemplate |  | string |  |
| &emsp;&emsp;&emsp;&emsp;contractNo |  | string |  |
| &emsp;&emsp;&emsp;&emsp;contractName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;contractPeriodStart |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;contractPeriodEnd |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;contractAmount |  | number |  |
| &emsp;&emsp;&emsp;&emsp;contractFileName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;contractFileUrl |  | string |  |
| &emsp;&emsp;&emsp;&emsp;certFiles |  | array | TeacherCertRespVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;teacherId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;certType |  | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;certName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileUrl |  | string |  |
| &emsp;&emsp;&emsp;&emsp;createTime |  | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime |  | string(date-time) |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 0,
				"avatar": "",
				"name": "",
				"desc": "",
				"type": "",
				"biz": "",
				"org": "",
				"field": "",
				"phone": "",
				"status": "",
				"signStatus": "",
				"signDate": "",
				"contractType": "",
				"contractTemplate": "",
				"contractNo": "",
				"contractName": "",
				"contractPeriodStart": "",
				"contractPeriodEnd": "",
				"contractAmount": 0,
				"contractFileName": "",
				"contractFileUrl": "",
				"certFiles": [
					{
						"id": 0,
						"teacherId": 0,
						"certType": "",
						"certName": "",
						"fileName": "",
						"fileUrl": ""
					}
				],
				"createTime": "",
				"updateTime": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 讲师统计卡片数据

**接口地址**:`/publicbiz/teacher/stat`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

暂无

**响应状态**:

| 状态码 | 说明 | schema                        |
| ------ | ---- | ----------------------------- |
| 200    | OK   | CommonResultTeacherStatRespVO |

**响应参数**:

| 参数名称            | 参数说明 | 类型              | schema            |
| ------------------- | -------- | ----------------- | ----------------- |
| code                |          | integer(int32)    | integer(int32)    |
| data                |          | TeacherStatRespVO | TeacherStatRespVO |
| &emsp;&emsp;total   |          | integer(int32)    |                   |
| &emsp;&emsp;inner   |          | integer(int32)    |                   |
| &emsp;&emsp;outer   |          | integer(int32)    |                   |
| &emsp;&emsp;pending |          | integer(int32)    |                   |
| msg                 |          | string            |                   |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"total": 0,
		"inner": 0,
		"outer": 0,
		"pending": 0
	},
	"msg": ""
}
```

## 编辑讲师

**接口地址**:`/publicbiz/teacher/update`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0,
  "avatar": "",
  "name": "",
  "desc": "",
  "type": "",
  "biz": "",
  "org": "",
  "field": "",
  "phone": "",
  "status": "",
  "signStatus": "",
  "signDate": "",
  "contractType": "",
  "contractTemplate": "",
  "contractNo": "",
  "contractName": "",
  "contractPeriodStart": "",
  "contractPeriodEnd": "",
  "contractAmount": 0,
  "contractFileName": "",
  "contractFileUrl": "",
  "certFiles": [
    {
      "teacherId": 0,
      "certType": "",
      "certName": "",
      "fileName": "",
      "fileUrl": ""
    }
  ]
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| teacherUpdateReqVO | 师资库 - 编辑讲师 Request VO | body | true | TeacherUpdateReqVO | TeacherUpdateReqVO |
| &emsp;&emsp;id | 讲师ID |  | false | integer(int64) |  |
| &emsp;&emsp;avatar | 头像URL |  | false | string |  |
| &emsp;&emsp;name | 讲师姓名 |  | false | string |  |
| &emsp;&emsp;desc | 简介 |  | false | string |  |
| &emsp;&emsp;type | 讲师类型 |  | false | string |  |
| &emsp;&emsp;biz | 业务模块 |  | false | string |  |
| &emsp;&emsp;org | 关联机构 |  | false | string |  |
| &emsp;&emsp;field | 擅长领域 |  | false | string |  |
| &emsp;&emsp;phone | 联系电话 |  | false | string |  |
| &emsp;&emsp;status | 合作状态 |  | false | string |  |
| &emsp;&emsp;signStatus | 电子签约状态 |  | false | string |  |
| &emsp;&emsp;signDate | 签约日期 |  | false | string(date-time) |  |
| &emsp;&emsp;contractType | 合同类型 |  | false | string |  |
| &emsp;&emsp;contractTemplate | 合同模板 |  | false | string |  |
| &emsp;&emsp;contractNo | 合同编号 |  | false | string |  |
| &emsp;&emsp;contractName | 合同名称 |  | false | string |  |
| &emsp;&emsp;contractPeriodStart | 合同周期开始 |  | false | string(date-time) |  |
| &emsp;&emsp;contractPeriodEnd | 合同周期结束 |  | false | string(date-time) |  |
| &emsp;&emsp;contractAmount | 合同金额 |  | false | number |  |
| &emsp;&emsp;contractFileName | 纸质合同附件文件名 |  | false | string |  |
| &emsp;&emsp;contractFileUrl | 纸质合同附件文件URL |  | false | string |  |
| &emsp;&emsp;certFiles | 讲师资质文件列表 |  | false | array | TeacherCertSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;teacherId |  |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;certType |  |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;certName |  |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;fileName |  |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;fileUrl |  |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 讲师资质文件列表

**接口地址**:`/publicbiz/teacher/cert/list`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称   | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| ---------- | -------- | -------- | -------- | -------------- | ------ |
| teacher_id |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema                            |
| ------ | ---- | --------------------------------- |
| 200    | OK   | CommonResultListTeacherCertRespVO |

**响应参数**:

| 参数名称              | 参数说明 | 类型           | schema            |
| --------------------- | -------- | -------------- | ----------------- |
| code                  |          | integer(int32) | integer(int32)    |
| data                  |          | array          | TeacherCertRespVO |
| &emsp;&emsp;id        |          | integer(int64) |                   |
| &emsp;&emsp;teacherId |          | integer(int64) |                   |
| &emsp;&emsp;certType  |          | string         |                   |
| &emsp;&emsp;certName  |          | string         |                   |
| &emsp;&emsp;fileName  |          | string         |                   |
| &emsp;&emsp;fileUrl   |          | string         |                   |
| msg                   |          | string         |                   |

**响应示例**:

```javascript
{
	"code": 0,
	"data": [
		{
			"id": 0,
			"teacherId": 0,
			"certType": "",
			"certName": "",
			"fileName": "",
			"fileUrl": ""
		}
	],
	"msg": ""
}
```

## 查看操作日志分页列表

**接口地址**:`/system/operate-log/page`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称   | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| ---------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo     | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize   | 每页条数，最大值为 100 | query    | true     | string   |        |
| userId     | 用户编号               | query    | false    | string   |        |
| bizId      | 操作模块业务编号       | query    | false    | string   |        |
| type       | 操作模块，模拟匹配     | query    | false    | string   |        |
| subType    | 操作名，模拟匹配       | query    | false    | string   |        |
| action     | 操作明细，模拟匹配     | query    | false    | string   |        |
| createTime | 开始时间               | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                                 |
| ------ | ---- | -------------------------------------- |
| 200    | OK   | CommonResultPageResultOperateLogRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultOperateLogRespVO | PageResultOperateLogRespVO |
| &emsp;&emsp;list | 数据 | array | OperateLogRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 日志编号 | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;traceId | 链路追踪编号 | string |  |
| &emsp;&emsp;&emsp;&emsp;userId | 用户编号 | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;userName | 用户昵称 | string |  |
| &emsp;&emsp;&emsp;&emsp;type | 操作模块类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;subType | 操作名 | string |  |
| &emsp;&emsp;&emsp;&emsp;bizId | 操作模块业务编号 | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;action | 操作明细 | string |  |
| &emsp;&emsp;&emsp;&emsp;extra | 拓展字段 | string |  |
| &emsp;&emsp;&emsp;&emsp;requestMethod | 请求方法名 | string |  |
| &emsp;&emsp;&emsp;&emsp;requestUrl | 请求地址 | string |  |
| &emsp;&emsp;&emsp;&emsp;userIp | 用户 IP | string |  |
| &emsp;&emsp;&emsp;&emsp;userAgent | 浏览器 UserAgent | string |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;transMap |  | object |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1024,
				"traceId": "89aca178-a370-411c-ae02-3f0d672be4ab",
				"userId": 1024,
				"userName": "芋艿",
				"type": "订单",
				"subType": "创建订单",
				"bizId": 1,
				"action": "修改编号为 1 的用户信息，将性别从男改成女，将姓名从芋道改成源码。",
				"extra": "{'orderId': 1}",
				"requestMethod": "GET",
				"requestUrl": "/xxx/yyy",
				"userIp": "127.0.0.1",
				"userAgent": "Mozilla/5.0",
				"createTime": "",
				"transMap": {}
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 校验Excel导入的讲师数据

**接口地址**:`/publicbiz/teacher/import/validate-excel`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

暂无

**响应状态**:

| 状态码 | 说明 | schema                                  |
| ------ | ---- | --------------------------------------- |
| 200    | OK   | CommonResultTeacherImportValidateRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | TeacherImportValidateRespVO | TeacherImportValidateRespVO |
| &emsp;&emsp;validateList | 校验结果列表 | array | TeacherImportValidateItemVO |
| &emsp;&emsp;&emsp;&emsp;rowNum | 行号 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;name | 讲师姓名 | string |  |
| &emsp;&emsp;&emsp;&emsp;type | 讲师类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;biz | 业务模块 | string |  |
| &emsp;&emsp;&emsp;&emsp;org | 关联机构 | string |  |
| &emsp;&emsp;&emsp;&emsp;field | 擅长领域 | string |  |
| &emsp;&emsp;&emsp;&emsp;phone | 联系电话 | string |  |
| &emsp;&emsp;&emsp;&emsp;email | 邮箱地址 | string |  |
| &emsp;&emsp;&emsp;&emsp;description | 个人简介 | string |  |
| &emsp;&emsp;&emsp;&emsp;status | 合作状态 | string |  |
| &emsp;&emsp;&emsp;&emsp;errorMessage | 错误信息 | string |  |
| &emsp;&emsp;&emsp;&emsp;validateStatus | 校验状态：VALID-有效，ERROR-错误 | string |  |
| &emsp;&emsp;&emsp;&emsp;originalData |  | TeacherSaveReqVO | TeacherSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;avatar | 头像URL | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name | 讲师姓名 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description | 简介 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;type | 讲师类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;biz | 业务模块 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;org | 关联机构 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;field | 擅长领域 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;phone | 联系电话 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;email | 邮箱地址 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;status | 合作状态 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;signStatus | 电子签约状态 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;signDate | 签约日期 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;contractType | 合同类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;contractTemplate | 合同模板 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;contractNo | 合同编号 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;contractName | 合同名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;contractPeriodStart | 合同周期开始 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;contractPeriodEnd | 合同周期结束 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;contractAmount | 合同金额 | number |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;contractFileName | 纸质合同附件文件名 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;contractFileUrl | 纸质合同附件文件URL | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;certFiles | 讲师资质文件列表 | array | TeacherCertSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id | 资质文件ID（更新时必填，新增时为空） | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;teacherId |  | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;certType |  | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;certName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileName |  | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileUrl |  | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;validStartDate | 证件有效期开始时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;validEndDate | 证件有效期结束时间 | string(date-time) |  |
| &emsp;&emsp;totalCount | 总记录数 | integer(int32) |  |
| &emsp;&emsp;validCount | 有效记录数 | integer(int32) |  |
| &emsp;&emsp;errorCount | 错误记录数 | integer(int32) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"validateList": [
			{
				"rowNum": 0,
				"name": "",
				"type": "",
				"biz": "",
				"org": "",
				"field": "",
				"phone": "",
				"email": "",
				"description": "",
				"status": "",
				"errorMessage": "",
				"validateStatus": "",
				"originalData": {
					"avatar": "",
					"name": "",
					"description": "",
					"type": "",
					"biz": "",
					"org": "",
					"field": "",
					"phone": "",
					"email": "",
					"status": "",
					"signStatus": "",
					"signDate": "",
					"contractType": "",
					"contractTemplate": "",
					"contractNo": "",
					"contractName": "",
					"contractPeriodStart": "",
					"contractPeriodEnd": "",
					"contractAmount": 0,
					"contractFileName": "",
					"contractFileUrl": "",
					"certFiles": [
						{
							"id": 0,
							"teacherId": 0,
							"certType": "",
							"certName": "",
							"fileName": "",
							"fileUrl": "",
							"validStartDate": "",
							"validEndDate": ""
						}
					]
				}
			}
		],
		"totalCount": 0,
		"validCount": 0,
		"errorCount": 0
	},
	"msg": ""
}
```

## 执行批量导入讲师（只导入校验通过的数据）

**接口地址**:`/publicbiz/teacher/import/execute`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "teacherList": [
    {
      "avatar": "",
      "name": "",
      "description": "",
      "type": "",
      "biz": "",
      "org": "",
      "field": "",
      "phone": "",
      "email": "",
      "status": "",
      "signStatus": "",
      "signDate": "",
      "contractType": "",
      "contractTemplate": "",
      "contractNo": "",
      "contractName": "",
      "contractPeriodStart": "",
      "contractPeriodEnd": "",
      "contractAmount": 0,
      "contractFileName": "",
      "contractFileUrl": "",
      "certFiles": [
        {
          "id": 0,
          "teacherId": 0,
          "certType": "",
          "certName": "",
          "fileName": "",
          "fileUrl": "",
          "validStartDate": "",
          "validEndDate": ""
        }
      ]
    }
  ]
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| teacherImportReqVO | 师资库 - 批量导入讲师 Request VO | body | true | TeacherImportReqVO | TeacherImportReqVO |
| &emsp;&emsp;teacherList | 导入的讲师数据列表 |  | false | array | TeacherSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;avatar | 头像URL |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;name | 讲师姓名 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;description | 简介 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;type | 讲师类型 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;biz | 业务模块 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;org | 关联机构 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;field | 擅长领域 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;phone | 联系电话 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;email | 邮箱地址 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;status | 合作状态 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;signStatus | 电子签约状态 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;signDate | 签约日期 |  | false | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;contractType | 合同类型 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;contractTemplate | 合同模板 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;contractNo | 合同编号 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;contractName | 合同名称 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;contractPeriodStart | 合同周期开始 |  | false | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;contractPeriodEnd | 合同周期结束 |  | false | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;contractAmount | 合同金额 |  | false | number |  |
| &emsp;&emsp;&emsp;&emsp;contractFileName | 纸质合同附件文件名 |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;contractFileUrl | 纸质合同附件文件URL |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;certFiles | 讲师资质文件列表 |  | false | array | TeacherCertSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id | 资质文件ID（更新时必填，新增时为空） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;teacherId |  |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;certType |  |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;certName |  |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileName |  |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;fileUrl |  |  | false | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;validStartDate | 证件有效期开始时间 |  | false | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;validEndDate | 证件有效期结束时间 |  | false | string(date-time) |  |

**响应状态**:

| 状态码 | 说明 | schema                          |
| ------ | ---- | ------------------------------- |
| 200    | OK   | CommonResultTeacherImportRespVO |

**响应参数**:

| 参数名称                 | 参数说明     | 类型                | schema              |
| ------------------------ | ------------ | ------------------- | ------------------- |
| code                     |              | integer(int32)      | integer(int32)      |
| data                     |              | TeacherImportRespVO | TeacherImportRespVO |
| &emsp;&emsp;totalCount   | 总记录数     | integer(int32)      |                     |
| &emsp;&emsp;successCount | 成功导入数   | integer(int32)      |                     |
| &emsp;&emsp;failCount    | 失败数       | integer(int32)      |                     |
| &emsp;&emsp;failReasons  | 失败原因列表 | string              |                     |
| msg                      |              | string              |                     |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"totalCount": 0,
		"successCount": 0,
		"failCount": 0,
		"failReasons": ""
	},
	"msg": ""
}
```
