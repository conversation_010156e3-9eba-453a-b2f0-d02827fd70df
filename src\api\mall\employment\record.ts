import request from '@/config/axios'

/**
 * 激励/处罚记录API接口
 */

// 记录信息接口
export interface Record {
  id: string
  type: 'incentive' | 'punishment'
  title: string
  date: string
  creditImpact: number
  moneyImpact: number
  reason: string
  status: 'pending' | 'approved' | 'rejected'
  attachments: Array<{
    id: string
    name: string
    url: string
  }>
  recorder: string
  createTime: string
  updateTime: string
}

// 查询参数接口
export interface RecordQueryParams {
  type?: 'incentive' | 'punishment'
  dateRange?: string[]
  status?: string
  pageNum: number
  pageSize: number
}

// 新增记录参数接口
export interface CreateRecordParams {
  type: 'incentive' | 'punishment'
  title: string
  date: string
  creditImpact: number
  moneyImpact: number
  reason: string
  attachments?: Array<{
    name: string
    url: string
  }>
}

// 更新记录参数接口
export interface UpdateRecordParams {
  id: string
  title?: string
  date?: string
  creditImpact?: number
  moneyImpact?: number
  reason?: string
  status?: 'pending' | 'approved' | 'rejected'
  attachments?: Array<{
    name: string
    url: string
  }>
}

/**
 * 获取记录列表
 * @param params 查询参数
 * @returns Promise<{ list: Record[], total: number }>
 */
export function getRecordList(params: RecordQueryParams) {
  return request.get({
    url: '/mall/employment/record/list',
    params
  })
}

/**
 * 获取记录详情
 * @param id 记录ID
 * @returns Promise<Record>
 */
export function getRecordDetail(id: string) {
  return request.get({
    url: `/mall/employment/record/${id}`
  })
}

/**
 * 新增记录
 * @param data 记录信息
 * @returns Promise<void>
 */
export function createRecord(data: CreateRecordParams) {
  return request.post({
    url: '/mall/employment/record',
    data
  })
}

/**
 * 更新记录
 * @param data 更新信息
 * @returns Promise<void>
 */
export function updateRecord(data: UpdateRecordParams) {
  return request.put({
    url: `/mall/employment/record/${data.id}`,
    data
  })
}

/**
 * 删除记录
 * @param id 记录ID
 * @returns Promise<void>
 */
export function deleteRecord(id: string) {
  return request.delete({
    url: `/mall/employment/record/${id}`
  })
}

/**
 * 审核记录
 * @param id 记录ID
 * @param status 审核状态
 * @param comment 审核意见
 * @returns Promise<void>
 */
export function approveRecord(id: string, status: 'approved' | 'rejected', comment?: string) {
  return request.put({
    url: `/mall/employment/record/${id}/approve`,
    data: { status, comment }
  })
}

/**
 * 导出记录列表
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export function exportRecordList(params: Omit<RecordQueryParams, 'pageNum' | 'pageSize'>) {
  return request.download({
    url: '/mall/employment/record/export',
    params
  })
}
