<!--
  页面名称：新增/编辑家政服务订单
  功能描述：新增/编辑家政服务订单，支持表单校验、提交、重置、编辑回显
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="handleUpdateVisible"
    :title="isEdit ? '编辑家政服务订单' : '新建家政服务订单'"
    direction="rtl"
    size="500px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="order-form">
        <!-- 客户与服务信息模块 -->
        <div class="form-module">
          <div class="module-title">
            <el-icon><User /></el-icon>
            客户与服务信息
          </div>
          <div class="module-content">
            <!-- 关联商机 -->
            <el-form-item label="关联商机" prop="businessOpportunity">
              <el-select
                v-model="form.businessOpportunity"
                placeholder="请选择关联商机 (可选)"
                clearable
                style="width: 100%"
              >
                <el-option label="商机001" value="opp001" />
                <el-option label="商机002" value="opp002" />
              </el-select>
              <div class="form-tip">选择关联商机可自动填充部分服务信息</div>
            </el-form-item>

            <!-- 关联线索 -->
            <el-form-item label="关联线索" prop="lead">
              <el-select
                v-model="form.lead"
                placeholder="请选择关联线索 (可选)"
                clearable
                style="width: 100%"
              >
                <el-option label="线索001" value="lead001" />
                <el-option label="线索002" value="lead002" />
              </el-select>
              <div class="form-tip">选择关联线索可自动填充客户信息</div>
            </el-form-item>

            <!-- 客户姓名 -->
            <el-form-item label="客户姓名" prop="customerName">
              <el-input v-model="form.customerName" placeholder="请输入客户姓名" />
            </el-form-item>

            <!-- 联系电话 -->
            <el-form-item label="联系电话" prop="customerPhone">
              <el-input v-model="form.customerPhone" placeholder="请输入联系电话" />
            </el-form-item>

            <!-- 服务地址 -->
            <el-form-item label="服务地址" prop="serviceAddress">
              <el-input v-model="form.serviceAddress" placeholder="请输入服务地址" />
            </el-form-item>

            <!-- 服务套餐 -->
            <el-form-item label="服务套餐" prop="servicePackage">
              <el-select
                v-model="form.servicePackage"
                placeholder="请选择服务套餐"
                style="width: 100%"
                @change="handleServicePackageChange"
              >
                <el-option label="SP001 - 月嫂服务套餐 (¥12800.00)" value="package_a" />
                <el-option label="SP002 - 油烟机深度清洗 (¥188.00)" value="package_b" />
                <el-option label="SP003 - 小时工服务 (¥80.00/小时)" value="package_c" />
                <el-option label="SP004 - 育儿嫂服务 (¥9800.00)" value="package_d" />
                <el-option label="SP005 - 深度保洁服务 (¥1500.00)" value="package_e" />
                <el-option label="SP006 - 日常保洁服务 (¥200.00)" value="package_f" />
                <el-option label="SP007 - 玻璃清洗服务 (¥150.00)" value="package_g" />
              </el-select>
              <div class="form-tip">选择套餐后会自动填充服务类型和订单金额</div>
            </el-form-item>

            <!-- 服务类型 -->
            <el-form-item label="服务类型" prop="serviceType">
              <el-select
                v-model="form.serviceType"
                placeholder="请先选择服务套餐"
                style="width: 100%"
                :disabled="!form.servicePackage"
              >
                <el-option label="月嫂服务" value="maternity" />
                <el-option label="深度保洁" value="deep_cleaning" />
                <el-option label="小时工" value="hourly" />
                <el-option label="育儿嫂服务" value="nanny" />
                <el-option label="油烟机清洗" value="range_hood_cleaning" />
                <el-option label="日常保洁" value="daily_cleaning" />
                <el-option label="玻璃清洗" value="glass_cleaning" />
              </el-select>
            </el-form-item>

            <!-- 预约上门时间 -->
            <el-form-item label="预约上门时间" prop="appointmentTime">
              <el-date-picker
                v-model="form.appointmentTime"
                type="datetime"
                placeholder="年-月-日 --:--"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <!-- 指派服务人员 -->
            <el-form-item label="指派服务人员" prop="servicePersonnel">
              <el-input v-model="form.servicePersonnel" placeholder="请输入服务人员" />
            </el-form-item>

            <!-- 服务机构 -->
            <el-form-item label="服务机构" prop="serviceAgency">
              <el-select
                v-model="form.serviceAgency"
                placeholder="请选择服务机构 (可选)"
                clearable
                style="width: 100%"
              >
                <el-option label="爱家月嫂服务中心" value="agency_1" />
                <el-option label="清洁无忧家政公司" value="agency_2" />
                <el-option label="母婴护理专家机构" value="agency_3" />
                <el-option label="专业清洗服务公司" value="agency_4" />
              </el-select>
              <div class="form-tip">如果选择服务机构,服务人员将从该机构指派</div>
            </el-form-item>

            <!-- 订单金额 -->
            <el-form-item label="订单金额" prop="orderAmount">
              <el-input v-model="form.orderAmount" placeholder="请输入订单金额" type="number">
                <template #prefix>¥</template>
              </el-input>
            </el-form-item>

            <!-- 支付状态 -->
            <el-form-item label="支付状态" prop="paymentStatus">
              <el-select
                v-model="form.paymentStatus"
                placeholder="请选择支付状态"
                style="width: 100%"
              >
                <el-option label="未支付" value="unpaid" />
                <el-option label="已支付" value="paid" />
                <el-option label="部分支付" value="partial" />
              </el-select>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { User } from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 表单数据
const form = reactive({
  businessOpportunity: '',
  lead: '',
  customerName: '',
  customerPhone: '',
  serviceAddress: '',
  servicePackage: '',
  serviceType: '',
  appointmentTime: '',
  servicePersonnel: '',
  serviceAgency: '',
  orderAmount: '',
  paymentStatus: 'unpaid'
})

// 表单校验规则
const rules = {
  customerName: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
  customerPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  serviceAddress: [{ required: true, message: '请输入服务地址', trigger: 'blur' }],
  servicePackage: [{ required: true, message: '请选择服务套餐', trigger: 'change' }],
  serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
  appointmentTime: [{ required: true, message: '请选择预约时间', trigger: 'change' }],
  servicePersonnel: [{ required: true, message: '请输入服务人员', trigger: 'blur' }],
  orderAmount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
  ],
  paymentStatus: [{ required: true, message: '请选择支付状态', trigger: 'change' }]
}

// 计算属性
const isEdit = computed(() => !!props.orderData)

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.orderData) {
      // 编辑模式，回显数据
      Object.assign(form, props.orderData)
    } else if (newVal && !props.orderData) {
      // 新增模式，重置表单
      resetForm()
    }
  }
)

// 方法定义
const handleServicePackageChange = (value: string) => {
  // 根据套餐自动填充服务类型和金额
  const packageMap = {
    package_a: { serviceType: 'maternity', orderAmount: '12800' },
    package_b: { serviceType: 'range_hood_cleaning', orderAmount: '188' },
    package_c: { serviceType: 'hourly', orderAmount: '320' },
    package_d: { serviceType: 'nanny', orderAmount: '9800' },
    package_e: { serviceType: 'deep_cleaning', orderAmount: '1500' },
    package_f: { serviceType: 'daily_cleaning', orderAmount: '200' },
    package_g: { serviceType: 'glass_cleaning', orderAmount: '150' }
  }

  if (packageMap[value]) {
    form.serviceType = packageMap[value].serviceType
    form.orderAmount = packageMap[value].orderAmount
  }
}

const resetForm = () => {
  Object.assign(form, {
    businessOpportunity: '',
    lead: '',
    customerName: '',
    customerPhone: '',
    serviceAddress: '',
    servicePackage: '',
    serviceType: '',
    appointmentTime: '',
    servicePersonnel: '',
    serviceAgency: '',
    orderAmount: '',
    paymentStatus: 'unpaid'
  })
  formRef.value?.clearValidate()
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleUpdateVisible = (newVal: boolean) => {
  emit('update:visible', newVal)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped lang="scss">
.drawer-content {
  padding: 10px;
  height: calc(100vh - 120px);
  overflow-y: auto;

  .order-form {
    .form-module {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 10px;
      margin-bottom: 10px;
      overflow: visible;

      .module-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e4e7ed;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .module-content {
        overflow: visible;

        .el-form-item {
          margin-bottom: 15px;

          .el-form-item__label {
            font-weight: 500;
            color: #333;
          }

          .el-input__wrapper,
          .el-select .el-input__wrapper {
            border-radius: 4px;
            box-shadow: 0 0 0 1px #dcdfe6 inset;

            &:hover {
              box-shadow: 0 0 0 1px #c0c4cc inset;
            }

            &.is-focus {
              box-shadow: 0 0 0 1px #409eff inset;
            }
          }

          .el-date-editor {
            .el-input__wrapper {
              border-radius: 4px;
            }
          }
        }

        .form-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 2px;
          line-height: 1.4;
        }
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fff;

  .el-button {
    border-radius: 4px;
    font-weight: 500;
  }
}
</style>
