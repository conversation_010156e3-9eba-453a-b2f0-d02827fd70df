<!--
  页面名称：机构档案信息
  功能描述：展示机构的基本信息、合作信息、资质信息等
-->
<template>
  <div class="agency-profile">
    <!-- 基本信息 -->
    <div class="profile-section">
      <div class="section-title">机构基本信息</div>
      <div class="info-grid">
        <div class="info-pair">
          <label>机构全称</label>
          <span>{{ agency.fullName || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>机构简称</label>
          <span>{{ agency.name || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>机构ID</label>
          <span>{{ agency.id || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>机构类型</label>
          <span>{{ agency.type || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>法人代表</label>
          <span>{{ agency.legalPerson || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>成立日期</label>
          <span>{{ formatDate(agency.establishDate) }}</span>
        </div>
        <div class="info-pair full-width">
          <label>统一社会信用代码</label>
          <span>{{ agency.creditCode || '-' }}</span>
        </div>
        <div class="info-pair full-width">
          <label>注册地址</label>
          <span>{{ agency.registerAddress || '-' }}</span>
        </div>
        <div class="info-pair full-width">
          <label>经营地址</label>
          <span>{{ agency.businessAddress || '-' }}</span>
        </div>
        <div class="info-pair full-width">
          <label>主营业务</label>
          <div>
            <el-tag
              v-for="service in agency.businessServices"
              :key="service"
              size="small"
              class="service-tag"
            >
              {{ service }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 合作与商业信息 -->
    <div class="profile-section">
      <div class="section-title">合作与商业信息</div>
      <div class="info-grid">
        <div class="info-pair">
          <label>主要联系人</label>
          <span>{{ agency.contact || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>联系电话</label>
          <span>{{ agency.phone || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>合作状态</label>
          <el-tag :type="agency.status === 'cooperating' ? 'success' : 'warning'" size="small">
            {{ agency.statusText || '-' }}
          </el-tag>
        </div>
        <div class="info-pair">
          <label>当前评级</label>
          <span>{{ agency.rating || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>合作模式</label>
          <span>{{ agency.cooperationMode || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>我方签约人</label>
          <span>{{ agency.ourSigner || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>合同编号</label>
          <span>{{ agency.contractNo || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>合同有效期</label>
          <span>{{ formatDateRange(agency.contractStartDate, agency.contractEndDate) }}</span>
        </div>
        <div class="info-pair">
          <label>保证金</label>
          <span>{{ formatMoney(agency.deposit) }}</span>
        </div>
        <div class="info-pair">
          <label>续约提醒</label>
          <span>{{ formatDate(agency.renewalReminder) }}</span>
        </div>
      </div>
    </div>

    <!-- 资质与结算信息 -->
    <div class="profile-section">
      <div class="section-title">资质与结算信息</div>
      <div class="info-grid">
        <div class="info-pair">
          <label>对公账户名</label>
          <span>{{ agency.accountName || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>结算周期</label>
          <span>{{ agency.settlementCycle || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>开户银行</label>
          <span>{{ agency.bankName || '-' }}</span>
        </div>
        <div class="info-pair">
          <label>银行账号</label>
          <span>{{ agency.bankAccount || '-' }}</span>
        </div>
        <div class="info-pair full-width">
          <label>资质文件</label>
          <div class="file-list">
            <div v-for="file in agency.qualificationFiles" :key="file.id" class="file-item">
              <el-icon :color="file.status === 'verified' ? '#67c23a' : '#909399'">
                <component :is="file.status === 'verified' ? 'CheckCircle' : 'TimesCircle'" />
              </el-icon>
              <span>{{ file.name }}</span>
              <el-button type="text" size="small" @click="viewFile(file)"> 查看 </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckCircle, TimesCircle } from '@element-plus/icons-vue'

/** 机构数据 */
const props = defineProps<{
  agency: any
}>()

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString()
}

/** 格式化日期范围 */
const formatDateRange = (startDate: string, endDate: string) => {
  if (!startDate || !endDate) return '-'
  return `${formatDate(startDate)} 至 ${formatDate(endDate)}`
}

/** 格式化金额 */
const formatMoney = (amount: number) => {
  if (!amount) return '-'
  return `¥${amount.toLocaleString()}`
}

/** 查看文件 */
const viewFile = (file: any) => {
  console.log('查看文件:', file)
}
</script>

<style scoped lang="scss">
.agency-profile {
  .profile-section {
    margin-bottom: 30px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #343a40;
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px 30px;
  }

  .info-pair {
    font-size: 14px;

    label {
      display: block;
      color: #6c757d;
      font-size: 13px;
      margin-bottom: 4px;
    }

    span {
      font-weight: 500;
      color: #343a40;
    }

    &.full-width {
      grid-column: 1 / -1;
    }
  }

  .service-tag {
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .file-list {
    .file-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
