<!--
  页面名称：轮播图管理
  功能描述：展示轮播图列表，支持新增、编辑、删除等操作
-->
<template>
  <div class="carousel-management">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="轮播图标题">
          <el-autocomplete
            v-model="searchForm.carouselTitle"
            :fetch-suggestions="
              (queryString, cb) => {
                const suggestions = searchHistory.value
                  .filter((item) => item.toLowerCase().includes(queryString.toLowerCase()))
                  .map((item) => ({ value: item }))
                cb(suggestions)
              }
            "
            placeholder="输入轮播图标题"
            clearable
            @keyup.enter="handleSearch"
            @input="handleDebouncedSearch"
            @select="
              (item) => {
                searchForm.carouselTitle = item.value
                handleSearch()
              }
            "
            style="width: 100%"
          >
            <template #default="{ item }">
              <div class="history-item">
                <el-icon><Clock /></el-icon>
                <span>{{ item.value }}</span>
              </div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 100px">
            <el-option label="全部" value="" />
            <el-option :label="'启用'" :value="1" />
            <el-option :label="'禁用'" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="平台">
          <el-select
            v-model="searchForm.platform"
            placeholder="全部"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="雇主端" value="employer" />
            <el-option label="阿姨端" value="aunt" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSearch">
            <el-icon v-if="!loading"><Search /></el-icon>
            {{ loading ? '查询中...' : '查询' }}
          </el-button>
          <el-button :loading="loading" @click="handleReset">
            <el-icon v-if="!loading"><Refresh /></el-icon>
            {{ loading ? '重置中...' : '重置' }}
          </el-button>
        </el-form-item>
        <el-form-item class="add-button-wrapper">
          <el-button type="primary" @click="onAdd">
            <i class="fas fa-plus"></i> 新增轮播图
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 查询条件显示 -->
    <div v-if="hasActiveFilters" class="filter-tags">
      <el-tag
        v-if="searchForm.carouselTitle"
        closable
        @close="clearFilter('carouselTitle')"
        type="info"
      >
        标题: {{ searchForm.carouselTitle }}
      </el-tag>
      <el-tag
        v-if="searchForm.status !== ''"
        closable
        @close="clearFilter('status')"
        type="warning"
      >
        状态: {{ searchForm.status === 1 ? '启用' : '禁用' }}
      </el-tag>
      <el-tag v-if="searchForm.platform" closable @close="clearFilter('platform')" type="success">
        平台: {{ searchForm.platform === 'employer' ? '雇主端' : '阿姨端' }}
      </el-tag>
      <el-button v-if="hasActiveFilters" type="text" size="small" @click="clearAllFilters">
        清除所有筛选
      </el-button>
    </div>

    <!-- 查询统计信息 -->
    <div v-if="tableData.length > 0 || hasActiveFilters" class="query-stats">
      <el-text type="info">
        共找到 <strong>{{ pagination.total }}</strong> 条记录
        <span v-if="hasActiveFilters" class="filter-info"> (已应用筛选条件) </span>
      </el-text>
    </div>

    <!-- 轮播图列表 -->
    <el-table
      :data="tableData"
      :loading="loading"
      style="width: 100%"
      v-loading="loading"
      element-loading-text="数据加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="轮播图" width="120">
        <template #default="scope">
          <el-image
            :src="scope.row.carouselImageUrl"
            :preview-src-list="[scope.row.carouselImageUrl]"
            style="width: 80px; height: 60px"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="carouselTitle" label="标题" min-width="150" />
      <el-table-column prop="carouselLinkUrl" label="链接地址" min-width="200" />
      <el-table-column prop="sortOrder" label="排序" width="80" />
      <el-table-column prop="platform" label="平台" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.platform === 'employer' ? 'primary' : 'success'" size="small">
            {{ scope.row.platform === 'employer' ? '雇主端' : '阿姨端' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'" size="small">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="150">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="onEdit(scope.row)">编辑</el-button>
          <el-button
            size="small"
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            @click="onToggleStatus(scope.row)"
          >
            {{ scope.row.status === 1 ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="danger" @click="onDelete(scope.row)"> 删除 </el-button>
        </template>
      </el-table-column>

      <!-- 空数据状态 -->
      <template #empty>
        <div class="empty-data">
          <el-empty
            :image-size="100"
            :description="hasActiveFilters ? '没有找到匹配的数据' : '暂无数据'"
          >
            <el-button v-if="hasActiveFilters" type="primary" @click="clearAllFilters">
              清除筛选条件
            </el-button>
            <el-button v-else type="primary" @click="onAdd"> 新增轮播图 </el-button>
          </el-empty>
        </div>
      </template>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.pageNo"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" @close="onDialogClose">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="平台" prop="platform">
          <el-select v-model="form.platform" placeholder="请选择平台" style="width: 100%">
            <el-option label="雇主端" value="employer" />
            <el-option label="阿姨端" value="aunt" />
          </el-select>
        </el-form-item>
        <el-form-item label="轮播图" prop="carouselImageUrl">
          <div class="image-upload-container">
            <el-upload
              class="image-uploader"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :http-request="handleImageUpload"
              :on-success="onImageSuccess"
            >
              <img v-if="form.carouselImageUrl" :src="form.carouselImageUrl" class="upload-image" />
              <el-icon v-else class="upload-icon"><Plus /></el-icon>
              <div class="upload-text"></div>
            </el-upload>
            <div class="image-upload-tips">
              <div class="tip-item">
                <i class="el-icon-info"></i>
                <span>建议尺寸750×400px</span>
              </div>
              <div class="tip-item">
                <i class="el-icon-info"></i>
                <span>支持jpg、png、gif格式，不超过2MB</span>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="标题" prop="carouselTitle">
          <el-input
            v-model="form.carouselTitle"
            placeholder="请输入轮播图标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="链接地址" prop="carouselLinkUrl">
          <el-input
            v-model="form.carouselLinkUrl"
            placeholder="请输入链接地址"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onDialogClose">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { Plus, Search, Refresh, Clock } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getCarouselList,
  createCarousel,
  updateCarousel,
  deleteCarousel,
  updateCarouselStatus,
  type Carousel,
  type CarouselQueryParams,
  type CreateCarouselParams,
  type UpdateCarouselParams
} from '@/api/mall/employment/carousel'
// 导入文件上传接口
import { updateFile } from '@/api/infra/file'

/** 搜索表单数据 */
const searchForm = reactive({
  carouselTitle: '',
  status: '',
  platform: ''
})

/** 表格数据 */
const tableData = ref<Carousel[]>([])

/** 分页信息 */
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

/** 加载状态 */
const loading = ref(false)

/** 对话框显示状态 */
const dialogVisible = ref(false)

/** 对话框标题 */
const dialogTitle = ref('')

/** 表单数据 */
const form = reactive<CreateCarouselParams & { id?: number }>({
  id: undefined,
  platform: 'employer',
  carouselImageUrl: '',
  carouselTitle: '',
  carouselLinkUrl: '',
  sortOrder: 0,
  status: 1
})

/** 表单校验规则 */
const rules = {
  platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
  carouselImageUrl: [{ required: true, message: '请上传轮播图', trigger: 'change' }],
  carouselTitle: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  carouselLinkUrl: [{ required: true, message: '请输入链接地址', trigger: 'blur' }],
  sortOrder: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

/** 表单引用 */
const formRef = ref()

/** 防抖查询 */
const debouncedSearch = ref<NodeJS.Timeout | null>(null)

/** 搜索处理 */
const handleSearch = async () => {
  try {
    // 清除之前的防抖定时器
    if (debouncedSearch.value) {
      clearTimeout(debouncedSearch.value)
    }

    // 保存查询历史
    if (searchForm.carouselTitle?.trim()) {
      saveSearchHistory(searchForm.carouselTitle.trim())
    }

    // 重置到第一页
    pagination.pageNo = 1
    // 显示加载状态
    loading.value = true
    // 执行查询
    await fetchList()
    ElMessage.success('查询完成')
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 防抖搜索处理 */
const handleDebouncedSearch = () => {
  // 清除之前的定时器
  if (debouncedSearch.value) {
    clearTimeout(debouncedSearch.value)
  }

  // 设置新的定时器，500ms后执行查询
  debouncedSearch.value = setTimeout(() => {
    handleSearch()
  }, 500)
}

/** 重置搜索 */
const handleReset = async () => {
  try {
    // 重置搜索表单
    Object.assign(searchForm, {
      carouselTitle: '',
      status: '',
      platform: ''
    })
    // 重置分页
    pagination.pageNo = 1
    // 显示加载状态
    loading.value = true
    // 重新获取数据
    await fetchList()
    ElMessage.success('重置完成')
  } catch (error) {
    console.error('重置失败:', error)
    ElMessage.error('重置失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 获取轮播图列表 */
const fetchList = async () => {
  try {
    // 构建查询参数
    const params: CarouselQueryParams = {
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      carouselTitle: searchForm.carouselTitle?.trim() || undefined,
      status: searchForm.status !== '' ? Number(searchForm.status) : undefined,
      platform: searchForm.platform || undefined
    }

    // 移除空值参数
    Object.keys(params).forEach((key) => {
      if (params[key] === undefined || params[key] === null || params[key] === '') {
        delete params[key]
      }
    })

    // 调用真实接口
    console.log('调用真实接口，参数:', params)
    const res: any = await getCarouselList(params)
    console.log('出参:', res)

    // 兼容不同返回形态：res 可能是 {list,total} 或 {data: { list, total }} 或 纯数组
    const rawList = Array.isArray(res)
      ? res
      : res?.list || res?.data?.list || res?.records || res?.data || []

    // 设置分页信息
    pagination.total = (res?.total ?? res?.data?.total ?? 0) as number
    if (res?.pageNo) pagination.pageNo = res.pageNo
    if (res?.pageSize) pagination.pageSize = res.pageSize

    // 数据映射，确保字段名称一致
    tableData.value = (rawList || []).map((item: any) => {
      return {
        id: item.id,
        carouselTitle: item.carouselTitle || item.carousel_title || item.title || '-',
        carouselImageUrl:
          item.carouselImageUrl || item.carousel_image_url || item.imageUrl || item.image_url || '',
        carouselLinkUrl:
          item.carouselLinkUrl || item.carousel_link_url || item.linkUrl || item.link_url || '',
        sortOrder: item.sortOrder || item.sort_order || item.sort || 0,
        status: item.status !== undefined && item.status !== null ? item.status : 1,
        platform: item.platform || 'employer',
        createTime: item.createTime || item.create_time || item.createTime || '',
        updateTime: item.updateTime || item.update_time || item.updateTime || ''
      }
    })

    console.log('处理后的数据:', tableData.value)
  } catch (error) {
    console.error('获取轮播图列表失败:', error)
    ElMessage.error('获取轮播图列表失败')
    // 清空数据
    tableData.value = []
    pagination.total = 0
    throw error
  }
}

/** 新增 */
const onAdd = () => {
  dialogTitle.value = '新增轮播图'
  Object.assign(form, {
    id: undefined,
    platform: 'employer',
    carouselImageUrl: '',
    carouselTitle: '',
    carouselLinkUrl: '',
    sortOrder: 0,
    status: 1
  })
  dialogVisible.value = true
}

/** 编辑 */
const onEdit = (row: Carousel) => {
  dialogTitle.value = '编辑轮播图'
  Object.assign(form, {
    id: row.id,
    platform: row.platform,
    carouselImageUrl: row.carouselImageUrl,
    carouselTitle: row.carouselTitle,
    carouselLinkUrl: row.carouselLinkUrl,
    sortOrder: row.sortOrder,
    status: row.status
  })
  dialogVisible.value = true
}

/** 切换状态 */
const onToggleStatus = async (row: Carousel) => {
  try {
    const newStatus = row.status === 1 ? 0 : 1
    const res = await updateCarouselStatus({ id: row.id, status: newStatus })
    ElMessage.success('状态更新成功')
    fetchList()
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
}

/** 删除 */
const onDelete = async (row: Carousel) => {
  try {
    await ElMessageBox.confirm('确定要删除这个轮播图吗？', '提示', {
      type: 'warning'
    })
    const res = await deleteCarousel(row.id)
    ElMessage.success('删除成功')
    fetchList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

/** 图片上传前校验 */
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

/** 图片上传成功 */
const onImageSuccess = (response: any, file: any) => {
  if (response && response.data) {
    form.carouselImageUrl = response.data
    ElMessage.success('图片上传成功')
  }
}

/** 图片上传请求 */
const handleImageUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      form.carouselImageUrl = response.data
      ElMessage.success('轮播图上传成功')
    }
  } catch (error) {
    console.error('轮播图上传失败:', error)
    ElMessage.error('轮播图上传失败')
  }
}

/** 对话框关闭 */
const onDialogClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

/** 提交表单 */
const onSubmit = async () => {
  try {
    await formRef.value?.validate()

    if (form.id) {
      // 编辑
      const updateData: UpdateCarouselParams = {
        id: form.id,
        platform: form.platform,
        carouselTitle: form.carouselTitle,
        carouselImageUrl: form.carouselImageUrl,
        carouselLinkUrl: form.carouselLinkUrl,
        sortOrder: form.sortOrder,
        status: form.status
      }
      const res = await updateCarousel(updateData)
      ElMessage.success('更新成功')
      dialogVisible.value = false
      fetchList()
    } else {
      // 新增
      const createData: CreateCarouselParams = {
        platform: form.platform,
        carouselTitle: form.carouselTitle,
        carouselImageUrl: form.carouselImageUrl,
        carouselLinkUrl: form.carouselLinkUrl,
        sortOrder: form.sortOrder,
        status: form.status
      }
      const res = await createCarousel(createData)
      ElMessage.success('新增成功')
      dialogVisible.value = false
      fetchList()
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/** 分页大小改变 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageNo = 1
  fetchList()
}

/** 当前页改变 */
const handleCurrentChange = (page: number) => {
  pagination.pageNo = page
  fetchList()
}

/** 判断是否有激活的筛选条件 */
const hasActiveFilters = computed(() => {
  return searchForm.carouselTitle || searchForm.status !== '' || searchForm.platform
})

/** 清除单个筛选条件 */
const clearFilter = (key: string) => {
  searchForm[key] = ''
  pagination.pageNo = 1
  fetchList()
}

/** 清除所有筛选条件 */
const clearAllFilters = () => {
  Object.assign(searchForm, {
    carouselTitle: '',
    status: '',
    platform: ''
  })
  pagination.pageNo = 1
  fetchList()
}

/** 查询历史记录 */
const searchHistory = ref<string[]>([])

/** 保存查询历史 */
const saveSearchHistory = (query: string) => {
  if (!query.trim()) return

  // 移除重复的查询
  const index = searchHistory.value.indexOf(query)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }

  // 添加到开头
  searchHistory.value.unshift(query)

  // 限制历史记录数量
  if (searchHistory.value.length > 10) {
    searchHistory.value = searchHistory.value.slice(0, 10)
  }

  // 保存到本地存储
  localStorage.setItem('carouselSearchHistory', JSON.stringify(searchHistory.value))
}

/** 加载查询历史 */
const loadSearchHistory = () => {
  try {
    const history = localStorage.getItem('carouselSearchHistory')
    if (history) {
      searchHistory.value = JSON.parse(history)
    }
  } catch (error) {
    console.error('加载查询历史失败:', error)
  }
}

/** 应用历史查询 */
const applyHistoryQuery = (query: string) => {
  searchForm.carouselTitle = query
  handleSearch()
}

onMounted(() => {
  fetchList()
  loadSearchHistory() // 加载历史记录
})
</script>

<style scoped lang="scss">
.carousel-management {
  .search-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-form {
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }

        &.add-button-wrapper {
          margin-left: auto;
        }
      }
    }
  }

  .action-bar {
    margin-bottom: 20px;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
  }

  .filter-tags {
    margin-bottom: 20px;
    padding: 10px 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;

    .el-tag {
      background-color: #e1f3d8;
      color: #67c23a;
      border-color: #67c23a;
    }

    .el-button {
      color: #409eff;
      font-size: 14px;
    }
  }

  .query-stats {
    margin-bottom: 20px;
    padding: 10px 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    gap: 10px;

    .el-text {
      font-size: 14px;
      color: #606266;

      strong {
        color: #409eff;
        font-weight: bold;
      }

      .filter-info {
        color: #909399;
        font-style: italic;
      }
    }
  }

  .empty-data {
    padding: 40px 0;
    text-align: center;
  }

  .image-upload-container {
    display: flex;
    align-items: flex-start;
    gap: 20px;
  }

  .image-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 50%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }
  }

  .upload-image {
    width: 200px;
    height: 120px;
    object-fit: cover;
    border-radius: 4px;
  }

  .upload-icon {
    font-size: 28px;
    color: #8c939d;
    margin-bottom: 8px;
  }

  .upload-text {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }

  .image-upload-tips {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-top: 10px;

    .tip-item {
      display: flex;
      align-items: flex-start;
      gap: 6px;
      font-size: 13px;
      color: #666;
      line-height: 1.4;

      i {
        color: #409eff;
        font-size: 14px;
        margin-top: 1px;
      }
    }
  }

  .history-item {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
      background-color: #ecf5ff;
    }

    .el-icon {
      margin-right: 8px;
      color: #909399;
    }
  }
}
</style>
