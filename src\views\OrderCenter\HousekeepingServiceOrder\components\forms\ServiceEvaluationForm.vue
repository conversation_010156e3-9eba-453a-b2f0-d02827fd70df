<!--
  页面名称：服务评价表单
  功能描述：新增/编辑家政服务订单的服务评价
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="isEdit ? '编辑评价' : '订单评价'"
    direction="rtl"
    size="50%"
    :before-close="handleClose"
  >
    <div class="form-container">
      <div class="form-section">
        <h3 class="section-title">服务评分</h3>

        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
          <el-form-item label="总体满意度" prop="overallRating" required>
            <el-rate v-model="form.overallRating" />
          </el-form-item>

          <el-form-item label="评价标签" prop="tags">
            <el-input
              v-model="form.tags"
              placeholder="多个请用英文逗号,隔开"
              type="textarea"
              :rows="2"
            />
            <div class="form-tip">多个标签请用英文逗号分隔，如：非常专业,准时到达,清洁彻底</div>
          </el-form-item>

          <el-form-item label="详细评价" prop="comment" required>
            <el-input
              v-model="form.comment"
              type="textarea"
              :rows="6"
              placeholder="请详细描述您的服务体验"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">提交评价</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  visible: boolean
  orderId?: string
  evaluationData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: '',
  evaluationData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data?: any]
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()
const isEdit = ref(false)

// 表单数据
const form = reactive({
  overallRating: 5,
  tags: '',
  comment: ''
})

// 表单校验规则
const rules: FormRules = {
  overallRating: [{ required: true, message: '请选择总体满意度', trigger: 'change' }],
  comment: [{ required: true, message: '请输入详细评价', trigger: 'blur' }]
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.evaluationData) {
        // 编辑模式
        isEdit.value = true
        form.overallRating = props.evaluationData.overallRating
        form.tags = Array.isArray(props.evaluationData.tags)
          ? props.evaluationData.tags.join(',')
          : props.evaluationData.tags || ''
        form.comment = props.evaluationData.comment
      } else {
        // 新增模式
        isEdit.value = false
        resetForm()
      }
    }
  }
)

// 监听evaluationData变化
watch(
  () => props.evaluationData,
  (newVal) => {
    if (newVal && props.visible) {
      isEdit.value = true
      form.overallRating = newVal.overallRating
      form.tags = Array.isArray(newVal.tags) ? newVal.tags.join(',') : newVal.tags || ''
      form.comment = newVal.comment
    }
  }
)

const resetForm = () => {
  form.overallRating = 5
  form.tags = ''
  form.comment = ''
  formRef.value?.clearValidate()
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
  isEdit.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 构建提交的数据
    const submitData = {
      overallRating: form.overallRating,
      tags: form.tags.split(',').filter((tag) => tag.trim()),
      comment: form.comment,
      evaluationTime: new Date().toLocaleString('zh-CN')
    }

    console.log('服务评价表单提交数据:', submitData)
    ElMessage.success(isEdit.value ? '评价更新成功' : '评价提交成功')
    emit('success', submitData)
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.form-container {
  padding: 20px;

  .form-section {
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
    }

    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
}

.drawer-footer {
  text-align: right;
}
</style>
