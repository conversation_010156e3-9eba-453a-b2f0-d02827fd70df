<!--
  页面名称：收款确认对话框
  功能描述：确认订单收款，支持输入收款金额、收款方式、收款日期、收款备注，包含金额有效性验证
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="收款确认"
    width="500px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="payment-confirm-content">
      <!-- 订单金额显示 -->
      <div class="order-amount-section">
        <div class="amount-label">订单金额</div>
        <div class="amount-value">¥{{ orderData?.orderAmount || '0' }}</div>
        <div class="amount-tips">
          <el-icon><InfoFilled /></el-icon>
          <span>收款金额范围：¥{{ minAmount }} - ¥{{ maxAmount }}</span>
        </div>
      </div>

      <!-- 收款表单 -->
      <el-form :model="paymentForm" :rules="paymentRules" ref="paymentFormRef" label-width="120px">
        <el-form-item label="实际收款金额" prop="actualAmount" required>
          <el-input
            v-model="paymentForm.actualAmount"
            placeholder="请输入实际收款金额"
            type="text"
            @input="handleAmountInput"
            @blur="validateAmount"
            :class="{ 'is-error': amountError }"
          >
            <template #prepend>¥</template>
          </el-input>
          <div v-if="amountError" class="error-message">{{ amountError }}</div>
          <div v-if="!amountError && paymentForm.actualAmount" class="success-message">
            <el-icon><CircleCheck /></el-icon>
            <span>金额有效</span>
          </div>
          <div v-if="!amountError && !paymentForm.actualAmount" class="tips-message">
            <el-icon><InfoFilled /></el-icon>
            <span>请输入收款金额，范围：¥{{ minAmount }} - ¥{{ maxAmount }}</span>
          </div>
        </el-form-item>

        <el-form-item label="收款方式" prop="paymentMethod" required>
          <el-select
            v-model="paymentForm.paymentMethod"
            placeholder="请选择收款方式"
            style="width: 100%"
          >
            <el-option label="银行转账" value="bank_transfer" />
            <el-option label="支付宝" value="alipay" />
            <el-option label="微信支付" value="wechat_pay" />
            <el-option label="现金" value="cash" />
            <el-option label="支票" value="check" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="收款日期" prop="paymentDate" required>
          <el-date-picker
            v-model="paymentForm.paymentDate"
            type="date"
            placeholder="请选择收款日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="收款备注" prop="paymentNotes">
          <el-input
            v-model="paymentForm.paymentNotes"
            type="textarea"
            :rows="3"
            placeholder="请输入收款备注(可选)..."
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="confirming"
          :disabled="!isFormValid"
        >
          确认收款
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Money, InfoFilled, CircleCheck } from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: [paymentData: any]
}>()

// 响应式数据
const paymentFormRef = ref()
const confirming = ref(false)
const amountError = ref('')

// 收款表单数据
const paymentForm = ref({
  actualAmount: '',
  paymentMethod: '',
  paymentDate: '',
  paymentNotes: ''
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 金额范围计算
const orderAmount = computed(() => {
  const amount = parseFloat(props.orderData?.orderAmount?.replace(/[,，]/g, '') || '0')
  return isNaN(amount) ? 0 : amount
})

const minAmount = computed(() => {
  // 最小金额为订单金额的0.1倍，但不小于1元，如果订单金额为0则最小为1元
  if (orderAmount.value === 0) {
    return '1.00'
  }
  return Math.max(orderAmount.value * 0.1, 1).toFixed(2)
})

const maxAmount = computed(() => {
  // 最大金额为订单金额的1.5倍，如果订单金额为0则最大为1000元
  if (orderAmount.value === 0) {
    return '1000.00'
  }
  return (orderAmount.value * 1).toFixed(2)
})

// 表单是否有效
const isFormValid = computed(() => {
  return (
    paymentForm.value.actualAmount &&
    paymentForm.value.paymentMethod &&
    paymentForm.value.paymentDate &&
    !amountError.value
  )
})

// 表单校验规则
const paymentRules = {
  actualAmount: [
    { required: true, message: '请输入实际收款金额', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value === '' || value === null || value === undefined) {
          callback(new Error('请输入实际收款金额'))
          return
        }

        // 先清理金额格式，移除逗号等字符
        const cleanValue = value.replace(/[,，]/g, '')

        // 检查是否包含非法字符（只允许数字和小数点）
        if (!/^\d+(\.\d{0,2})?$/.test(cleanValue)) {
          callback(new Error('请输入有效的金额格式（最多2位小数）'))
          return
        }

        const numValue = parseFloat(cleanValue)
        if (isNaN(numValue)) {
          callback(new Error('请输入有效的数字'))
          return
        }

        if (numValue <= 0) {
          callback(new Error('收款金额必须大于0'))
          return
        }

        if (numValue < parseFloat(minAmount.value)) {
          callback(new Error(`收款金额不能小于¥${minAmount.value}`))
          return
        }

        if (numValue > parseFloat(maxAmount.value)) {
          callback(new Error(`收款金额不能超过¥${maxAmount.value}`))
          return
        }

        callback()
      },
      trigger: 'blur'
    }
  ],
  paymentMethod: [{ required: true, message: '请选择收款方式', trigger: 'change' }],
  paymentDate: [{ required: true, message: '请选择收款日期', trigger: 'change' }]
}

// 监听订单数据变化，初始化表单
watch(
  () => props.orderData,
  (newOrderData) => {
    if (newOrderData) {
      // 处理订单金额格式，移除逗号等字符
      const orderAmountStr = newOrderData.orderAmount?.toString() || ''
      const cleanAmount = orderAmountStr.replace(/[,，]/g, '')
      paymentForm.value.actualAmount = cleanAmount
      paymentForm.value.paymentDate = new Date().toISOString().split('T')[0]
      // 清除之前的错误信息
      amountError.value = ''

      // 延迟验证，确保金额格式已经处理完成
      nextTick(() => {
        if (cleanAmount) {
          validateAmount()
        }
      })
    }
  },
  { immediate: true }
)

// 监听金额变化，实时验证
watch(
  () => paymentForm.value.actualAmount,
  (newValue) => {
    if (newValue) {
      validateAmount()
    } else {
      amountError.value = ''
    }
  }
)

// 方法
/** 处理关闭 */
const handleClose = () => {
  paymentForm.value = {
    actualAmount: '',
    paymentMethod: '',
    paymentDate: '',
    paymentNotes: ''
  }
  amountError.value = ''
  visible.value = false
}

/** 处理金额输入 */
const handleAmountInput = (value: string) => {
  // 只允许输入数字和小数点
  const filteredValue = value.replace(/[^\d.]/g, '')
  // 确保只有一个小数点
  const parts = filteredValue.split('.')
  if (parts.length > 2) {
    paymentForm.value.actualAmount = parts[0] + '.' + parts.slice(1).join('')
  } else {
    paymentForm.value.actualAmount = filteredValue
  }

  // 限制小数位数为2位
  if (parts.length === 2 && parts[1].length > 2) {
    paymentForm.value.actualAmount = parts[0] + '.' + parts[1].substring(0, 2)
  }
}

/** 验证金额 */
const validateAmount = () => {
  const value = paymentForm.value.actualAmount
  if (!value) {
    amountError.value = ''
    return
  }

  // 先清理金额格式，移除逗号等字符
  const cleanValue = value.replace(/[,，]/g, '')

  // 检查是否包含非法字符（只允许数字和小数点）
  if (!/^\d+(\.\d{0,2})?$/.test(cleanValue)) {
    amountError.value = '请输入有效的金额格式（最多2位小数）'
    return
  }

  const numValue = parseFloat(cleanValue)
  if (isNaN(numValue)) {
    amountError.value = '请输入有效的数字'
    return
  }

  if (numValue <= 0) {
    amountError.value = '收款金额必须大于0'
    return
  }

  if (numValue < parseFloat(minAmount.value)) {
    amountError.value = `收款金额不能小于¥${minAmount.value}`
    return
  }

  if (numValue > parseFloat(maxAmount.value)) {
    amountError.value = `收款金额不能超过¥${maxAmount.value}`
    return
  }

  amountError.value = ''
}

/** 确认收款 */
const handleConfirm = async () => {
  if (!paymentFormRef.value) return

  try {
    // 先验证金额
    validateAmount()
    if (amountError.value) {
      ElMessage.error(amountError.value)
      return
    }

    await paymentFormRef.value.validate()
    confirming.value = true

    // 构建收款数据
    const paymentData = {
      orderId: props.orderData?.id,
      orderNumber: props.orderData?.orderNumber,
      actualAmount: parseFloat(paymentForm.value.actualAmount),
      paymentMethod: paymentForm.value.paymentMethod,
      paymentDate: paymentForm.value.paymentDate,
      paymentNotes: paymentForm.value.paymentNotes
    }

    // 触发确认事件
    emit('confirm', paymentData)

    ElMessage.success('收款确认成功')
    handleClose()
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单信息')
  } finally {
    confirming.value = false
  }
}
</script>

<style scoped lang="scss">
.payment-confirm-content {
  .order-amount-section {
    text-align: center;
    margin-bottom: 24px;
    padding: 20px;
    background: #f0f9ff;
    border-radius: 8px;
    border: 1px solid #e0f2fe;

    .amount-label {
      color: #666;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .amount-value {
      color: #409eff;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .amount-tips {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      color: #909399;
      font-size: 12px;

      .el-icon {
        font-size: 12px;
      }
    }
  }

  .el-form {
    .el-form-item {
      margin-bottom: 20px;
    }

    .error-message {
      color: #f56c6c;
      font-size: 12px;
      margin-top: 4px;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .success-message {
      color: #67c23a;
      font-size: 12px;
      margin-top: 4px;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .tips-message {
      color: #909399;
      font-size: 12px;
      margin-top: 4px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-input.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c inset;
}
</style>
