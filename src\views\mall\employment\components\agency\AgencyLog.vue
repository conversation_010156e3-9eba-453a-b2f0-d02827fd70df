<!--
  页面名称：机构沟通日志
  功能描述：显示机构与平台的沟通记录，包括通话记录、消息记录等
-->
<template>
  <div class="agency-log">
    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item label="沟通类型">
          <el-select v-model="filterForm.type" placeholder="请选择沟通类型" clearable>
            <el-option label="全部" value="" />
            <el-option label="通话记录" value="call" />
            <el-option label="消息记录" value="message" />
            <el-option label="邮件记录" value="email" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 沟通记录列表 -->
    <div class="log-list">
      <el-timeline>
        <el-timeline-item
          v-for="log in logList"
          :key="log.id"
          :timestamp="log.createTime"
          :type="getTimelineType(log.type)"
        >
          <el-card class="log-card">
            <div class="log-header">
              <div class="log-type">
                <el-tag :type="getTagType(log.type)" size="small">
                  {{ getTypeText(log.type) }}
                </el-tag>
              </div>
              <div class="log-time">{{ formatTime(log.createTime) }}</div>
            </div>

            <div class="log-content">
              <div class="log-title">
                <strong>{{ log.title }}</strong>
              </div>
              <div class="log-description">{{ log.description }}</div>

              <!-- 通话记录详情 -->
              <div v-if="log.type === 'call'" class="call-details">
                <div class="detail-item">
                  <span class="label">通话时长：</span>
                  <span>{{ log.duration }}分钟</span>
                </div>
                <div class="detail-item">
                  <span class="label">通话状态：</span>
                  <el-tag :type="log.status === 'success' ? 'success' : 'danger'" size="small">
                    {{ log.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </div>
              </div>

              <!-- 消息记录详情 -->
              <div v-if="log.type === 'message'" class="message-details">
                <div class="detail-item">
                  <span class="label">消息类型：</span>
                  <span>{{ log.messageType }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">消息内容：</span>
                  <span>{{ log.content }}</span>
                </div>
              </div>

              <!-- 邮件记录详情 -->
              <div v-if="log.type === 'email'" class="email-details">
                <div class="detail-item">
                  <span class="label">邮件主题：</span>
                  <span>{{ log.subject }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">收件人：</span>
                  <span>{{ log.recipient }}</span>
                </div>
              </div>
            </div>

            <div class="log-footer">
              <div class="log-operator">
                <span class="label">操作人：</span>
                <span>{{ log.operator }}</span>
              </div>
              <div class="log-actions">
                <el-button type="text" size="small" @click="handleViewDetail(log)">
                  查看详情
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="handleReply(log)"
                  v-if="log.type === 'message'"
                >
                  回复
                </el-button>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="沟通记录详情"
      width="600px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentLog" class="log-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-row">
            <span class="label">记录类型：</span>
            <el-tag :type="getTagType(currentLog.type)">
              {{ getTypeText(currentLog.type) }}
            </el-tag>
          </div>
          <div class="detail-row">
            <span class="label">创建时间：</span>
            <span>{{ formatTime(currentLog.createTime) }}</span>
          </div>
          <div class="detail-row">
            <span class="label">操作人：</span>
            <span>{{ currentLog.operator }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>详细内容</h4>
          <div class="detail-content">{{ currentLog.description }}</div>
        </div>

        <div
          v-if="currentLog.attachments && currentLog.attachments.length > 0"
          class="detail-section"
        >
          <h4>附件</h4>
          <div class="attachment-list">
            <div
              v-for="attachment in currentLog.attachments"
              :key="attachment.id"
              class="attachment-item"
            >
              <el-link :href="attachment.url" target="_blank">
                <i class="el-icon-document"></i>
                {{ attachment.name }}
              </el-link>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDetail">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

// 定义接口类型
interface LogItem {
  id: string
  type: 'call' | 'message' | 'email'
  title: string
  description: string
  createTime: string
  operator: string
  status?: string
  duration?: number
  messageType?: string
  content?: string
  subject?: string
  recipient?: string
  attachments?: Array<{
    id: string
    name: string
    url: string
  }>
}

interface FilterForm {
  type: string
  dateRange: string[]
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

// 响应式数据
const filterForm = reactive<FilterForm>({
  type: '',
  dateRange: []
})

const logList = ref<LogItem[]>([])
const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const detailDialogVisible = ref(false)
const currentLog = ref<LogItem | null>(null)

// 获取沟通记录列表
const getLogList = async () => {
  try {
    // 模拟API调用
    const mockData: LogItem[] = [
      {
        id: '1',
        type: 'call',
        title: '客服回访电话',
        description: '客服小王对机构进行回访，了解服务情况',
        createTime: '2024-01-15 14:30:00',
        operator: '小王',
        status: 'success',
        duration: 15
      },
      {
        id: '2',
        type: 'message',
        title: '系统通知消息',
        description: '系统发送服务到期提醒消息',
        createTime: '2024-01-14 10:00:00',
        operator: '系统',
        messageType: '通知',
        content: '您的服务将于2024年2月1日到期，请及时续费。'
      },
      {
        id: '3',
        type: 'email',
        title: '合同确认邮件',
        description: '发送合同确认邮件给机构负责人',
        createTime: '2024-01-13 16:45:00',
        operator: '小李',
        subject: '服务合同确认',
        recipient: '<EMAIL>'
      }
    ]

    logList.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    console.error('获取沟通记录失败:', error)
    ElMessage.error('获取沟通记录失败')
  }
}

// 获取时间线类型
const getTimelineType = (type: string) => {
  switch (type) {
    case 'call':
      return 'primary'
    case 'message':
      return 'success'
    case 'email':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取标签类型
const getTagType = (type: string) => {
  switch (type) {
    case 'call':
      return 'primary'
    case 'message':
      return 'success'
    case 'email':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取类型文本
const getTypeText = (type: string) => {
  switch (type) {
    case 'call':
      return '通话记录'
    case 'message':
      return '消息记录'
    case 'email':
      return '邮件记录'
    default:
      return '其他'
  }
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 查询
const handleSearch = () => {
  pagination.currentPage = 1
  getLogList()
}

// 重置
const handleReset = () => {
  filterForm.type = ''
  filterForm.dateRange = []
  pagination.currentPage = 1
  getLogList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getLogList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  getLogList()
}

// 查看详情
const handleViewDetail = (log: LogItem) => {
  currentLog.value = log
  detailDialogVisible.value = true
}

// 回复消息
const handleReply = (log: LogItem) => {
  ElMessage.info('回复功能开发中...')
}

// 关闭详情弹窗
const handleCloseDetail = () => {
  detailDialogVisible.value = false
  currentLog.value = null
}

// 生命周期
onMounted(() => {
  getLogList()
})
</script>

<style scoped lang="scss">
.agency-log {
  padding: 20px;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.log-list {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.log-card {
  margin-bottom: 10px;

  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .log-time {
      color: #999;
      font-size: 12px;
    }
  }

  .log-content {
    .log-title {
      margin-bottom: 8px;
      color: #333;
    }

    .log-description {
      color: #666;
      margin-bottom: 10px;
      line-height: 1.5;
    }

    .call-details,
    .message-details,
    .email-details {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 10px;

      .detail-item {
        display: flex;
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: #666;
          min-width: 80px;
        }
      }
    }
  }

  .log-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;

    .log-operator {
      color: #999;
      font-size: 12px;

      .label {
        margin-right: 5px;
      }
    }

    .log-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.log-detail {
  .detail-section {
    margin-bottom: 20px;

    h4 {
      margin-bottom: 10px;
      color: #333;
      font-size: 16px;
    }

    .detail-row {
      display: flex;
      margin-bottom: 8px;

      .label {
        font-weight: 500;
        color: #666;
        min-width: 100px;
      }
    }

    .detail-content {
      color: #666;
      line-height: 1.6;
      background: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
    }

    .attachment-list {
      .attachment-item {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
