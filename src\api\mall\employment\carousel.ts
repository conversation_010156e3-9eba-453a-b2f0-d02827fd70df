import request from '@/config/axios'

/**
 * 轮播图管理API接口
 */

// 轮播图信息接口
export interface Carousel {
  id: number
  carouselTitle: string
  carouselImageUrl: string
  carouselLinkUrl: string
  sortOrder: number
  status: number // 1-启用，0-禁用
  platform: string // employer-雇主端，aunt-阿姨端
  createTime: string
  updateTime?: string
}

// 查询参数接口
export interface CarouselQueryParams {
  page: number
  size: number
  carouselTitle?: string
  status?: number
  platform?: string
}

// 新增轮播图参数接口
export interface CreateCarouselParams {
  platform: string
  carouselTitle: string
  carouselImageUrl: string
  carouselLinkUrl?: string
  sortOrder?: number
  status?: number
}

// 更新轮播图参数接口
export interface UpdateCarouselParams {
  id: number
  platform?: string
  carouselTitle?: string
  carouselImageUrl?: string
  carouselLinkUrl?: string
  sortOrder?: number
  status?: number
}

// 更新状态参数接口
export interface UpdateStatusParams {
  id: number
  status: number
}

// 分页响应接口
export interface CarouselPageResponse {
  list: Carousel[]
  total: number
  page: number
  size: number
}

/**
 * 分页查询轮播图列表
 * @param params 查询参数
 * @returns Promise<any> - 兼容不同的接口返回格式
 */
export function getCarouselList(params: CarouselQueryParams) {
  return request.get({
    url: '/publicbiz/carousel/page',
    params
  })
}

/**
 * 新增轮播图
 * @param data 轮播图信息
 * @returns Promise<{ id: number }>
 */
export function createCarousel(data: CreateCarouselParams) {
  return request.post({
    url: '/publicbiz/carousel/create',
    data
  })
}

/**
 * 更新轮播图
 * @param data 更新信息
 * @returns Promise<void>
 */
export function updateCarousel(data: UpdateCarouselParams) {
  return request.put({
    url: '/publicbiz/carousel/update',
    data
  })
}

/**
 * 删除轮播图
 * @param id 轮播图ID
 * @returns Promise<void>
 */
export function deleteCarousel(id: number) {
  return request.delete({
    url: `/publicbiz/carousel/delete/${id}`
  })
}

/**
 * 更新轮播图状态
 * @param data 状态更新参数
 * @returns Promise<void>
 */
export function updateCarouselStatus(data: UpdateStatusParams) {
  return request.put({
    url: '/publicbiz/carousel/updateStatus',
    data
  })
}

/**
 * 更新轮播图排序
 * @param id 轮播图ID
 * @param sortOrder 排序
 * @returns Promise<void>
 */
export function updateCarouselSort(id: string, sortOrder: number) {
  return request.put({
    url: `/mall/employment/carousel/${id}/sort`,
    data: { sortOrder }
  })
}

/**
 * 批量更新轮播图排序
 * @param sortData 排序数据
 * @returns Promise<void>
 */
export function batchUpdateCarouselSort(sortData: Array<{ id: string; sortOrder: number }>) {
  return request.put({
    url: '/mall/employment/carousel/batch-sort',
    data: { sortData }
  })
}
