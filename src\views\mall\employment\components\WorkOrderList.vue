<template>
  <div class="work-order-list">
    <h2>工单列表</h2>
    <el-button type="primary" @click="fetchWorkOrderList">获取工单列表</el-button>
    <el-table :data="tableData" style="width: 100%" border v-loading="loading">
      <el-table-column prop="workOrderNo" label="工单号" />
      <el-table-column prop="workOrderTitle" label="工单标题" />
      <el-table-column prop="workOrderType" label="工单类型" />
      <el-table-column prop="workOrderStatus" label="工单状态" />
      <el-table-column prop="createTime" label="创建时间" />
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getWorkOrderPage } from '@/api/mall/employment/workOrder'

const tableData = ref([])
const loading = ref(false)

const fetchWorkOrderList = async () => {
  loading.value = true
  try {
    const params = { page: 1, size: 10 }
    const res = await getWorkOrderPage(params)
    if (res.code === 200) {
      tableData.value = res.data || []
    }
  } catch (error) {
    console.error('获取工单列表失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchWorkOrderList()
})
</script>

<style scoped>
.work-order-list {
  padding: 20px;
}
</style> 