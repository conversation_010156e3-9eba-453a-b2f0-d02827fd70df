<!--
  页面名称：师资操作日志
  功能描述：展示讲师的操作日志，右侧抽屉，时间线样式，支持关闭
-->
<template>
  <el-drawer
    v-model="visible"
    :title="`'${teacherName}' 的操作日志`"
    size="600px"
    :with-header="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="$emit('close')"
  >
    <div class="teacher-optlog-timeline-wrap">
      <el-timeline>
        <el-timeline-item
          v-for="(item, idx) in logs"
          :key="idx"
          :timestamp="formatTime(item.createTime)"
          placement="top"
        >
          <div class="teacher-optlog-item">
            <div class="teacher-optlog-meta">
              <span class="teacher-optlog-user">{{ item.userName || '系统' }}</span>
              <span class="teacher-optlog-type">{{ item.type }} - {{ item.subType }}</span>
            </div>
            <div class="teacher-optlog-content">{{ item.action }}</div>
            <div class="teacher-optlog-extra" v-if="item.extra">
              <span class="extra-label">额外信息：</span>
              <span class="extra-content">{{ item.extra }}</span>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
      <div v-if="logs.length === 0" class="empty-logs">
        <el-empty description="暂无操作日志" />
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const visible = ref(false)
const teacherName = ref('')
const logs = ref([])

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 对外暴露打开方法，支持传入讲师姓名和日志数据
const open = (name: string, logList?: any[]) => {
  teacherName.value = name
  logs.value = logList || []
  visible.value = true
}

defineExpose({ open })
</script>

<style scoped lang="scss">
.teacher-optlog-timeline-wrap {
  padding: 24px 0 0 0;
}
.teacher-optlog-item {
  background: #f7fafd;
  border-radius: 8px;
  padding: 12px 18px;
  margin-bottom: 8px;
}
.teacher-optlog-meta {
  color: #888;
  font-size: 13px;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.teacher-optlog-user {
  font-weight: bold;
  margin-right: 8px;
}
.teacher-optlog-type {
  color: #666;
  font-size: 12px;
}
.teacher-optlog-content {
  font-size: 15px;
  color: #222;
  margin-bottom: 4px;
}
.teacher-optlog-extra {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
.extra-label {
  font-weight: bold;
}
.extra-content {
  color: #666;
}
.empty-logs {
  padding: 40px 0;
  text-align: center;
}
</style>
