import request from '@/config/axios'
import qs from 'qs'

// 视频VO
export interface VideoVO {
  id: number
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId: number
  description?: string
  createTime?: string
  visibleOrgId?: number
  visibleOrgName?: string
}

// 视频列表
export const getVideoList = (params: {
  pageNo: number
  pageSize: number
  name?: string
  sourceOrgId?: number
  categoryId?: number
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.get({ url: '/system/material/video/list', params })
}

// 新增视频
export const createVideo = (data: {
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/video/create', data })
}

// 编辑视频
export const updateVideo = (data: {
  id: number
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/video/update', data })
}

// 删除视频
export const deleteVideo = (id: number) => {
  return request.post({
    url: '/system/material/video/delete',
    data: qs.stringify({ id }),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 视频详情
export const getVideoDetail = (id: number) => {
  return request.get({ url: '/system/material/video/detail', params: { id } })
}
