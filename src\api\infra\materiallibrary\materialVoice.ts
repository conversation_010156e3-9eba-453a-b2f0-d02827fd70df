import request from '@/config/axios'
import qs from 'qs'

// 音频VO
export interface VoiceVO {
  id: number
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId: number
  description?: string
  createTime?: string
  visibleOrgId?: number
  visibleOrgName?: string
  duration?: number // 音频时长（秒）
  bitrate?: number // 比特率
  format?: string // 音频格式
}

// 音频列表
export const getVoiceList = (params: {
  pageNo: number
  pageSize: number
  name?: string
  sourceOrgId?: number
  categoryId?: number
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.get({ url: '/system/material/voice/list', params })
}

// 新增音频
export const createVoice = (data: {
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
  duration?: number
  bitrate?: number
  format?: string
}) => {
  return request.post({ url: '/system/material/voice/create', data })
}

// 编辑音频
export const updateVoice = (data: {
  id: number
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
  duration?: number
  bitrate?: number
  format?: string
}) => {
  return request.post({ url: '/system/material/voice/update', data })
}

// 删除音频
export const deleteVoice = (id: number) => {
  return request.post({
    url: '/system/material/voice/delete',
    data: qs.stringify({ id }),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 音频详情
export const getVoiceDetail = (id: number) => {
  return request.get({ url: '/system/material/voice/detail', params: { id } })
}
