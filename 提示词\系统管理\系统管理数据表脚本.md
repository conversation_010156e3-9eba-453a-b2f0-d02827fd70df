-- 用户信息表 CREATE TABLE `system_users` ( `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID', `account_type` tinyint(1) NOT NULL DEFAULT '2' COMMENT '账户类型，1-内部员工，2-企业用户', `username` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户账号', `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码', `nickname` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户昵称', `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注', `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID', `post_ids` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '岗位编号数组', `email` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '用户邮箱', `mobile` varchar(11) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '手机号码', `sex` tinyint(4) DEFAULT '0' COMMENT '用户性别', `avatar` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '头像地址', `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）', `work_status_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '工作状态类型，1-全职，2-兼职，3-其它', `login_ip` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '最后登录IP', `login_date` datetime DEFAULT NULL COMMENT '最后登录时间', `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者', `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者', `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除', `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号', PRIMARY KEY (`id`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=143 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 为 system_users 表添加合作伙伴相关字段 -- 添加所属合作伙伴ID字段 ALTER TABLE `system_users` ADD COLUMN `partner_id` bigint(20) DEFAULT NULL COMMENT '所属合作伙伴ID' AFTER `work_status_type`;

-- 添加所属合作伙伴名称字段 ALTER TABLE `system_users` ADD COLUMN `partner_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属合作伙伴名称' AFTER `partner_id`;

-- 为 partner_id 字段添加索引以提高查询性能 ALTER TABLE `system_users` ADD INDEX `idx_partner_id` (`partner_id`) USING BTREE;
