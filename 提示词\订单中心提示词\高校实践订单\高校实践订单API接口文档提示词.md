---
---

description: globs: alwaysApply: false

---

提示词请分析以下高校实践订单相关文件：

1. 数据库表结构：`提示词/订单中心提示词/高校实践订单/高校实践订单数据库表结构.sql`
2. 前端页面文件：
   - 主页面：`src/views/OrderCenter/UniversityPracticeCenter/index.vue`
   - 新增高校实践订单组件：`src/views/OrderCenter/UniversityPracticeCenter/components/AddIndividualtrainingOrder.vue`
   - 查看高校实践订单组件：`src/views/OrderCenter/UniversityPracticeCenter/components/IndividualtrainingOrderView.vue`
   - 查看操作日志组件：`src/views/OrderCenter/UniversityPracticeCenter/components/OptLog.vue`

基于这些文件的内容，请为我生成一份完整的高校实践订单API接口文档，文档需要包含：

**文档结构要求：**

1. 接口概述和基础信息（基础路径、数据格式、统一响应格式）

2. 接口必须包含一个动词指示该接口的功能，例如page/create/update/delete，不要RESTfull API风格

3. 接口中的字段名或参考名与表结构中的字段名保持一致，并且接口中的字段都采用驼峰的命名方式

4. 每个接口的详细说明，包括：
   - 接口地址和请求方式
   - 功能说明
   - 请求参数表格（参数名、类型、必填、说明）
   - 响应字段表格（字段名、类型、说明）
   - 完整的请求示例（包含URL和JSON数据）
   - 完整的返回示例（JSON格式）
5. 数据字典（枚举值说明）;
6. 错误码说明;

**接口范围：**

- 高校实践订单相关接口（增删改查、分页查询、导出等）
- 上传纸质合同/上传电子合同
- 下载合同附件功能
- 高校实践订单发起审批功能
- 高校实践订单操作日志功能
- 其他从前端代码中识别出的相关接口

## 请确保文档格式规范、内容完整，便于开发者理解和对接使用。

---

# 高校实践订单API接口文档

## 1. 接口概述和基础信息

### 1.1 基础信息

- **基础路径**: `/publicbiz/order`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token

### 1.2 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-06-21T10:30:00Z"
}
```

### 1.3 分页响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [],
    "total": 100,
    "page": 1,
    "size": 10
  },
  "timestamp": "2024-06-21T10:30:00Z"
}
```

## 2. 接口详细说明

### 2.1 分页查询高校实践订单

**接口地址**: `GET /publicbiz/order/page`

**功能说明**: 分页查询高校实践订单列表，支持多条件筛选

**请求参数**:

| 参数名        | 类型    | 必填 | 说明                               |
| ------------- | ------- | ---- | ---------------------------------- |
| page          | Integer | 是   | 页码，从1开始                      |
| size          | Integer | 是   | 每页大小，最大100                  |
| orderStatus   | String  | 否   | 订单状态筛选                       |
| paymentStatus | String  | 否   | 支付状态筛选                       |
| keyword       | String  | 否   | 关键词搜索（项目名称、高校、企业） |
| startDate     | String  | 否   | 开始日期（YYYY-MM-DD）             |
| endDate       | String  | 否   | 结束日期（YYYY-MM-DD）             |
| managerId     | Long    | 否   | 负责人ID筛选                       |

**响应字段**:

| 字段名         | 类型    | 说明         |
| -------------- | ------- | ------------ |
| id             | Long    | 订单ID       |
| orderNo        | String  | 订单号       |
| projectName    | String  | 项目名称     |
| universityName | String  | 合作高校名称 |
| enterpriseName | String  | 合作企业名称 |
| startDate      | String  | 开始日期     |
| endDate        | String  | 结束日期     |
| managerName    | String  | 负责人姓名   |
| totalAmount    | Decimal | 订单总金额   |
| orderStatus    | String  | 订单状态     |
| paymentStatus  | String  | 支付状态     |
| createTime     | String  | 创建时间     |

**请求示例**:

```http
GET /publicbiz/order/page?page=1&size=10&orderStatus=pending_approval&keyword=暑期实践
```

**返回示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "orderNo": "HP202406001",
      "projectName": "2024年暑期社会实践项目",
      "universityName": "XX大学经济管理学院",
      "enterpriseName": "ABC科技有限公司",
      "startDate": "2024-07-01",
      "endDate": "2024-08-30",
      "managerName": "张三",
      "totalAmount": 580000.0,
      "orderStatus": "pending_approval",
      "paymentStatus": "pending",
      "createTime": "2024-06-15 10:30:00"
    }
  ]
}
```

### 2.2 创建高校实践订单

**接口地址**: `POST /publicbiz/order/create`

**功能说明**: 创建新的高校实践订单

**请求参数**:

| 参数名             | 类型    | 必填 | 说明                                        |
| ------------------ | ------- | ---- | ------------------------------------------- |
| leadId             | String  | 否   | 关联线索ID                                  |
| projectName        | String  | 是   | 项目名称                                    |
| universityName     | String  | 是   | 合作高校名称                                |
| universityContact  | String  | 否   | 高校联系人                                  |
| universityPhone    | String  | 否   | 高校联系电话                                |
| universityEmail    | String  | 否   | 高校联系邮箱                                |
| enterpriseName     | String  | 是   | 合作企业名称                                |
| enterpriseContact  | String  | 否   | 企业联系人                                  |
| enterprisePhone    | String  | 否   | 企业联系电话                                |
| enterpriseEmail    | String  | 否   | 企业联系邮箱                                |
| startDate          | String  | 是   | 开始日期（YYYY-MM-DD）                      |
| endDate            | String  | 是   | 结束日期（YYYY-MM-DD）                      |
| studentCount       | Integer | 否   | 参与学生人数                                |
| practiceDuration   | String  | 否   | 实践时长                                    |
| practiceLocation   | String  | 否   | 实践地点                                    |
| serviceFee         | Decimal | 否   | 服务费                                      |
| managementFee      | Decimal | 否   | 管理费                                      |
| otherFee           | Decimal | 否   | 其他费用                                    |
| totalAmount        | Decimal | 是   | 订单总金额                                  |
| managerId          | Long    | 是   | 负责人ID                                    |
| managerName        | String  | 是   | 负责人姓名                                  |
| managerPhone       | String  | 否   | 负责人电话                                  |
| projectDescription | String  | 否   | 项目描述                                    |
| contractType       | String  | 否   | 合同类型（electronic/paper）                |
| remark             | String  | 否   | 备注                                        |
| paymentStatus      | String  | 否   | 支付状态（pending/paid/refunded/cancelled） |
| collectionAmount   | Decimal | 否   | 收款金额（当paymentStatus为paid时必填）     |
| collectionMethod   | String  | 否   | 收款方式（当paymentStatus为paid时必填）     |
| collectionDate     | String  | 否   | 收款日期（当paymentStatus为paid时必填）     |
| operatorName       | String  | 否   | 操作人（当paymentStatus为paid时必填）       |
| collectionRemark   | String  | 否   | 收款备注                                    |

**响应字段**:

| 字段名     | 类型   | 说明     |
| ---------- | ------ | -------- |
| id         | Long   | 订单ID   |
| orderNo    | String | 订单号   |
| createTime | String | 创建时间 |

**请求示例**:

```json
{
  "projectName": "2024年暑期社会实践项目",
  "universityName": "XX大学经济管理学院",
  "enterpriseName": "ABC科技有限公司",
  "startDate": "2024-07-01",
  "endDate": "2024-08-30",
  "totalAmount": 580000.0,
  "managerId": 1001,
  "managerName": "张三",
  "contractType": "electronic",
  "paymentStatus": "paid",
  "collectionAmount": 580000.0,
  "collectionMethod": "bank_transfer",
  "collectionDate": "2024-06-21",
  "operatorName": "李四",
  "collectionRemark": "银行转账收款"
}
```

**返回示例**:

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "orderNo": "HP202406001",
    "createTime": "2024-06-21 10:30:00"
  }
}
```

### 2.3 更新高校实践订单

**接口地址**: `POST /publicbiz/order/update`

**功能说明**: 更新高校实践订单信息

**请求参数**:

| 参数名             | 类型    | 必填 | 说明                                        |
| ------------------ | ------- | ---- | ------------------------------------------- |
| id                 | Long    | 是   | 订单ID                                      |
| orderNo            | String  | 是   | 订单号                                      |
| projectName        | String  | 否   | 项目名称                                    |
| universityName     | String  | 否   | 合作高校名称                                |
| universityContact  | String  | 否   | 高校联系人                                  |
| universityPhone    | String  | 否   | 高校联系电话                                |
| universityEmail    | String  | 否   | 高校联系邮箱                                |
| enterpriseName     | String  | 否   | 合作企业名称                                |
| enterpriseContact  | String  | 否   | 企业联系人                                  |
| enterprisePhone    | String  | 否   | 企业联系电话                                |
| enterpriseEmail    | String  | 否   | 企业联系邮箱                                |
| startDate          | String  | 否   | 开始日期（YYYY-MM-DD）                      |
| endDate            | String  | 否   | 结束日期（YYYY-MM-DD）                      |
| studentCount       | Integer | 否   | 参与学生人数                                |
| practiceDuration   | String  | 否   | 实践时长                                    |
| practiceLocation   | String  | 否   | 实践地点                                    |
| serviceFee         | Decimal | 否   | 服务费                                      |
| managementFee      | Decimal | 否   | 管理费                                      |
| otherFee           | Decimal | 否   | 其他费用                                    |
| totalAmount        | Decimal | 否   | 订单总金额                                  |
| managerId          | Long    | 否   | 负责人ID                                    |
| managerName        | String  | 否   | 负责人姓名                                  |
| managerPhone       | String  | 否   | 负责人电话                                  |
| projectDescription | String  | 否   | 项目描述                                    |
| remark             | String  | 否   | 备注                                        |
| paymentStatus      | String  | 否   | 支付状态（pending/paid/refunded/cancelled） |
| collectionAmount   | Decimal | 否   | 收款金额（当paymentStatus为paid时必填）     |
| collectionMethod   | String  | 否   | 收款方式（当paymentStatus为paid时必填）     |
| collectionDate     | String  | 否   | 收款日期（当paymentStatus为paid时必填）     |
| operatorName       | String  | 否   | 操作人（当paymentStatus为paid时必填）       |
| collectionRemark   | String  | 否   | 收款备注                                    |

**响应字段**:

| 字段名     | 类型    | 说明         |
| ---------- | ------- | ------------ |
| success    | Boolean | 更新是否成功 |
| updateTime | String  | 更新时间     |

**请求示例**:

```json
{
  "id": 1,
  "orderNo": "HP202406001",
  "projectName": "2024年暑期社会实践项目（更新）",
  "totalAmount": 600000.0,
  "paymentStatus": "paid",
  "collectionAmount": 600000.0,
  "collectionMethod": "wechat",
  "collectionDate": "2024-06-21",
  "operatorName": "王五",
  "collectionRemark": "微信支付收款"
}
```

**返回示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "success": true,
    "updateTime": "2024-06-21 11:00:00"
  }
}
```

### 2.4 删除高校实践订单

**接口地址**: `POST /publicbiz/order/delete`

**功能说明**: 删除高校实践订单（逻辑删除）

**请求参数**:

| 参数名  | 类型   | 必填 | 说明   |
| ------- | ------ | ---- | ------ |
| id      | Long   | 是   | 订单ID |
| orderNo | String | 是   | 订单号 |

**响应字段**:

| 字段名  | 类型    | 说明         |
| ------- | ------- | ------------ |
| success | Boolean | 删除是否成功 |

**请求示例**:

```json
{
  "id": 1,
  "orderNo": "HP202406001"
}
```

**返回示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": {
    "success": true
  }
}
```

### 2.5 查看高校实践订单详情

**接口地址**: `GET /publicbiz/order/detail`

**功能说明**: 获取高校实践订单详细信息

**请求参数**:

| 参数名  | 类型   | 必填 | 说明   |
| ------- | ------ | ---- | ------ |
| id      | Long   | 是   | 订单ID |
| orderNo | String | 是   | 订单号 |

**响应字段**:

| 字段名                           | 类型    | 说明             |
| -------------------------------- | ------- | ---------------- |
| id                               | Long    | 订单ID           |
| orderNo                          | String  | 订单号           |
| orderType                        | String  | 订单类型         |
| businessLine                     | String  | 业务线           |
| opportunityId                    | String  | 关联商机ID       |
| leadId                           | String  | 关联线索ID       |
| projectName                      | String  | 项目名称         |
| projectDescription               | String  | 项目描述         |
| startDate                        | String  | 开始日期         |
| endDate                          | String  | 结束日期         |
| totalAmount                      | Decimal | 订单总金额       |
| paidAmount                       | Decimal | 已支付金额       |
| refundAmount                     | Decimal | 退款金额         |
| paymentStatus                    | String  | 支付状态         |
| orderStatus                      | String  | 订单状态         |
| managerId                        | Long    | 负责人ID         |
| managerName                      | String  | 负责人姓名       |
| managerPhone                     | String  | 负责人电话       |
| contractType                     | String  | 合同类型         |
| contractFileUrl                  | String  | 合同文件URL      |
| contractStatus                   | String  | 合同状态         |
| remark                           | String  | 备注             |
| settlementStatus                 | String  | 结算状态         |
| settlementTime                   | String  | 结算时间         |
| settlementMethod                 | String  | 结算方式         |
| createTime                       | String  | 创建时间         |
| updateTime                       | String  | 更新时间         |
| collectionAmount                 | Decimal | 收款金额         |
| collectionMethod                 | String  | 收款方式         |
| collectionDate                   | String  | 收款日期         |
| operatorName                     | String  | 操作人           |
| collectionRemark                 | String  | 收款备注         |
| practiceDetail                   | Object  | 高校实践订单详情 |
| practiceDetail.universityName    | String  | 合作高校名称     |
| practiceDetail.universityContact | String  | 高校联系人       |
| practiceDetail.universityPhone   | String  | 高校联系电话     |
| practiceDetail.universityEmail   | String  | 高校联系邮箱     |
| practiceDetail.enterpriseName    | String  | 合作企业名称     |
| practiceDetail.enterpriseContact | String  | 企业联系人       |
| practiceDetail.enterprisePhone   | String  | 企业联系电话     |
| practiceDetail.enterpriseEmail   | String  | 企业联系邮箱     |
| practiceDetail.studentCount      | Integer | 参与学生人数     |
| practiceDetail.practiceDuration  | String  | 实践时长         |
| practiceDetail.practiceLocation  | String  | 实践地点         |
| practiceDetail.serviceFee        | Decimal | 服务费           |
| practiceDetail.managementFee     | Decimal | 管理费           |
| practiceDetail.otherFee          | Decimal | 其他费用         |

**请求示例**:

```http
GET /publicbiz/order/detail?id=1&orderNo=HP202406001
```

**返回示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "orderNo": "HP202406001",
    "orderType": "practice",
    "businessLine": "高校实践",
    "projectName": "2024年暑期社会实践项目",
    "projectDescription": "为XX大学经济管理学院学生提供暑期社会实践机会",
    "startDate": "2024-07-01",
    "endDate": "2024-08-30",
    "totalAmount": 580000.0,
    "paidAmount": 580000.0,
    "refundAmount": 0.0,
    "paymentStatus": "paid",
    "orderStatus": "approved",
    "managerName": "张三",
    "managerPhone": "***********",
    "contractType": "electronic",
    "contractStatus": "signed",
    "createTime": "2024-06-15 10:30:00",
    "collectionAmount": 580000.0,
    "collectionMethod": "bank_transfer",
    "collectionDate": "2024-06-21",
    "operatorName": "李四",
    "collectionRemark": "银行转账收款，已确认到账",
    "practiceDetail": {
      "universityName": "XX大学经济管理学院",
      "enterpriseName": "ABC科技有限公司",
      "studentCount": 50,
      "practiceDuration": "2个月",
      "practiceLocation": "北京市朝阳区",
      "serviceFee": 500000.0,
      "managementFee": 80000.0
    }
  }
}
```

### 2.6 上传合同附件

**接口地址**: `POST /publicbiz/order/upload-contract`

**功能说明**: 上传高校实践订单合同附件

**请求参数**:

| 参数名       | 类型   | 必填 | 说明                         |
| ------------ | ------ | ---- | ---------------------------- |
| orderId      | Long   | 是   | 订单ID                       |
| orderNo      | String | 是   | 订单号                       |
| contractType | String | 是   | 合同类型（electronic/paper） |
| file         | File   | 是   | 合同文件                     |
| remark       | String | 否   | 备注                         |

**响应字段**:

| 字段名   | 类型    | 说明             |
| -------- | ------- | ---------------- |
| success  | Boolean | 上传是否成功     |
| fileUrl  | String  | 文件URL          |
| fileName | String  | 文件名           |
| fileSize | Long    | 文件大小（字节） |

**请求示例**:

```http
POST /publicbiz/order/upload-contract
Content-Type: multipart/form-data

orderId: 1
orderNo: HP202406001
contractType: paper
file: [合同文件]
remark: 纸质合同扫描件
```

**返回示例**:

```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "success": true,
    "fileUrl": "https://example.com/contracts/HT-202406001.pdf",
    "fileName": "合同_XX大学暑期实践项目.pdf",
    "fileSize": 2048576
  }
}
```

### 2.7 下载合同附件

**接口地址**: `GET /publicbiz/order/download-contract`

**功能说明**: 下载高校实践订单合同附件

**请求参数**:

| 参数名  | 类型   | 必填 | 说明    |
| ------- | ------ | ---- | ------- |
| orderId | Long   | 是   | 订单ID  |
| orderNo | String | 是   | 订单号  |
| fileUrl | String | 是   | 文件URL |

**响应**: 文件流

**请求示例**:

```http
GET /publicbiz/order/download-contract?orderId=1&orderNo=HP202406001&fileUrl=https://example.com/contracts/HT-202406001.pdf
```

### 2.8 发起审批

**接口地址**: `POST /publicbiz/order/initiate-approval`

**功能说明**: 发起高校实践订单审批流程

**请求参数**:

| 参数名       | 类型   | 必填 | 说明                                         |
| ------------ | ------ | ---- | -------------------------------------------- |
| orderId      | Long   | 是   | 订单ID                                       |
| orderNo      | String | 是   | 订单号                                       |
| approvalType | String | 是   | 审批类型（order_approval/contract_approval） |
| priority     | String | 否   | 优先级（low/normal/high/urgent）             |
| approverIds  | Array  | 是   | 审批人ID列表                                 |
| comments     | String | 否   | 审批说明                                     |

**响应字段**:

| 字段名     | 类型    | 说明         |
| ---------- | ------- | ------------ |
| success    | Boolean | 发起是否成功 |
| approvalId | String  | 审批流程ID   |
| approvalNo | String  | 审批编号     |

**请求示例**:

```json
{
  "orderId": 1,
  "orderNo": "HP202406001",
  "approvalType": "order_approval",
  "priority": "normal",
  "approverIds": [1001, 1002],
  "comments": "请审批该高校实践订单"
}
```

**返回示例**:

```json
{
  "code": 200,
  "message": "审批发起成功",
  "data": {
    "success": true,
    "approvalId": "AP202406001",
    "approvalNo": "AP-202406001"
  }
}
```

### 2.9 查询操作日志

**接口地址**: `GET /publicbiz/order/operation-logs`

**功能说明**: 查询高校实践订单操作日志

**请求参数**:

| 参数名    | 类型    | 必填 | 说明                   |
| --------- | ------- | ---- | ---------------------- |
| orderId   | Long    | 是   | 订单ID                 |
| orderNo   | String  | 是   | 订单号                 |
| logType   | String  | 否   | 日志类型筛选           |
| startDate | String  | 否   | 开始日期（YYYY-MM-DD） |
| endDate   | String  | 否   | 结束日期（YYYY-MM-DD） |
| page      | Integer | 否   | 页码，默认1            |
| size      | Integer | 否   | 每页大小，默认20       |

**响应字段**:

| 字段名           | 类型   | 说明       |
| ---------------- | ------ | ---------- |
| id               | Long   | 日志ID     |
| logType          | String | 日志类型   |
| logTitle         | String | 日志标题   |
| logContent       | String | 日志内容   |
| oldStatus        | String | 原状态     |
| newStatus        | String | 新状态     |
| operatorId       | Long   | 操作人ID   |
| operatorName     | String | 操作人姓名 |
| operatorRole     | String | 操作人角色 |
| relatedPartyType | String | 关联方类型 |
| relatedPartyName | String | 关联方名称 |
| createTime       | String | 创建时间   |

**请求示例**:

```http
GET /publicbiz/order/operation-logs?orderId=1&orderNo=HP202406001&logType=订单创建
```

**返回示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "logType": "订单创建",
      "logTitle": "创建高校实践订单",
      "logContent": "创建了新的高校实践订单",
      "oldStatus": "",
      "newStatus": "draft",
      "operatorName": "张三",
      "operatorRole": "管理员",
      "createTime": "2024-06-15 14:30:00"
    }
  ]
}
```

### 2.10 导出高校实践订单

**接口地址**: `POST /publicbiz/order/export`

**功能说明**: 导出高校实践订单数据

**请求参数**:

| 参数名        | 类型   | 必填 | 说明                             |
| ------------- | ------ | ---- | -------------------------------- |
| orderStatus   | String | 否   | 订单状态筛选                     |
| paymentStatus | String | 否   | 支付状态筛选                     |
| keyword       | String | 否   | 关键词搜索                       |
| startDate     | String | 否   | 开始日期（YYYY-MM-DD）           |
| endDate       | String | 否   | 结束日期（YYYY-MM-DD）           |
| exportFormat  | String | 否   | 导出格式（excel/csv），默认excel |

**响应**: 文件流

**请求示例**:

```json
{
  "orderStatus": "pending_approval",
  "paymentStatus": "pending",
  "startDate": "2024-06-01",
  "endDate": "2024-06-30",
  "exportFormat": "excel"
}
```

## 3. 数据字典

### 3.1 订单状态（orderStatus）

| 值               | 说明   |
| ---------------- | ------ |
| draft            | 草稿   |
| pending_approval | 待审批 |
| approving        | 审批中 |
| approved         | 已批准 |
| rejected         | 已拒绝 |
| pending_payment  | 待支付 |
| executing        | 执行中 |
| completed        | 已完成 |
| cancelled        | 已取消 |

### 3.2 支付状态（paymentStatus）

| 值        | 说明   |
| --------- | ------ |
| pending   | 待支付 |
| paid      | 已支付 |
| refunded  | 已退款 |
| cancelled | 已取消 |

### 3.3 合同类型（contractType）

| 值         | 说明     |
| ---------- | -------- |
| electronic | 电子合同 |
| paper      | 纸质合同 |

### 3.4 合同状态（contractStatus）

| 值       | 说明   |
| -------- | ------ |
| unsigned | 未签署 |
| signed   | 已签署 |
| rejected | 已拒绝 |

### 3.5 结算状态（settlementStatus）

| 值         | 说明     |
| ---------- | -------- |
| pending    | 待结算   |
| processing | 结算中   |
| completed  | 已结算   |
| failed     | 结算失败 |

### 3.6 支付类型（paymentType）

| 值            | 说明      |
| ------------- | --------- |
| cash          | 现金      |
| wechat        | 微信支付  |
| alipay        | 支付宝    |
| bank_transfer | 银行转账  |
| pos           | POS机刷卡 |
| other         | 其他      |

### 3.7 收款方式（collectionMethod）

| 值            | 说明      |
| ------------- | --------- |
| cash          | 现金      |
| wechat        | 微信支付  |
| alipay        | 支付宝    |
| bank_transfer | 银行转账  |
| pos           | POS机刷卡 |
| other         | 其他      |

### 3.8 日志类型（logType）

| 值         | 说明           |
| ---------- | -------------- |
| 订单创建   | 订单创建操作   |
| 订单编辑   | 订单编辑操作   |
| 审批通过   | 审批通过操作   |
| 审批驳回   | 审批驳回操作   |
| 确认收款   | 确认收款操作   |
| 订单完成   | 订单完成操作   |
| 系统管理员 | 系统管理员操作 |

## 4. 错误码说明

| 错误码 | 说明               | 解决方案                     |
| ------ | ------------------ | ---------------------------- |
| 200    | 操作成功           | -                            |
| 400    | 请求参数错误       | 检查请求参数格式和必填项     |
| 401    | 未授权             | 检查认证token是否有效        |
| 403    | 权限不足           | 检查用户是否有操作权限       |
| 404    | 资源不存在         | 检查订单ID或订单号是否正确   |
| 409    | 业务冲突           | 检查订单状态是否允许当前操作 |
| 500    | 服务器内部错误     | 联系技术支持                 |
| 1001   | 订单号已存在       | 使用新的订单号               |
| 1002   | 订单状态不允许操作 | 检查订单当前状态             |
| 1003   | 文件上传失败       | 检查文件格式和大小           |
| 1004   | 审批流程已存在     | 检查是否重复发起审批         |
| 1005   | 合同文件不存在     | 检查合同文件是否已上传       |

## 5. 注意事项

1. **字段命名**: 所有接口字段均采用驼峰命名方式，与数据库表结构保持一致
2. **日期格式**: 日期类型统一使用 `YYYY-MM-DD` 格式，时间类型使用 `YYYY-MM-DD HH:mm:ss` 格式
3. **金额精度**: 金额字段统一使用 `Decimal(12,2)` 精度，保留2位小数
4. **文件上传**: 支持PDF、Word、JPG、PNG格式，单个文件大小不超过10MB
5. **状态流转**: 订单状态变更需要严格按照业务流程进行，不能跨状态操作
6. **权限控制**: 不同角色用户只能操作有权限的订单
7. **日志记录**: 所有关键操作都会记录操作日志，便于审计和追踪
8. **收款信息**: 当支付状态为"已支付"（paid）时，收款金额、收款方式、收款日期、操作人为必填字段；其他支付状态下，这些字段为可选
9. **收款方式**: 收款方式支持现金、微信支付、支付宝、银行转账、POS机刷卡、其他等6种方式
10. **收款验证**: 收款金额建议与订单总金额保持一致，系统会进行合理性验证

## 6. 接口调用示例

### 6.1 完整业务流程示例

```javascript
// 1. 创建订单
const createOrder = async () => {
  const response = await fetch('/publicbiz/order/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: 'Bearer ' + token
    },
    body: JSON.stringify({
      projectName: '2024年暑期社会实践项目',
      universityName: 'XX大学经济管理学院',
      enterpriseName: 'ABC科技有限公司',
      startDate: '2024-07-01',
      endDate: '2024-08-30',
      totalAmount: 580000.0,
      managerId: 1001,
      managerName: '张三'
    })
  })
  return response.json()
}

// 2. 上传合同
const uploadContract = async (orderId, orderNo, file) => {
  const formData = new FormData()
  formData.append('orderId', orderId)
  formData.append('orderNo', orderNo)
  formData.append('contractType', 'paper')
  formData.append('file', file)

  const response = await fetch('/publicbiz/order/upload-contract', {
    method: 'POST',
    headers: {
      Authorization: 'Bearer ' + token
    },
    body: formData
  })
  return response.json()
}

// 3. 发起审批
const initiateApproval = async (orderId, orderNo) => {
  const response = await fetch('/publicbiz/order/initiate-approval', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: 'Bearer ' + token
    },
    body: JSON.stringify({
      orderId: orderId,
      orderNo: orderNo,
      approvalType: 'order_approval',
      priority: 'normal',
      approverIds: [1001, 1002],
      comments: '请审批该高校实践订单'
    })
  })
  return response.json()
}
```

这份API接口文档涵盖了高校实践订单的所有核心功能，包括增删改查、合同管理、审批流程、操作日志等。文档结构清晰，字段说明详细，便于开发者理解和对接使用。
