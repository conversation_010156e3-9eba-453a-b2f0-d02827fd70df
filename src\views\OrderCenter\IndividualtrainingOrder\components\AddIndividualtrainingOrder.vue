<!--
  页面名称：个人培训订单新增/编辑表单
  功能描述：新增/编辑个人培训订单，支持表单校验、提交、重置、编辑回显
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑个人培训订单' : '新增个人培训订单'"
    size="60%"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="individual-training-form"
    >
      <!-- 订单信息 -->
      <div class="section-title">订单信息</div>

      <el-form-item label="关联商机" prop="businessOpportunity">
        <el-select
          v-model="form.businessOpportunity"
          placeholder="请选择关联商机"
          style="width: 100%"
        >
          <el-option label="OPP202406009 - 个人培训课程推广商机" value="OPP202406009" />
        </el-select>
        <div class="field-hint">选择关联商机可自动填充部分课程信息</div>
      </el-form-item>

      <el-form-item label="关联线索" prop="associatedLead">
        <el-select v-model="form.associatedLead" placeholder="请选择关联线索" style="width: 100%">
          <el-option
            label="LEAD202406009 - 孙女士 131****3456 个人培训咨询"
            value="LEAD202406009"
          />
        </el-select>
        <div class="field-hint">选择关联线索可自动填充学员信息</div>
      </el-form-item>

      <el-form-item label="订单类型" prop="orderType" required>
        <el-radio-group v-model="form.orderType">
          <el-radio label="training">个人培训</el-radio>
          <el-radio label="certification">考试认证</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="学员姓名" prop="studentName" required>
        <el-input v-model="form.studentName" placeholder="请输入学员姓名" />
      </el-form-item>

      <el-form-item label="课程/考试项目" prop="courseName" required>
        <el-select v-model="form.courseName" placeholder="请选择课程/考试项目" style="width: 100%">
          <el-option label="项目管理PMP认证课程" value="pmp_course" />
          <el-option label="Python编程基础课程" value="python_course" />
          <el-option label="高级母婴护理师认证" value="maternal_care" />
          <el-option label="营养师资格认证" value="nutritionist" />
          <el-option label="心理咨询师三级认证" value="psychologist" />
          <el-option label="数据分析师认证课程" value="data_analyst" />
        </el-select>
      </el-form-item>

      <el-form-item label="订单来源" prop="orderSource" required>
        <el-select v-model="form.orderSource" placeholder="请选择订单来源" style="width: 100%">
          <el-option label="线上小程序" value="online_miniprogram" />
          <el-option label="线下报名" value="offline_registration" />
          <el-option label="电话咨询" value="phone_consultation" />
          <el-option label="朋友推荐" value="friend_recommendation" />
        </el-select>
      </el-form-item>

      <el-form-item label="订单金额" prop="orderAmount" required>
        <el-input v-model="form.orderAmount" placeholder="请输入订单金额" />
      </el-form-item>

      <el-form-item label="支付状态" prop="paymentStatus" required>
        <el-select v-model="form.paymentStatus" placeholder="请选择支付状态" style="width: 100%">
          <el-option label="待支付" value="pending" />
          <el-option label="已支付" value="paid" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
      </el-form-item>

      <!-- 支付信息模块 - 只在已支付状态时显示 -->
      <div v-if="form.paymentStatus === 'paid'" class="section-title">
        <el-icon class="section-icon"><Money /></el-icon>
        支付信息
      </div>

      <div v-if="form.paymentStatus === 'paid'">
        <el-form-item label="收款金额" prop="collectionAmount" required>
          <el-input v-model="form.collectionAmount" placeholder="请输入实际收款金额" />
        </el-form-item>

        <el-form-item label="收款方式" prop="collectionMethod" required>
          <el-select
            v-model="form.collectionMethod"
            placeholder="请选择收款方式"
            style="width: 100%"
          >
            <el-option label="银行转账" value="bank_transfer" />
            <el-option label="支付宝" value="alipay" />
            <el-option label="微信支付" value="wechat_pay" />
            <el-option label="现金" value="cash" />
          </el-select>
        </el-form-item>

        <el-form-item label="收款日期" prop="collectionDate" required>
          <el-date-picker
            v-model="form.collectionDate"
            type="datetime"
            placeholder="年-月-日 --:--"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="操作人" prop="operator" required>
          <el-input v-model="form.operator" placeholder="请输入操作人姓名" />
        </el-form-item>

        <el-form-item label="收款备注" prop="collectionRemarks">
          <el-input
            v-model="form.collectionRemarks"
            type="textarea"
            :rows="3"
            placeholder="请输入收款备注信息 (可选)"
          />
        </el-form-item>
      </div>

      <el-form-item label="学习/考试状态" prop="learningStatus" required>
        <el-select
          v-model="form.learningStatus"
          placeholder="请选择学习/考试状态"
          style="width: 100%"
        >
          <el-option label="未开始" value="not_started" />
          <el-option label="学习中" value="learning" />
          <el-option label="待考试" value="pending_exam" />
          <el-option label="学习中/待考试" value="learning_pending_exam" />
          <el-option label="待确认" value="pending_confirmation" />
          <el-option label="已通过" value="passed" />
          <el-option label="已完成" value="completed" />
        </el-select>
      </el-form-item>

      <!-- 合同管理 -->
      <div class="section-title">合同管理</div>

      <el-form-item label="合同类型" prop="contractType">
        <el-radio-group v-model="form.contractType">
          <el-radio label="electronic">电子合同</el-radio>
          <el-radio label="paper">纸质合同</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 电子合同时只显示合同模板 -->
      <el-form-item
        v-if="form.contractType === 'electronic'"
        label="合同模板"
        prop="contractTemplate"
      >
        <el-select v-model="form.contractTemplate" placeholder="请选择合同模板" style="width: 100%">
          <el-option label="个人培训协议模板" value="training_contract" />
          <el-option label="考试认证协议模板" value="certification_contract" />
          <el-option label="通用协议模板" value="general_contract" />
        </el-select>
      </el-form-item>

      <!-- 纸质合同时显示完整的合同管理字段 -->
      <div v-if="form.contractType === 'paper'">
        <el-form-item label="合同附件" prop="contractAttachment">
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :file-list="fileList"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                {{ fileList.length > 0 ? '已选择文件' : '未选择任何文件' }}
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="合同编号" prop="contractNumber">
          <el-input v-model="form.contractNumber" placeholder="请输入合同编号" />
        </el-form-item>

        <el-form-item label="合同名称" prop="contractName">
          <el-input v-model="form.contractName" placeholder="请输入合同名称" />
        </el-form-item>

        <el-form-item label="签署日期" prop="signingDate">
          <el-date-picker
            v-model="form.signingDate"
            type="date"
            placeholder="年-月-日"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="合同金额" prop="contractAmount">
          <el-input v-model="form.contractAmount" placeholder="请输入合同金额" />
        </el-form-item>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Money } from '@element-plus/icons-vue'
import type { FormInstance, UploadFile } from 'element-plus'

// Props
interface Props {
  visible: boolean
  editData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
/** 表单引用 */
const formRef = ref<FormInstance>()

/** 表单数据 */
const form = ref({
  businessOpportunity: 'OPP202406009',
  associatedLead: 'LEAD202406009',
  orderType: 'certification',
  studentName: '',
  courseName: 'pmp_course',
  orderSource: 'online_miniprogram',
  orderAmount: '',
  paymentStatus: 'pending',
  learningStatus: 'not_started',
  contractType: 'paper',
  contractTemplate: 'training_contract',
  collectionAmount: '',
  collectionMethod: '',
  collectionDate: '',
  operator: '',
  collectionRemarks: '',
  contractAttachment: '',
  contractNumber: '',
  contractName: '',
  signingDate: '',
  contractAmount: ''
})

/** 表单校验规则 */
const rules = {
  orderType: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  studentName: [{ required: true, message: '请输入学员姓名', trigger: 'blur' }],
  courseName: [{ required: true, message: '请选择课程/考试项目', trigger: 'change' }],
  orderSource: [{ required: true, message: '请选择订单来源', trigger: 'change' }],
  orderAmount: [{ required: true, message: '请输入订单金额', trigger: 'blur' }],
  paymentStatus: [{ required: true, message: '请选择支付状态', trigger: 'change' }],
  learningStatus: [{ required: true, message: '请选择学习/考试状态', trigger: 'change' }],
  // 支付信息相关验证规则
  collectionAmount: [{ required: true, message: '请输入收款金额', trigger: 'blur' }],
  collectionMethod: [{ required: true, message: '请选择收款方式', trigger: 'change' }],
  collectionDate: [{ required: true, message: '请选择收款日期', trigger: 'change' }],
  operator: [{ required: true, message: '请输入操作人姓名', trigger: 'blur' }]
}

/** 文件列表 */
const fileList = ref<UploadFile[]>([])

/** 加载状态 */
const loading = ref(false)

/** 是否为编辑模式 */
const isEdit = ref(false)

// 方法
/** 处理文件变化 */
const handleFileChange = (file: UploadFile) => {
  fileList.value = [file]
  form.value.contractAttachment = file.name || ''
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // TODO: 调用接口提交数据
    // 模拟接口调用延迟
    await new Promise((resolve) => setTimeout(resolve, 1000))

    ElMessage.success(isEdit.value ? '更新成功' : '保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error('提交失败，请检查表单信息')
  } finally {
    loading.value = false
  }
}

/** 重置表单 */
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  fileList.value = []
  initFormData()
}

/** 关闭抽屉 */
const handleClose = () => {
  emit('update:visible', false)
  handleReset()
}

/** 初始化表单数据 */
const initFormData = () => {
  form.value = {
    businessOpportunity: 'OPP202406009',
    associatedLead: 'LEAD202406009',
    orderType: 'certification',
    studentName: '',
    courseName: 'pmp_course',
    orderSource: 'online_miniprogram',
    orderAmount: '',
    paymentStatus: 'pending',
    learningStatus: 'not_started',
    contractType: 'paper',
    contractTemplate: 'training_contract',
    collectionAmount: '',
    collectionMethod: '',
    collectionDate: '',
    operator: '',
    collectionRemarks: '',
    contractAttachment: '',
    contractNumber: '',
    contractName: '',
    signingDate: '',
    contractAmount: ''
  }
}

/** 编辑时回显数据 */
const setEditData = (data: any) => {
  if (data) {
    form.value = { ...data }
    isEdit.value = true
  } else {
    initFormData()
    isEdit.value = false
  }
}

// 监听器
watch(
  () => props.editData,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        setEditData(newVal)
      })
    }
  },
  { immediate: true }
)

watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) {
      handleReset()
    }
  }
)
</script>

<style scoped lang="scss">
.individual-training-form {
  padding: 20px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin: 20px 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;

    .section-icon {
      margin-right: 8px;
      color: #67c23a;
    }
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e4e7ed;
  }
}

.upload-demo {
  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
  }
}
</style>
