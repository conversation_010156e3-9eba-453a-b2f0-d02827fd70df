<!--
  页面名称：高校实践订单新增
  功能描述：新增高校实践订单，支持关联线索自动填充，电子合同和纸质合同共用附件上传
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="drawerTitle"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="top"
      class="order-form"
    >
      <!-- 项目信息 -->
      <div class="form-section">
        <div class="section-title">项目信息</div>

        <!-- 关联线索 -->
        <el-form-item label="关联线索" prop="lead">
          <el-select
            v-model="form.lead"
            placeholder="请选择关联线索"
            style="width: 100%"
            @change="handleLeadChange"
          >
            <el-option v-for="lead in leads" :key="lead.id" :label="lead.name" :value="lead.id" />
          </el-select>

          <!-- 线索信息展示 -->
          <div v-if="selectedLead" class="info-box lead-info">
            <div class="info-header">
              <el-icon class="info-icon lead-icon"><InfoFilled /></el-icon>
              <span class="info-title">线索信息已自动填充</span>
            </div>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">联系人：</span>
                <span class="info-value">{{ selectedLead.contactPerson }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">联系电话：</span>
                <span class="info-value">{{ selectedLead.contactPhone }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">线索来源：</span>
                <span class="info-value">{{ selectedLead.source }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">线索描述：</span>
                <span class="info-value">{{ selectedLead.description }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">线索状态：</span>
                <el-tag :type="getLeadStatusType(selectedLead.status)" size="small">
                  {{ selectedLead.status }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="form-tip">选择关联线索可自动填充客户信息</div>
        </el-form-item>

        <!-- 项目名称 -->
        <el-form-item label="项目名称" prop="projectName">
          <el-input
            v-model="form.projectName"
            placeholder="请输入项目名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <!-- 合作高校 -->
        <el-form-item label="合作高校" prop="university">
          <el-input v-model="form.university" placeholder="请输入合作高校名称" maxlength="100" />
        </el-form-item>

        <!-- 合作企业 -->
        <el-form-item label="合作企业" prop="enterprise">
          <el-input v-model="form.enterprise" placeholder="请输入合作企业名称" maxlength="100" />
        </el-form-item>

        <!-- 项目周期 -->
        <el-form-item label="项目周期" prop="projectPeriod">
          <el-date-picker
            v-model="form.projectPeriod"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <!-- 项目负责人 -->
        <el-form-item label="项目负责人" prop="manager">
          <el-input v-model="form.manager" placeholder="请输入项目负责人" maxlength="50" />
        </el-form-item>

        <!-- 订单金额 -->
        <el-form-item label="订单金额" prop="amount">
          <el-input
            v-model.number="form.amount"
            placeholder="请输入订单金额"
            type="number"
            min="0"
            style="width: 100%"
          >
            <template #prefix>¥</template>
          </el-input>
        </el-form-item>

        <!-- 支付状态 -->
        <el-form-item label="支付状态" prop="paymentStatus">
          <el-select v-model="form.paymentStatus" placeholder="请选择支付状态" style="width: 100%">
            <el-option label="待支付" value="unpaid" />
            <el-option label="已支付" value="paid" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>

        <!-- 合同类型 -->
        <el-form-item label="合同类型" prop="contractType">
          <el-radio-group v-model="form.contractType">
            <el-radio value="electronic">电子合同</el-radio>
            <el-radio value="paper">纸质合同</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 合同附件上传 -->
        <el-form-item label="合同附件" prop="contractFile">
          <div class="contract-upload-container">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              :limit="1"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              class="contract-upload"
            >
              <el-button type="primary" size="small">选择文件</el-button>
              <template #tip>
                <div class="upload-tip">支持格式: PDF、Word、JPG、PNG, 文件大小不超过10MB</div>
              </template>
            </el-upload>
          </div>
        </el-form-item>

        <!-- 项目描述 -->
        <el-form-item label="项目描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入项目描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </div>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadInstance, UploadFile } from 'element-plus'

// Props
interface Props {
  visible: boolean
  isEdit?: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 表单引用
const formRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()

// 加载状态
const loading = ref(false)

// 文件列表
const fileList = ref<UploadFile[]>([])

// 表单数据
const form = ref({
  lead: '',
  projectName: '',
  university: '',
  enterprise: '',
  projectPeriod: [],
  manager: '',
  amount: '',
  paymentStatus: 'unpaid',
  contractType: 'electronic',
  contractFile: null as File | null,
  description: ''
})

// 抽屉标题
const drawerTitle = computed(() => {
  return props.isEdit ? '编辑高校实践订单' : '新建高校实践订单'
})

// 线索列表
const leads = ref([
  {
    id: '1',
    name: 'LEAD202406001 - 张三 138****1234 电话咨询',
    contactPerson: '张三',
    contactPhone: '138****1234',
    source: '电话咨询',
    description: '咨询暑期实践项目',
    status: '已转化'
  },
  {
    id: '2',
    name: 'LEAD202406002 - 李四 139****5678 电话咨询',
    contactPerson: '李四',
    contactPhone: '139****5678',
    source: '电话咨询',
    description: '咨询春季实习项目',
    status: '已转化'
  },
  {
    id: '3',
    name: 'LEAD202406003 - 王五 137****9012 网络咨询',
    contactPerson: '王五',
    contactPhone: '137****9012',
    source: '网络咨询',
    description: '咨询AI培训项目',
    status: '跟进中'
  }
])

// 选中的线索
const selectedLead = computed(() => {
  if (!form.value.lead) return null
  return leads.value.find((item) => item.id === form.value.lead)
})

// 表单校验规则
const rules: FormRules = {
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '项目名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  university: [{ required: true, message: '请输入合作高校', trigger: 'blur' }],
  enterprise: [{ required: true, message: '请输入合作企业', trigger: 'blur' }],
  projectPeriod: [{ required: true, message: '请选择项目周期', trigger: 'change' }],
  manager: [{ required: true, message: '请输入项目负责人', trigger: 'blur' }],
  amount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '订单金额必须大于0', trigger: 'blur' }
  ],
  paymentStatus: [{ required: true, message: '请选择支付状态', trigger: 'change' }]
}

// 获取线索状态类型
const getLeadStatusType = (status: string) => {
  const statusMap: Record<string, 'warning' | 'success' | 'info'> = {
    跟进中: 'warning',
    已转化: 'success',
    已失效: 'info'
  }
  return statusMap[status] || 'info'
}

// 线索选择变化
const handleLeadChange = (value: string) => {
  if (value && selectedLead.value) {
    // 可以根据线索信息自动填充客户相关字段
    form.value.manager = selectedLead.value.contactPerson
    console.log('线索信息:', selectedLead.value)
  }
}

// 文件选择变化
const handleFileChange = (file: UploadFile) => {
  // 检查文件大小（10MB）
  const isLt10M = file.size! / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }

  // 检查文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/png'
  ]
  if (!allowedTypes.includes(file.raw?.type || '')) {
    ElMessage.error('只支持 PDF、Word、JPG、PNG 格式的文件!')
    return false
  }

  form.value.contractFile = file.raw || null
  return true
}

// 文件移除
const handleFileRemove = () => {
  form.value.contractFile = null
}

// 监听弹窗显示状态，重置表单
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm()
    }
  }
)

// 重置表单
const resetForm = () => {
  form.value = {
    lead: '',
    projectName: '',
    university: '',
    enterprise: '',
    projectPeriod: [],
    manager: '',
    amount: '',
    paymentStatus: 'unpaid',
    contractType: 'electronic',
    contractFile: null,
    description: ''
  }
  fileList.value = []
  formRef.value?.clearValidate()
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟接口调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    ElMessage.success(props.isEdit ? '编辑成功' : '创建成功')
    emit('success', form.value)
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.order-form {
  padding: 20px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.form-section {
  margin-bottom: 30px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
  }
}

.info-box {
  margin-top: 12px;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid;

  .info-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .info-icon {
      font-size: 16px;
      color: #409eff;

      &.lead-icon {
        color: #722ed1;
      }
    }

    .info-title {
      font-weight: 500;
      color: #303133;
    }
  }

  .info-content {
    .info-item {
      display: flex;
      margin-bottom: 8px;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-weight: 500;
        color: #606266;
        min-width: 80px;
        flex-shrink: 0;
      }

      .info-value {
        color: #303133;
        flex: 1;

        &.amount {
          color: #67c23a;
          font-weight: bold;
        }
      }
    }
  }

  &.lead-info {
    background: #f9f0ff;
    border-color: #722ed1;
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.contract-upload-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;

  .contract-upload {
    width: 100%;

    .upload-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-drawer__header) {
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 0;
}

:deep(.el-drawer__body) {
  padding: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-form-item.is-required .el-form-item__label::before) {
  color: #f56c6c;
}
</style>
