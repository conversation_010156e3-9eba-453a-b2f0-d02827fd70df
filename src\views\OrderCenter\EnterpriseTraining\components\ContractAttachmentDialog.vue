<!--
  页面名称：合同附件上传弹窗
  功能描述：上传合同附件，支持文件选择、上传状态显示等，使用与编辑页面相同的上传接口
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="上传合同附件"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="contract-attachment-container">
      <!-- 附件上传 -->
      <el-form-item label="选择附件" required>
        <el-upload
          ref="attachmentUploadRef"
          action="#"
          :auto-upload="false"
          :on-change="handleAttachmentChange"
          :on-remove="handleAttachmentRemove"
          :file-list="attachmentList"
          :limit="5"
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx"
          class="attachment-upload"
        >
          <el-button type="primary" size="small">
            <el-icon><Plus /></el-icon>
            选择附件
          </el-button>
          <template #tip>
            <div class="upload-tip"
              >支持格式: PDF、Word、Excel、JPG、PNG, 单个文件不超过10MB, 最多5个文件</div
            >
          </template>
        </el-upload>
      </el-form-item>

      <!-- 附件列表展示 -->
      <div v-if="attachmentList.length > 0" class="attachment-list">
        <div class="attachment-header">
          <span class="attachment-title">已选择附件 ({{ attachmentList.length }}/5)</span>
          <div v-if="contractFileUrl" class="contract-url-info">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
            <el-tag type="success" size="small">合同附件已上传</el-tag>
            <span class="url-text">{{ contractFileUrl }}</span>
          </div>
        </div>
        <div class="attachment-items">
          <div v-for="(file, index) in attachmentList" :key="index" class="attachment-item">
            <div class="attachment-info">
              <el-icon class="file-icon"><Document /></el-icon>
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
              <el-tag
                :type="
                  file.status === 'success'
                    ? 'success'
                    : file.status === 'fail'
                      ? 'danger'
                      : 'warning'
                "
                size="small"
              >
                {{
                  file.status === 'success'
                    ? '上传成功'
                    : file.status === 'fail'
                      ? '上传失败'
                      : '上传中'
                }}
              </el-tag>
            </div>
            <div class="attachment-actions">
              <el-button type="danger" size="small" @click="removeAttachment(index)">
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 上传说明 -->
      <div class="upload-description">
        <el-alert title="上传说明" type="info" :closable="false" show-icon>
          <template #default>
            <p>1. 支持上传PDF、Word、Excel、JPG、PNG等格式文件</p>
            <p>2. 单个文件大小不超过10MB</p>
            <p>3. 最多可上传5个文件</p>
            <p>4. 上传成功后，合同附件将自动关联到当前订单</p>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="uploading"
          :disabled="attachmentList.length === 0"
        >
          确认上传
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled, Document, Plus } from '@element-plus/icons-vue'
import request from '@/config/axios'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: undefined
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 组件引用
const attachmentUploadRef = ref()

// 状态
const uploading = ref(false)
const attachmentList = ref<any[]>([])
const contractFileUrl = ref('')

// 方法
/** 格式化文件大小 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/** 附件选择变化处理 */
const handleAttachmentChange = async (file: any, fileList: any[]) => {
  console.log('附件选择变化:', file, fileList)
  attachmentList.value = fileList

  // 如果选择了新文件，自动上传
  if (file && file.raw && file.status === 'ready') {
    try {
      await uploadSingleAttachment(file)
    } catch (error) {
      console.error('文件上传失败:', error)
      // 从列表中移除上传失败的文件
      const index = attachmentList.value.findIndex((f) => f.uid === file.uid)
      if (index > -1) {
        attachmentList.value.splice(index, 1)
      }
    }
  }
}

/** 附件移除处理 */
const handleAttachmentRemove = (file: any, fileList: any[]) => {
  console.log('附件移除:', file, fileList)
  attachmentList.value = fileList

  // 如果移除的是合同附件，清空合同文件URL
  if (file && file.url === contractFileUrl.value) {
    contractFileUrl.value = ''
    console.log('已清空合同文件URL')
  }
}

/** 移除指定索引的附件 */
const removeAttachment = (index: number) => {
  const removedFile = attachmentList.value[index]
  attachmentList.value.splice(index, 1)

  // 如果移除的是合同附件，清空合同文件URL
  if (removedFile && removedFile.url === contractFileUrl.value) {
    contractFileUrl.value = ''
    console.log('已清空合同文件URL')
  }
}

/** 上传单个附件到服务器 - 使用与编辑页面相同的接口 */
const uploadSingleAttachment = async (file: any) => {
  if (!file.raw) {
    throw new Error(`文件 ${file.name} 数据不完整`)
  }

  const formData = new FormData()
  formData.append('file', file.raw)
  formData.append('directory', 'enterprise-training-order')

  try {
    // 使用与编辑页面相同的上传接口
    const res = await request.postOriginal({
      url: '/infra/file/upload',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })

    console.log('文件上传响应:', res)

    // 根据接口返回格式：{ "code": 0, "data": "https://...", "msg": "" }
    const fileUrl = res.data

    if (res.data && fileUrl && typeof fileUrl === 'string' && /^https?:\/\//.test(fileUrl)) {
      // 上传成功，更新文件状态和URL
      file.url = fileUrl
      file.status = 'success'

      // 保存合同文件URL
      contractFileUrl.value = fileUrl

      ElMessage.success(`附件 ${file.name} 上传成功`)
      console.log('合同文件URL已保存:', fileUrl)
      return { success: true, fileUrl, fileName: file.name }
    } else {
      throw new Error('文件上传失败：接口返回数据格式错误')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    file.status = 'fail'
    throw error
  }
}

/** 提交表单 */
const handleSubmit = async () => {
  if (attachmentList.value.length === 0) {
    ElMessage.warning('请选择要上传的附件')
    return
  }

  // 检查是否有上传失败的文件
  const failedFiles = attachmentList.value.filter((file) => file.status === 'fail')
  if (failedFiles.length > 0) {
    ElMessage.warning('存在上传失败的文件，请重新上传或删除')
    return
  }

  // 检查是否有正在上传的文件
  const uploadingFiles = attachmentList.value.filter((file) => file.status === 'uploading')
  if (uploadingFiles.length > 0) {
    ElMessage.warning('存在正在上传的文件，请等待上传完成')
    return
  }

  uploading.value = true

  try {
    // 所有文件都已上传成功，直接返回成功
    const uploadedFiles = attachmentList.value.filter((file) => file.status === 'success')

    ElMessage.success('合同附件上传成功')
    emit('success', {
      files: uploadedFiles,
      contractFileUrl: contractFileUrl.value
    })
    handleCancel()
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败，请重试')
  } finally {
    uploading.value = false
  }
}

/** 取消 */
const handleCancel = () => {
  emit('update:visible', false)
  // 重置状态
  attachmentList.value = []
  contractFileUrl.value = ''
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 弹窗打开时，初始化数据
      attachmentList.value = []
      contractFileUrl.value = ''
    }
  }
)
</script>

<style scoped lang="scss">
.contract-attachment-container {
  .attachment-upload {
    .upload-tip {
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
      margin-top: 8px;
    }
  }

  .attachment-list {
    margin-top: 20px;

    .attachment-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .attachment-title {
        font-weight: 500;
        color: #303133;
        font-size: 14px;
      }

      .contract-url-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .info-icon {
          color: #67c23a;
        }

        .url-text {
          color: #67c23a;
          font-size: 12px;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .attachment-items {
      .attachment-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background: #f8f9fa;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .attachment-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .file-icon {
            color: #409eff;
          }

          .file-name {
            color: #303133;
            font-weight: 500;
          }

          .file-size {
            color: #909399;
            font-size: 12px;
          }
        }

        .attachment-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .upload-description {
    margin-top: 20px;

    .el-alert {
      .el-alert__content {
        p {
          margin: 4px 0;
          line-height: 1.5;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
