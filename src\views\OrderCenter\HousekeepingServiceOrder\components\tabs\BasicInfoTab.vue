<!--
  页面名称：基本信息标签页
  功能描述：展示家政服务订单的基本信息
-->
<template>
  <div class="basic-info-tab">
    <div class="info-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>客户姓名：</label>
            <span>{{ orderDetail.customerName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>联系电话：</label>
            <span>{{ orderDetail.customerPhone }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>服务地址：</label>
            <span class="address-text">{{ orderDetail.serviceAddress }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>服务类型：</label>
            <span>{{ orderDetail.serviceType }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>预约时间：</label>
            <span>{{ orderDetail.appointmentTime }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>服务人员：</label>
            <span>{{ orderDetail.servicePersonnel }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>服务机构：</label>
            <span>{{ orderDetail.serviceAgency }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>订单金额：</label>
            <span class="amount">¥{{ formatAmount(orderDetail.serviceAmount) }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <!-- 预留空间，保持布局平衡 -->
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue'

interface Props {
  orderDetail: any
}

defineProps<Props>()

const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}
</script>

<style scoped lang="scss">
.basic-info-tab {
  .info-content {
    .info-item {
      margin-bottom: 16px;
      display: flex;
      align-items: flex-start;

      label {
        min-width: 80px;
        color: #606266;
        font-weight: 500;
        margin-right: 8px;
      }

      span {
        color: #303133;
        flex: 1;

        &.amount {
          color: #f56c6c;
          font-weight: 500;
        }

        &.address-text {
          word-break: break-all;
          line-height: 1.5;
          white-space: pre-wrap;
          word-wrap: break-word;
        }
      }
    }
  }
}
</style>
