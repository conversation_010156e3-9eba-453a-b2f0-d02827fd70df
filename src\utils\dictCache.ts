import { getDictDataPage } from '@/api/system/dict/dict.data'

// 数据字典缓存
const dictCache = new Map<string, any[]>()

// 缓存过期时间（5分钟）
const CACHE_EXPIRE_TIME = 5 * 60 * 1000

// 缓存时间戳
const cacheTimestamps = new Map<string, number>()

/**
 * 获取数据字典选项
 * @param dictType 字典类型
 * @param status 状态（0-禁用，1-启用）
 * @param forceRefresh 是否强制刷新缓存
 * @returns Promise<Array<{label: string, value: string}>>
 */
export async function getDictOptions(
  dictType: string,
  status: number = 0,
  forceRefresh: boolean = false
): Promise<Array<{ label: string; value: string }>> {
  const cacheKey = `${dictType}_${status}`

  // 检查缓存是否存在且未过期
  if (!forceRefresh && dictCache.has(cacheKey)) {
    const timestamp = cacheTimestamps.get(cacheKey)
    if (timestamp && Date.now() - timestamp < CACHE_EXPIRE_TIME) {
      console.log(`[DictCache] 使用缓存数据: ${dictType}`)
      return dictCache.get(cacheKey) || []
    }
  }

  try {
    console.log(`[DictCache] 请求数据字典: ${dictType}, status: ${status}`)

    const res = await getDictDataPage({
      dictType,
      status,
      pageNo: 1,
      pageSize: 99
    })

    console.log(`[DictCache] 数据字典响应:`, res)

    let list: any[] = []
    if (res && res.data && Array.isArray(res.data.list)) {
      list = res.data.list
      console.log(`[DictCache] 使用 res.data.list, 长度: ${list.length}`)
    } else if (res && Array.isArray(res.list)) {
      list = res.list
      console.log(`[DictCache] 使用 res.list, 长度: ${list.length}`)
    } else {
      console.warn(`[DictCache] 未找到有效的数据列表:`, res)
    }

    const options = list.map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    console.log(`[DictCache] 处理后的选项:`, options)

    // 更新缓存
    dictCache.set(cacheKey, options)
    cacheTimestamps.set(cacheKey, Date.now())

    return options
  } catch (error) {
    console.error(`[DictCache] 获取数据字典失败 [${dictType}]:`, error)
    return []
  }
}

/**
 * 清除指定字典类型的缓存
 * @param dictType 字典类型
 */
export function clearDictCache(dictType?: string): void {
  if (dictType) {
    // 清除指定类型的缓存
    for (const key of dictCache.keys()) {
      if (key.startsWith(dictType)) {
        dictCache.delete(key)
        cacheTimestamps.delete(key)
      }
    }
  } else {
    // 清除所有缓存
    dictCache.clear()
    cacheTimestamps.clear()
  }
}

/**
 * 预加载常用的数据字典
 */
export async function preloadCommonDicts(): Promise<void> {
  const commonDicts = [
    { type: 'service_type', status: 0 },
    { type: 'platform_status', status: 0 },
    { type: 'composite_rating', status: 0 }
  ]

  await Promise.all(commonDicts.map((dict) => getDictOptions(dict.type, dict.status)))
}
