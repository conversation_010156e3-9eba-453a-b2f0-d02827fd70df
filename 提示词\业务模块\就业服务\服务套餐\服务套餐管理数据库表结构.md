```sql
CREATE TABLE `publicbiz_service_package` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `name` varchar(200) NOT NULL COMMENT '套餐名称',
  `category` varchar(50) NOT NULL COMMENT '服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理',
  `thumbnail` varchar(500) DEFAULT NULL COMMENT '套餐主图URL',
  `price` decimal(10,2) NOT NULL COMMENT '套餐价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `unit` varchar(20) NOT NULL COMMENT '价格单位：次/项/天/月',
  `service_duration` varchar(100) DEFAULT NULL COMMENT '服务时长，如：4小时、26天、90天',
  `package_type` varchar(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
  `task_split_rule` varchar(200) DEFAULT NULL COMMENT '任务拆分规则',
  `service_description` text COMMENT '服务描述，建议100-200字',
  `service_details` longtext COMMENT '详细服务内容，富文本格式',
  `service_process` longtext COMMENT '服务流程，富文本格式',
  `purchase_notice` text COMMENT '购买须知',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：active-已上架/pending-待上架/deleted-回收站',
  `advance_booking_days` int(11) NOT NULL DEFAULT '1' COMMENT '预约时间范围：1-提前1天/3-提前3天/7-提前7天',
  `time_selection_mode` varchar(20) NOT NULL DEFAULT 'fixed' COMMENT '时间选择模式：fixed-固定时间/flexible-灵活时间',
  `appointment_mode` varchar(20) NOT NULL DEFAULT 'start-date' COMMENT '预约模式：start-date-开始日期预约/all-at-once-一次性预约全部服务次数',
  `service_start_time` varchar(20) NOT NULL DEFAULT 'within-3-days' COMMENT '服务开始时间：within-3-days-下单后3天内开始/specified-date-指定日期开始',
  `address_setting` varchar(20) NOT NULL DEFAULT 'fixed' COMMENT '地址设置：fixed-固定地址/changeable-可变更地址',
  `max_booking_days` int(11) NOT NULL DEFAULT '30' COMMENT '最大预约天数',
  `cancellation_policy` varchar(500) DEFAULT NULL COMMENT '取消政策',
  `audit_status` varchar(20) NOT NULL DEFAULT 'auditing' COMMENT '审核状态：pending-待审核， auditing-审核中，approved-已通过，rejected-已拒绝',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `agency_name` varchar(100) DEFAULT NULL COMMENT '所属机构名称',
  `reject_reason` text COMMENT '拒绝原因，审核拒绝时填写',
  `category_id` bigint(20) DEFAULT NULL COMMENT '服务分类ID',
  `service_time_start` varchar(50) DEFAULT NULL COMMENT '服务开始时间，如：09:00:00',
  `service_time_end` varchar(50) DEFAULT NULL COMMENT '服务结束时间，如：13:00:00',
  `rest_day_type` varchar(20) DEFAULT NULL COMMENT '休息日类型：none-无特殊设置/saturday-周六/sunday-周日/weekend-周末/statutory-法定节假日/both-周末及法定节假日/negotiable-客户协商',
  `service_timespan` varchar(4000) DEFAULT NULL COMMENT '服务时间，如：9:00-13:00、全天、夜班',
  `service_times` int(11) DEFAULT NULL COMMENT '服务次数(次)或服务周期(天)，如：4、6、10',
  `validity_period` int(11) DEFAULT NULL COMMENT '有效期（天），如：90天、180天、365天',
  `validity_period_unit` varchar(20) DEFAULT 'day' COMMENT '有效期单位：day-天/week-周/month-月/year-年',
  `service_interval_type` varchar(20) DEFAULT NULL COMMENT '服务间隔类型/服务频次类型：day-每天/weekly-每周/monthly-每月/year-每年',
  `service_interval_value` int(11) DEFAULT NULL COMMENT '服务间隔数值/服务频次数值，如：1表示每周1次或每月1次',
  `single_duration_hours` int(11) DEFAULT NULL COMMENT '单次服务时长（小时），如：2、4、6',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_package_type` (`package_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_audit_status` (`audit_status`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COMMENT='服务套餐主表';

CREATE TABLE `publicbiz_package_carousel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `package_id` bigint(20) NOT NULL COMMENT '套餐ID',
  `image_url` varchar(500) NOT NULL COMMENT '轮播图URL',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  PRIMARY KEY (`id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COMMENT='服务套餐轮播图表';

CREATE TABLE `publicbiz_package_feature` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `package_id` bigint(20) NOT NULL COMMENT '套餐ID',
  `feature_name` varchar(100) NOT NULL COMMENT '特色标签名称',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  PRIMARY KEY (`id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_feature_name` (`feature_name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COMMENT='服务套餐特色标签表';
```

```sql
-- 添加合作伙伴相关字段的 ALTER TABLE 语句
-- 在 agency_name 字段后添加 partner_id 和 partner_name 字段
ALTER TABLE `publicbiz_service_package`
ADD COLUMN `partner_id` bigint(20) DEFAULT NULL COMMENT '合作伙伴ID' AFTER `agency_name`,
ADD COLUMN `partner_name` varchar(128) DEFAULT NULL COMMENT '合作伙伴名称' AFTER `partner_id`;
```
