# 个人培训与认证订单模块

## 模块概述

本模块实现了个人培训与认证订单的完整管理功能，包括订单列表展示、新增/编辑、查看详情、操作日志等功能。

## 文件结构

```
src/views/OrderCenter/IndividualtrainingOrder/
├── index.vue                                    # 主页面 - 订单列表
├── components/
│   ├── AddIndividualtrainingOrder.vue          # 新增/编辑订单抽屉
│   ├── IndividualtrainingOrderView.vue         # 查看订单详情抽屉
│   └── OptLog.vue                              # 操作日志抽屉
└── README.md                                   # 本文件
```

## 功能特性

### 1. 主页面 (index.vue)

- **统计卡片**: 显示总订单数、待处理订单、本月订单金额、订单完成率
- **搜索筛选**: 支持按订单状态、支付状态、类型、关键词搜索
- **数据表格**: 展示订单列表，支持分页
- **操作按钮**: 查看、编辑、删除、操作日志
- **响应式设计**: 适配不同屏幕尺寸

### 2. 新增/编辑订单 (AddIndividualtrainingOrder.vue)

- **订单信息模块**:
  - 关联商机/线索选择
  - 订单类型（个人培训/考试认证）
  - 学员姓名、课程项目、订单来源
  - 订单金额、支付状态、学习状态
- **合同管理模块**:
  - 合同类型选择
  - 合同模板、附件上传
  - 合同编号、名称、签署日期、金额
- **表单验证**: 必填字段验证、金额格式验证
- **编辑回显**: 支持编辑时自动填充数据

### 3. 查看订单详情 (IndividualtrainingOrderView.vue)

- **课程标题横幅**: 渐变背景显示课程名称
- **基本信息**: 订单号、学员信息、课程信息等
- **收款信息**: 支付金额、方式、日期等（绿色边框突出显示）
- **电子协议**: 协议状态、发起签约按钮
- **审批流程**: 审批记录展示
- **操作日志**: 时间线展示操作历史

### 4. 操作日志 (OptLog.vue)

- **时间线展示**: 按时间顺序显示操作记录
- **操作详情**: 显示操作人、时间、操作内容
- **状态变更**: 突出显示状态变化（如支付状态变更）
- **操作标签**: 不同类型操作用不同颜色标识

## 技术实现

### 前端技术栈

- **Vue 3**: 使用 Composition API
- **TypeScript**: 类型安全
- **Element Plus**: UI组件库
- **SCSS**: 样式预处理器

### 设计模式

- **组件化**: 模块化设计，便于维护
- **响应式**: 使用Vue 3的响应式系统
- **抽屉模式**: 右侧滑出式交互
- **表单验证**: 完整的表单校验机制

### 样式特点

- **模块化背景**: 每个功能模块使用浅色背景区分
- **卡片设计**: 统计卡片、信息卡片等
- **状态标签**: 不同状态使用不同颜色的标签
- **渐变效果**: 课程标题横幅使用渐变背景

## 使用说明

### 1. 查看订单列表

- 访问主页面查看所有订单
- 使用搜索和筛选功能快速定位订单
- 点击订单号可查看详情

### 2. 新增订单

- 点击"新建个人培训订单"按钮
- 填写订单信息和合同信息
- 提交表单完成新增

### 3. 编辑订单

- 在列表页点击"编辑"按钮
- 或在详情页点击"编辑"按钮
- 修改相关信息后保存

### 4. 查看详情

- 点击"查看"按钮或订单号
- 查看完整的订单信息和相关记录

### 5. 查看操作日志

- 点击"操作日志"按钮
- 查看该订单的所有操作历史

## 数据接口

### 模拟数据

目前使用模拟数据，包含以下字段：

- 订单基本信息（订单号、学员姓名、课程等）
- 状态信息（订单状态、支付状态、学习状态）
- 时间信息（报名时间、操作时间等）
- 关联信息（商机、线索等）

### 接口对接

预留了接口调用的位置，可根据实际后端接口进行调整：

- 列表查询接口
- 新增/编辑接口
- 详情查询接口
- 操作日志接口

## 注意事项

1. **表单验证**: 所有必填字段都有相应的验证规则
2. **状态管理**: 使用Vue 3的响应式系统管理组件状态
3. **用户体验**: 提供加载状态、成功/失败提示
4. **数据安全**: 删除操作需要二次确认
5. **响应式设计**: 适配不同屏幕尺寸

## 后续优化

1. **接口对接**: 连接真实的后端接口
2. **权限控制**: 根据用户角色控制操作权限
3. **数据导出**: 支持订单数据导出功能
4. **批量操作**: 支持批量编辑、删除等操作
5. **高级筛选**: 增加更多筛选条件和排序功能

