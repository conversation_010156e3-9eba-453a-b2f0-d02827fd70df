<template>
  <div class="enterprise-training-index">
    <!-- 简单的统计卡片 -->
    <div class="statistics-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statsData.totalOrders }}</div>
            <div class="stat-label">总订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statsData.pendingOrders }}</div>
            <div class="stat-label">待处理订单</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">¥{{ (statsData.monthlyAmount || 0).toLocaleString() }}</div>
            <div class="stat-label">本月订单金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statsData.completionRate || 0 }}%</div>
            <div class="stat-label">订单完成率</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 简单的搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="订单状态">
            <el-select
              v-model="searchForm.orderStatus"
              placeholder="全部订单状态"
              clearable
              style="width: 160px"
            >
              <el-option label="全部订单状态" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="待审批" value="pending_approval" />
              <el-option label="审批中" value="approving" />
              <el-option label="待支付" value="pending_payment" />
              <el-option label="待执行" value="pending_execution" />
              <el-option label="执行中" value="executing" />
              <el-option label="已完成" value="completed" />
              <el-option label="已关闭" value="closed" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </el-form-item>
          <el-form-item label="支付状态">
            <el-select
              v-model="searchForm.paymentStatus"
              placeholder="全部支付状态"
              clearable
              style="width: 160px"
            >
              <el-option label="全部支付状态" value="" />
              <el-option label="待支付" value="unpaid" />
              <el-option label="已支付" value="paid" />
              <el-option label="已退款" value="refunded" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索企业名称、培训项目..."
              style="width: 300px"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">搜索</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="action-buttons">
        <el-button type="success" @click="onAdd">
          <el-icon><Plus /></el-icon>
          + 新建企业培训订单
        </el-button>
      </div>
    </div>

    <!-- 简单的表格 -->
    <div class="table-section">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderNumber" label="订单号" width="150" />
        <el-table-column prop="companyName" label="企业名称" width="200" />
        <el-table-column prop="trainingProject" label="培训项目" width="250" />
        <el-table-column prop="traineeCount" label="培训人数" width="100">
          <template #default="scope"> {{ scope.row.traineeCount }}人 </template>
        </el-table-column>
        <el-table-column prop="trainingPeriod" label="培训周期" width="200">
          <template #default="scope">
            {{
              Array.isArray(scope.row.trainingPeriod)
                ? scope.row.trainingPeriod.join(' - ')
                : scope.row.trainingPeriod
            }}
          </template>
        </el-table-column>
        <el-table-column prop="totalFee" label="订单金额" width="120">
          <template #default="scope">
            ¥{{ scope.row.totalFee ? scope.row.totalFee.toLocaleString() : '0' }}
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus" label="订单状态" width="120">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ getOrderStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" label="支付状态" width="120">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
              {{ getPaymentStatusText(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click.stop="onView(scope.row)">查看</el-button>
            <el-button size="small" type="warning" @click.stop="onEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click.stop="onDelete(scope.row)">删除</el-button>
            <el-button size="small" @click.stop="onOptLog(scope.row)">操作日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑抽屉 -->
    <AddEnterpriseTraining
      v-model:visible="drawerVisible"
      :edit-data="editData as any"
      @success="onSuccess"
    />

    <!-- 操作日志抽屉 -->
    <OptLog v-model:visible="optLogVisible" :order-data="currentOrderData" />

    <!-- 查看详情抽屉 -->
    <EnterpriseTrainingView
      v-if="viewVisible"
      v-model:visible="viewVisible"
      :order-data="viewData as any"
      @edit="onEditFromView"
      @view-full-log="onViewFullLogFromView"
      @payment-status-changed="onPaymentStatusChanged"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import AddEnterpriseTraining from './components/AddEnterpriseTraining.vue'
import OptLog from '../UniversityPracticeCenter/components/OptLog.vue'
// 直接导入组件，避免动态导入问题
import EnterpriseTrainingView from './components/EnterpriseTrainingView.vue'
import {
  EnterpriseTrainingOrderApi,
  type EnterpriseTrainingOrder,
  type EnterpriseTrainingOrderPageParams,
  type EnterpriseTrainingOrderStatistics
} from '@/api/OrderCenter/EnterpriseTraining'
import request from '@/config/axios'

// 本地类型定义
interface EnterpriseTrainingOrderLocal {
  id?: number
  orderNumber?: string
  companyName: string
  trainingProject: string
  traineeCount: number
  trainingPeriod: string[] // 改为数组类型以匹配组件
  totalFee: number
  orderStatus: string
  paymentStatus: string
  manager: string
  businessOpportunity: string
  lead: string
  contractFile?: string
  // 额外字段用于显示
  createTime?: string
  // 支付状态相关字段
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  operatorName?: string
  collectionRemark?: string
}

// 响应式数据
/** 搜索表单数据 */
const searchForm = ref({
  keyword: '',
  orderStatus: '',
  paymentStatus: ''
})

/** 表格数据 */
const tableData = ref<EnterpriseTrainingOrderLocal[]>([])

/** 统计数据 */
const statsData = ref<EnterpriseTrainingOrderStatistics>({
  totalOrders: 0,
  pendingOrders: 0,
  monthlyAmount: 0,
  completionRate: 0
})

/** 分页信息 */
const pagination = ref({
  page: 1,
  size: 20,
  total: 0
})

/** 加载状态 */
const loading = ref(false)

/** 抽屉显示状态 */
const drawerVisible = ref(false)
const optLogVisible = ref(false)
const viewVisible = ref(false)

/** 编辑数据 */
const editData = ref<any>(null)

/** 查看数据 */
const viewData = ref<any>(null)

/** 当前订单ID */
const currentOrderId = ref('')

/** 操作日志相关 */
const currentOrderData = ref<any>(null)

// 方法
/** 获取订单状态类型 */
const getOrderStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'primary',
    pending_payment: 'warning',
    pending_fulfillment: 'info',
    fulfilling: 'primary',
    completed: 'success',
    closed: 'info',
    approval_rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    pending_payment: '待支付',
    pending_fulfillment: '待履约',
    fulfilling: '履约中',
    completed: '已完成',
    closed: '已关闭',
    approval_rejected: '审批驳回'
  }
  return statusMap[status] || status
}

/** 获取支付状态类型 */
const getPaymentStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    unpaid: 'warning',
    pending: 'warning', // 添加pending状态
    paid: 'success',
    refunded: 'info',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取支付状态文本 */
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    unpaid: '未支付',
    pending: '待支付', // 添加pending状态
    paid: '已支付',
    refunded: '已退款',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 搜索 */
const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}

/** 重置搜索 */
const onReset = () => {
  searchForm.value = {
    keyword: '',
    orderStatus: '',
    paymentStatus: ''
  }
  onSearch()
}

/** 格式化日期 */
const formatDate = (timestamp: string | undefined) => {
  if (!timestamp) return ''
  try {
    const date = new Date(timestamp)
    if (isNaN(date.getTime())) return ''

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (error) {
    console.error('日期格式化失败:', error)
    return ''
  }
}

/** 获取商机和线索的下拉数据 */
const getDropdownData = async () => {
  try {
    const result = await EnterpriseTrainingOrderApi.getDropdownData({
      orderType: 'enterprise_training',
      businessLine: '企业培训'
    })
    return result
  } catch (error) {
    console.warn('获取下拉数据失败:', error)
    return null
  }
}

/** 获取列表数据 */
const fetchList = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params: EnterpriseTrainingOrderPageParams = {
      tenantId: 1, // 这里应该从用户信息或store中获取
      page: pagination.value.page,
      size: pagination.value.size,
      keyword: searchForm.value.keyword || undefined,
      orderStatus: searchForm.value.orderStatus || undefined,
      paymentStatus: searchForm.value.paymentStatus || undefined
    }

    console.log('调用列表接口参数:', params)
    const result = await EnterpriseTrainingOrderApi.getOrderPage(params)
    console.log('列表接口返回结果:', result)

    // 转换数据格式以匹配本地类型
    if (result.list && Array.isArray(result.list)) {
      tableData.value = result.list.map((item) => {
        const convertedItem = {
          id: item.id || 0,
          orderNumber: item.orderNo || '',
          companyName: item.enterpriseName || '',
          trainingProject: item.trainingProject || '',
          traineeCount: item.participantsCount || 0,
          trainingPeriod: item.trainingDuration ? item.trainingDuration.split(' - ') : ['', ''],
          totalFee: item.totalFee || 0,
          orderStatus: item.orderStatus || '',
          paymentStatus: item.paymentStatus || '',
          createTime: item.createTime || '',
          manager: item.managerName || '',
          businessOpportunity: item.opportunityId || '',
          lead: item.leadId || '',
          contractFile: item.contractFileUrl || ''
        }

        return convertedItem
      })
    } else {
      console.warn('API返回的list不是数组:', result.list)
      tableData.value = []
    }

    pagination.value.total = result.total
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
    // 设置默认数据，避免页面空白
    tableData.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

/** 获取统计数据 */
const fetchStats = async () => {
  try {
    const result = await EnterpriseTrainingOrderApi.getStatistics(1) // 这里应该从用户信息或store中获取租户ID
    statsData.value = result
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 使用默认数据
    statsData.value = {
      totalOrders: 0,
      pendingOrders: 0,
      monthlyAmount: 0,
      completionRate: 0
    }
  }
}

/** 新增 */
const onAdd = () => {
  editData.value = null
  drawerVisible.value = true
}

/** 编辑 */
const onEdit = async (row: EnterpriseTrainingOrderLocal) => {
  try {
    // 优先使用行数据中的ID，如果没有则使用默认值13
    const orderId = row.id || 13

    // 调用调试接口获取数据，使用实际的订单ID
    const result = await request.get({
      url: `/publicbiz/enterprise-training-order/debug-main/${orderId}`
    })

    // 获取商机和线索的下拉数据
    const dropdownData = await getDropdownData()

    // 根据ID查找对应的商机和线索信息
    const businessOpportunityInfo = dropdownData?.businessOptions?.find(
      (item) =>
        item.id.toString() === (result.businessOpportunity || row.businessOpportunity)?.toString()
    )
    const leadInfo = dropdownData?.leadOptions?.find(
      (item) => item.id.toString() === (result.lead || row.lead)?.toString()
    )

    // 将接口返回的数据与首页行数据进行合并，确保数据完整性
    const mergedData = {
      ...row, // 保留首页的原始数据
      ...result, // 用接口返回的数据覆盖
      // 特殊字段处理
      id: orderId, // 确保ID一致
      orderNumber: row.orderNumber || result.orderNumber, // 优先使用首页的订单号
      companyName: row.companyName || result.companyName, // 优先使用首页的企业名称
      trainingProject: row.trainingProject || result.trainingProject, // 优先使用首页的培训项目
      // 支付状态相关字段的智能合并
      paymentStatus: result.paymentStatus || row.paymentStatus, // 优先使用接口返回的支付状态
      collectionAmount: result.collectionAmount || row.collectionAmount,
      collectionMethod: result.collectionMethod || row.collectionMethod,
      collectionDate: result.collectionDate || row.collectionDate,
      operatorName: result.operatorName || row.operatorName,
      collectionRemark: result.collectionRemark || row.collectionRemark,
      // 商机和线索信息回显
      businessOpportunity: businessOpportunityInfo
        ? {
            id: businessOpportunityInfo.id,
            name: businessOpportunityInfo.name,
            customerName: businessOpportunityInfo.customerName,
            amount: businessOpportunityInfo.totalPrice,
            status: businessOpportunityInfo.businessStage,
            description: businessOpportunityInfo.businessType
          }
        : null,
      lead: leadInfo
        ? {
            id: leadInfo.id,
            name: leadInfo.leadId,
            companyName: leadInfo.customerName,
            contactPerson: leadInfo.customerName,
            phone: leadInfo.customerPhone,
            email: '',
            status: leadInfo.leadStatus
          }
        : null
    }

    editData.value = mergedData
    drawerVisible.value = true
  } catch (error) {
    console.error('获取编辑数据失败:', error)
    ElMessage.warning('调试接口调用失败，使用首页数据')

    // 如果调试接口失败，使用首页的行数据
    editData.value = { ...row }
    drawerVisible.value = true
  }
}

/** 查看详情 */
const onView = async (row: EnterpriseTrainingOrderLocal) => {
  try {
    // 优先使用行数据中的ID，如果没有则使用默认值13
    const orderId = row.id || 13

    // 调用调试接口获取完整数据
    const result = await request.get({
      url: `/publicbiz/enterprise-training-order/debug-main/${orderId}`
    })

    // 获取商机和线索的下拉数据
    const dropdownData = await getDropdownData()

    // 根据ID查找对应的商机和线索信息
    const businessOpportunityInfo = dropdownData?.businessOptions?.find(
      (item) =>
        item.id.toString() === (result.businessOpportunity || row.businessOpportunity)?.toString()
    )
    const leadInfo = dropdownData?.leadOptions?.find(
      (item) => item.id.toString() === (result.lead || row.lead)?.toString()
    )

    // 将接口返回的数据与首页行数据进行合并，确保数据完整性
    const viewDataFormatted = {
      ...row, // 保留首页的原始数据
      ...result, // 用接口返回的数据覆盖
      // 转换为查看组件期望的格式
      id: orderId,
      orderNumber: row.orderNumber || result.orderNumber,
      companyName: row.companyName || result.companyName,
      trainingProject: row.trainingProject || result.trainingProject,
      trainingPeriod: row.trainingPeriod || result.trainingPeriod,
      traineeCount: row.traineeCount || result.traineeCount,
      orderAmount: row.totalFee || result.orderAmount,
      orderStatus: row.orderStatus || result.orderStatus,
      paymentStatus: result.paymentStatus || row.paymentStatus, // 优先使用接口返回的支付状态
      manager: row.manager || result.manager,
      createTime: row.createTime || result.createTime,
      businessOpportunity: businessOpportunityInfo
        ? {
            id: businessOpportunityInfo.id,
            name: businessOpportunityInfo.name,
            customerName: businessOpportunityInfo.customerName,
            amount: businessOpportunityInfo.totalPrice,
            status: businessOpportunityInfo.businessStage,
            description: businessOpportunityInfo.businessType
          }
        : null,
      lead: leadInfo
        ? {
            id: leadInfo.id,
            name: leadInfo.leadId,
            companyName: leadInfo.customerName,
            contactPerson: leadInfo.customerName,
            phone: leadInfo.customerPhone,
            email: '',
            status: leadInfo.leadStatus
          }
        : null,
      contractFile: row.contractFile || result.contractFile,
      contractFileUrl: row.contractFile || result.contractFile,
      // 支付状态相关字段
      collectionAmount: result.collectionAmount || row.collectionAmount,
      collectionMethod: result.collectionMethod || row.collectionMethod,
      collectionDate: result.collectionDate || row.collectionDate,
      operatorName: result.operatorName || row.operatorName,
      collectionRemark: result.collectionRemark || row.collectionRemark
    }

    viewData.value = viewDataFormatted
    viewVisible.value = true
  } catch (error) {
    console.error('获取详情数据失败:', error)
    ElMessage.warning('接口调用失败，使用首页数据')

    // 如果接口失败，使用首页的行数据
    const viewDataFormatted = {
      id: row.id,
      orderNumber: row.orderNumber,
      companyName: row.companyName,
      trainingProject: row.trainingProject,
      trainingPeriod: row.trainingPeriod,
      traineeCount: row.traineeCount,
      orderAmount: row.totalFee,
      orderStatus: row.orderStatus,
      paymentStatus: row.paymentStatus,
      manager: row.manager,
      createTime: row.createTime,
      businessOpportunity: row.businessOpportunity,
      lead: row.lead,
      contractFile: row.contractFile,
      contractFileUrl: row.contractFile
    }

    viewData.value = viewDataFormatted
    viewVisible.value = true
  }
}

/** 从查看页面编辑 */
const onEditFromView = async (row: EnterpriseTrainingOrderLocal) => {
  try {
    viewVisible.value = false

    // 优先使用行数据中的ID，如果没有则使用默认值13
    const orderId = row.id || 13

    // 调用调试接口获取最新数据
    const result = await request.get({
      url: `/publicbiz/enterprise-training-order/debug-main/${orderId}`
    })

    // 获取商机和线索的下拉数据
    const dropdownData = await getDropdownData()

    // 根据ID查找对应的商机和线索信息
    const businessOpportunityInfo = dropdownData?.businessOptions?.find(
      (item) =>
        item.id.toString() === (result.businessOpportunity || row.businessOpportunity)?.toString()
    )
    const leadInfo = dropdownData?.leadOptions?.find(
      (item) => item.id.toString() === (result.lead || row.lead)?.toString()
    )

    // 将接口返回的数据与行数据进行合并
    const mergedData = {
      ...row, // 保留行数据
      ...result, // 用接口返回的数据覆盖
      id: orderId, // 确保ID一致
      // 支付状态相关字段的智能合并
      paymentStatus: result.paymentStatus || row.paymentStatus, // 优先使用接口返回的支付状态
      collectionAmount: result.collectionAmount || row.collectionAmount,
      collectionMethod: result.collectionMethod || row.collectionMethod,
      collectionDate: result.collectionDate || row.collectionDate,
      operatorName: result.operatorName || row.operatorName,
      collectionRemark: result.collectionRemark || row.collectionRemark,
      // 商机和线索信息回显
      businessOpportunity: businessOpportunityInfo
        ? {
            id: businessOpportunityInfo.id,
            name: businessOpportunityInfo.name,
            customerName: businessOpportunityInfo.customerName,
            amount: businessOpportunityInfo.totalPrice,
            status: businessOpportunityInfo.businessStage,
            description: businessOpportunityInfo.businessType
          }
        : null,
      lead: leadInfo
        ? {
            id: leadInfo.id,
            name: leadInfo.leadId,
            companyName: leadInfo.customerName,
            contactPerson: leadInfo.customerName,
            phone: leadInfo.customerPhone,
            email: '',
            status: leadInfo.leadStatus
          }
        : null
    }

    editData.value = mergedData
    drawerVisible.value = true
  } catch (error) {
    console.error('获取编辑数据失败:', error)
    ElMessage.warning('接口调用失败，使用当前数据')

    // 如果接口失败，使用当前行数据
    editData.value = { ...row }
    drawerVisible.value = true
  }
}

/** 从查看页面查看完整日志 */
const onViewFullLogFromView = (row: EnterpriseTrainingOrderLocal | null) => {
  if (row) {
    currentOrderId.value = row.orderNumber || ''
    optLogVisible.value = true
  }
}

/** 删除 */
const onDelete = async (row: EnterpriseTrainingOrderLocal) => {
  try {
    await ElMessageBox.confirm('确定要删除该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用删除API
    if (row.id) {
      await EnterpriseTrainingOrderApi.deleteOrder(row.id, 1) // 这里应该从用户信息或store中获取租户ID

      ElMessage.success('删除成功')

      // 重新获取数据
      fetchList()

      // 如果当前页没有数据了，且不是第一页，则跳转到上一页
      if (tableData.value.length === 0 && pagination.value.page > 1) {
        pagination.value.page--
      }
    } else {
      ElMessage.error('订单ID无效')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

/** 操作日志 */
const onOptLog = (row: EnterpriseTrainingOrderLocal) => {
  currentOrderId.value = row.orderNumber || ''
  // 转换为高校实践订单兼容的格式
  currentOrderData.value = {
    id: row.id,
    orderNo: row.orderNumber,
    orderType: 'enterprise_training',
    businessLine: '企业培训',
    projectName: row.trainingProject || '',
    projectDescription: '企业培训项目',
    startDate: row.trainingPeriod ? row.trainingPeriod[0] : '',
    endDate: row.trainingPeriod ? row.trainingPeriod[1] : '',
    totalFee: row.totalFee || 0,
    paidAmount: 0, // 收款金额
    paymentStatus: row.paymentStatus || 'unpaid',
    orderStatus: row.orderStatus || 'draft',
    managerId: 1,
    managerName: row.manager || '',
    managerPhone: '',
    contractType: 'electronic',
    contractFileUrl: row.contractFile || '',
    contractStatus: 'pending',
    remark: '企业培训订单',
    createTime: row.createTime || '',
    updateTime: row.createTime || '',
    // 企业培训特有字段映射
    enterpriseName: row.companyName || '',
    enterpriseContact: '',
    enterprisePhone: '',
    enterpriseEmail: '',
    enterpriseAddress: '',
    trainingProject: row.trainingProject || '',
    trainingDescription: '企业培训项目',
    participantsCount: row.traineeCount || 0,
    trainingDuration: row.trainingPeriod ? row.trainingPeriod.join(' - ') : '',
    trainingLocation: '',
    trainingType: 'enterprise_training',
    perPersonFee: row.totalFee && row.traineeCount ? row.totalFee / row.traineeCount : 0,
    materialFee: 0,
    certificationFee: 0,
    // 收款信息
    collectionAmount: 0,
    collectionMethod: '',
    collectionDate: '',
    operatorName: '',
    collectionRemark: ''
  }
  optLogVisible.value = true
}

/** 行点击事件 */
const onRowClick = (row: EnterpriseTrainingOrderLocal) => {
  onView(row)
}

/** 操作成功回调 */
const onSuccess = (updatedData?: any) => {
  drawerVisible.value = false

  if (updatedData && updatedData.id) {
    // 如果返回了更新后的数据，直接更新首页列表中的对应记录
    const orderIndex = tableData.value.findIndex((item) => item.id === updatedData.id)
    if (orderIndex !== -1) {
      // 更新首页列表中的数据
      tableData.value[orderIndex] = {
        ...tableData.value[orderIndex],
        ...updatedData,
        // 确保关键字段的映射正确
        orderNumber:
          updatedData.orderNumber || updatedData.orderNo || tableData.value[orderIndex].orderNumber,
        companyName:
          updatedData.companyName ||
          updatedData.enterpriseName ||
          tableData.value[orderIndex].companyName,
        trainingProject: updatedData.trainingProject || tableData.value[orderIndex].trainingProject,
        traineeCount:
          updatedData.traineeCount ||
          updatedData.participantsCount ||
          tableData.value[orderIndex].traineeCount,
        totalFee:
          updatedData.totalFee || updatedData.orderAmount || tableData.value[orderIndex].totalFee,
        orderStatus: updatedData.orderStatus || tableData.value[orderIndex].orderStatus,
        paymentStatus: updatedData.paymentStatus || tableData.value[orderIndex].paymentStatus,
        manager:
          updatedData.manager || updatedData.managerName || tableData.value[orderIndex].manager
      }

      ElMessage.success(`订单 ${updatedData.id} 信息已更新`)
    } else {
      // 如果找不到对应记录，刷新整个列表
      fetchList()
    }
  } else {
    // 如果没有返回数据，刷新整个列表
    fetchList()
  }

  // 刷新统计数据
  fetchStats()
}

/** 支付状态变化回调 */
const onPaymentStatusChanged = (data: {
  orderId: number | undefined
  paymentStatus: string
  collectionData: any
}) => {
  if (data.orderId) {
    // 更新首页列表中对应订单的支付状态
    const orderIndex = tableData.value.findIndex((item) => item.id === data.orderId)
    if (orderIndex !== -1) {
      // 更新支付状态
      tableData.value[orderIndex].paymentStatus = data.paymentStatus

      // 如果收款成功，更新相关收款信息
      if (data.paymentStatus === 'paid' && data.collectionData) {
        // 这里可以根据需要更新更多收款相关字段
      }

      ElMessage.success(
        `订单 ${data.orderId} 支付状态已更新为: ${getPaymentStatusText(data.paymentStatus)}`
      )
    } else {
      console.warn(`未找到订单ID为 ${data.orderId} 的记录`)
    }
  }

  // 刷新统计数据
  fetchStats()
}

/** 分页大小改变 */
const onSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  fetchList()
}

/** 当前页改变 */
const onCurrentChange = (page: number) => {
  pagination.value.page = page
  fetchList()
}

// 生命周期
onMounted(async () => {
  try {
    await fetchList()
    await fetchStats()
  } catch (error) {
    console.error('页面加载失败:', error)
    ElMessage.error('页面加载失败，请刷新重试')
  }
})
</script>

<style scoped lang="scss">
.enterprise-training-index {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20px;

  .statistics-section {
    margin-bottom: 20px;

    .stat-card {
      background: white;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      border-left: 4px solid #409eff;
      text-align: center;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #409eff, #67c23a);
      }

      .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .stat-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }
  }

  .search-section {
    background: #f5f5f5;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .search-form {
      flex: 1;
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      margin-left: 20px;
    }
  }

  .table-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;

    .pagination-section {
      padding: 24px;
      text-align: right;
      border-top: 1px solid #ebeef5;
      background: #fafafa;
    }
  }
}

/* 分页样式 */
.pagination-section {
  margin-top: 20px;
  text-align: right;
}
</style>
