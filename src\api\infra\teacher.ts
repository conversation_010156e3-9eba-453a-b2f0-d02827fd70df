import request from '@/config/axios'

// 获取师资统计
export const getTeacherStat = () => {
  return request.get({ url: '/publicbiz/teacher/stat' })
}

// 获取师资列表（分页）
export const getTeacherPage = (params: any) => {
  return request.get({ url: '/publicbiz/teacher/page', params })
}

// 获取师资详情
export const getTeacherDetail = (id: string | number) => {
  return request.get({ url: '/publicbiz/teacher/detail', params: { id } })
}

// 新增师资
export const createTeacher = (data: any) => {
  return request.post({ url: '/publicbiz/teacher/create', data })
}

// 更新师资
export const updateTeacher = (data: any) => {
  return request.put({ url: '/publicbiz/teacher/update', data })
}

// 删除师资
export const deleteTeacher = (id: string | number) => {
  return request.delete({ url: '/publicbiz/teacher/delete', params: { id } })
}

// 校验Excel导入的讲师数据
export const validateTeacherImport = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  return request.post({
    url: '/publicbiz/teacher/import/validate-excel',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 执行批量导入讲师（只导入校验通过的数据）
export const executeTeacherImport = (teacherList: any[]) => {
  return request.post({
    url: '/publicbiz/teacher/import/execute',
    data: {
      teacherList
    }
  })
}

// 导出师资
export const exportTeachers = (params: any) => {
  return request.get({
    url: '/publicbiz/teacher/export',
    params,
    responseType: 'blob'
  })
}
