# 企业培训订单 API 接口文档

## 概述

本文档描述了企业培训订单模块的RESTful API接口，包括订单管理、收款管理、合同管理、审批流程、操作日志、数据导出和统计分析等功能。

**基础路径**: `/api/enterprise-training-order`

**认证方式**: Bearer Token

**内容类型**: `application/json`

## 接口列表

### 1. 分页查询企业培训订单

**接口地址**: `GET /api/enterprise-training-order/page`

**接口描述**: 分页查询企业培训订单列表，支持多条件筛选

**请求参数**:

| 参数名        | 类型    | 必填 | 说明                             | 示例值           |
| ------------- | ------- | ---- | -------------------------------- | ---------------- |
| tenantId      | Long    | 是   | 租户ID                           | 1                |
| page          | Integer | 否   | 页码，默认1                      | 1                |
| size          | Integer | 否   | 每页大小，默认20                 | 20               |
| orderStatus   | String  | 否   | 订单状态                         | pending_approval |
| paymentStatus | String  | 否   | 支付状态                         | unpaid           |
| keyword       | String  | 否   | 搜索关键词（企业名称、培训项目） | 数字化转型       |
| startDate     | String  | 否   | 开始日期（YYYY-MM-DD）           | 2024-06-01       |
| endDate       | String  | 否   | 结束日期（YYYY-MM-DD）           | 2024-06-30       |

**请求示例**:

```bash
GET /api/enterprise-training-order/page?tenantId=1&page=1&size=20&orderStatus=pending_approval&keyword=数字化转型
```

**响应参数**:

| 参数名     | 类型    | 说明                |
| ---------- | ------- | ------------------- |
| code       | Integer | 响应码，200表示成功 |
| data       | Object  | 响应数据            |
| data.list  | Array   | 订单列表            |
| data.total | Long    | 总记录数            |
| message    | String  | 响应消息            |

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1024,
        "orderNo": "ET202406001",
        "orderType": "enterprise_training",
        "businessLine": "training",
        "enterpriseName": "ABC科技有限公司",
        "trainingProject": "数字化转型管理培训",
        "participantsCount": 25,
        "trainingDuration": "2024.07.01 - 2024.07.15",
        "totalAmount": 125000.0,
        "paidAmount": 0.0,
        "paymentStatus": "pending",
        "orderStatus": "pending_approval",
        "managerName": "李四",
        "createTime": "2024-06-20T10:00:00"
      }
    ],
    "total": 1
  },
  "message": "操作成功"
}
```

### 2. 获取企业培训订单详情

**接口地址**: `GET /api/enterprise-training-order/{id}`

**接口描述**: 根据订单ID获取企业培训订单的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 订单ID |

**请求参数**:

| 参数名   | 类型 | 必填 | 说明   | 示例值 |
| -------- | ---- | ---- | ------ | ------ |
| tenantId | Long | 是   | 租户ID | 1      |

**请求示例**:

```bash
GET /api/enterprise-training-order/1024?tenantId=1
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "id": 1024,
    "orderNo": "ET202406001",
    "orderType": "enterprise_training",
    "businessLine": "training",
    "opportunityId": "OPP001",
    "leadId": "LEAD001",
    "projectName": "数字化转型项目",
    "projectDescription": "为企业提供数字化转型管理培训服务",
    "startDate": "2024-07-01",
    "endDate": "2024-07-15",
    "totalAmount": 125000.0,
    "paidAmount": 0.0,
    "refundAmount": 0.0,
    "paymentStatus": "pending",
    "orderStatus": "pending_approval",
    "managerId": 1001,
    "managerName": "李四",
    "managerPhone": "***********",
    "contractType": "paper",
    "contractFileUrl": "",
    "contractStatus": "unsigned",
    "remark": "客户要求尽快启动培训",
    "enterpriseName": "ABC科技有限公司",
    "enterpriseContact": "张三",
    "enterprisePhone": "010-12345678",
    "enterpriseEmail": "<EMAIL>",
    "enterpriseAddress": "北京市朝阳区xxx街道xxx号",
    "trainingProject": "数字化转型管理培训",
    "trainingDescription": "为企业提供数字化转型管理培训服务",
    "participantsCount": 25,
    "trainingDuration": "2024.07.01 - 2024.07.15",
    "trainingLocation": "北京",
    "trainingType": "management_training",
    "perPersonFee": 5000.0,
    "totalFee": 125000.0,
    "materialFee": 5000.0,
    "certificationFee": 10000.0,
    "createTime": "2024-06-20T10:00:00",
    "updateTime": "2024-06-20T10:00:00"
  },
  "message": "操作成功"
}
```

### 3. 创建企业培训订单

**接口地址**: `POST /api/enterprise-training-order/create`

**接口描述**: 创建新的企业培训订单

**请求参数**:

| 参数名              | 类型       | 必填 | 说明         | 示例值                           |
| ------------------- | ---------- | ---- | ------------ | -------------------------------- |
| tenantId            | Long       | 是   | 租户ID       | 1                                |
| opportunityId       | String     | 否   | 关联商机ID   | OPP001                           |
| leadId              | String     | 否   | 关联线索ID   | LEAD001                          |
| enterpriseName      | String     | 是   | 企业名称     | ABC科技有限公司                  |
| enterpriseContact   | String     | 否   | 企业联系人   | 张三                             |
| enterprisePhone     | String     | 否   | 企业联系电话 | 010-12345678                     |
| enterpriseEmail     | String     | 否   | 企业联系邮箱 | <EMAIL>                  |
| enterpriseAddress   | String     | 否   | 企业地址     | 北京市朝阳区xxx街道xxx号         |
| trainingProject     | String     | 是   | 培训项目名称 | 数字化转型管理培训               |
| trainingDescription | String     | 否   | 培训项目描述 | 为企业提供数字化转型管理培训服务 |
| participantsCount   | Integer    | 是   | 培训人数     | 25                               |
| trainingDuration    | String     | 是   | 培训周期     | 2024.07.01 - 2024.07.15          |
| trainingLocation    | String     | 否   | 培训地点     | 北京                             |
| trainingType        | String     | 否   | 培训类型     | management_training              |
| perPersonFee        | BigDecimal | 否   | 人均培训费   | 5000.00                          |
| totalFee            | BigDecimal | 否   | 总培训费     | 125000.00                        |
| materialFee         | BigDecimal | 否   | 教材费       | 5000.00                          |
| certificationFee    | BigDecimal | 否   | 认证费       | 10000.00                         |
| managerId           | Long       | 是   | 负责人ID     | 1001                             |
| managerName         | String     | 是   | 负责人姓名   | 李四                             |
| managerPhone        | String     | 否   | 负责人电话   | ***********                      |
| remark              | String     | 否   | 备注         | 客户要求尽快启动培训             |

**请求示例**:

```json
{
  "tenantId": 1,
  "opportunityId": "OPP001",
  "enterpriseName": "ABC科技有限公司",
  "enterpriseContact": "张三",
  "enterprisePhone": "010-12345678",
  "enterpriseEmail": "<EMAIL>",
  "enterpriseAddress": "北京市朝阳区xxx街道xxx号",
  "trainingProject": "数字化转型管理培训",
  "trainingDescription": "为企业提供数字化转型管理培训服务",
  "participantsCount": 25,
  "trainingDuration": "2024.07.01 - 2024.07.15",
  "trainingLocation": "北京",
  "trainingType": "management_training",
  "perPersonFee": 5000.0,
  "totalFee": 125000.0,
  "materialFee": 5000.0,
  "certificationFee": 10000.0,
  "managerId": 1001,
  "managerName": "李四",
  "managerPhone": "***********",
  "remark": "客户要求尽快启动培训"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "id": 1024,
    "orderNo": "ET202406001",
    "enterpriseName": "ABC科技有限公司",
    "trainingProject": "数字化转型管理培训",
    "participantsCount": 25,
    "totalFee": 125000.0,
    "orderStatus": "draft",
    "createTime": "2024-06-20T10:00:00"
  },
  "message": "创建成功"
}
```

### 4. 更新企业培训订单

**接口地址**: `PUT /api/enterprise-training-order/update`

**接口描述**: 更新企业培训订单信息

**请求参数**:

| 参数名            | 类型       | 必填 | 说明         | 示例值                  |
| ----------------- | ---------- | ---- | ------------ | ----------------------- |
| id                | Long       | 是   | 订单ID       | 1024                    |
| tenantId          | Long       | 是   | 租户ID       | 1                       |
| enterpriseName    | String     | 是   | 企业名称     | ABC科技有限公司         |
| trainingProject   | String     | 是   | 培训项目名称 | 数字化转型管理培训      |
| participantsCount | Integer    | 是   | 培训人数     | 30                      |
| trainingDuration  | String     | 是   | 培训周期     | 2024.07.01 - 2024.07.20 |
| totalFee          | BigDecimal | 否   | 总培训费     | 150000.00               |
| managerId         | Long       | 是   | 负责人ID     | 1001                    |
| managerName       | String     | 是   | 负责人姓名   | 李四                    |

**请求示例**:

```json
{
  "id": 1024,
  "tenantId": 1,
  "enterpriseName": "ABC科技有限公司",
  "trainingProject": "数字化转型管理培训",
  "participantsCount": 30,
  "trainingDuration": "2024.07.01 - 2024.07.20",
  "totalFee": 150000.0,
  "managerId": 1001,
  "managerName": "李四"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": true,
  "message": "更新成功"
}
```

### 5. 删除企业培训订单

**接口地址**: `DELETE /api/enterprise-training-order/{id}`

**接口描述**: 删除指定的企业培训订单

**路径参数**:

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 订单ID |

**请求参数**:

| 参数名   | 类型 | 必填 | 说明   | 示例值 |
| -------- | ---- | ---- | ------ | ------ |
| tenantId | Long | 是   | 租户ID | 1      |

**请求示例**:

```bash
DELETE /api/enterprise-training-order/1024?tenantId=1
```

**响应示例**:

```json
{
  "code": 200,
  "data": true,
  "message": "删除成功"
}
```

### 6. 确认收款

**接口地址**: `POST /api/enterprise-training-order/payment/confirm`

**接口描述**: 确认企业培训订单的收款

**请求参数**:

| 参数名        | 类型       | 必填 | 说明         | 示例值              |
| ------------- | ---------- | ---- | ------------ | ------------------- |
| orderId       | Long       | 是   | 订单ID       | 1024                |
| tenantId      | Long       | 是   | 租户ID       | 1                   |
| paymentType   | String     | 是   | 支付类型     | bank_transfer       |
| paymentAmount | BigDecimal | 是   | 支付金额     | 125000.00           |
| paymentTime   | String     | 是   | 支付时间     | 2024-06-20 15:30:00 |
| operatorId    | Long       | 是   | 操作人ID     | 1001                |
| operatorName  | String     | 是   | 操作人姓名   | 王五                |
| paymentRemark | String     | 否   | 支付备注     | 银行转账收款        |
| transactionId | String     | 否   | 第三方交易号 | TX20240620001       |

**请求示例**:

```json
{
  "orderId": 1024,
  "tenantId": 1,
  "paymentType": "bank_transfer",
  "paymentAmount": 125000.0,
  "paymentTime": "2024-06-20 15:30:00",
  "operatorId": 1001,
  "operatorName": "王五",
  "paymentRemark": "银行转账收款",
  "transactionId": "TX20240620001"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "success": true,
    "paymentNo": "PAY20240620001",
    "message": "收款确认成功"
  },
  "message": "操作成功"
}
```

### 7. 更新收款信息

**接口地址**: `PUT /api/enterprise-training-order/payment/update`

**接口描述**: 更新企业培训订单的收款信息

**请求参数**:

| 参数名        | 类型       | 必填 | 说明       | 示例值              |
| ------------- | ---------- | ---- | ---------- | ------------------- |
| paymentId     | Long       | 是   | 支付记录ID | 2001                |
| tenantId      | Long       | 是   | 租户ID     | 1                   |
| paymentAmount | BigDecimal | 否   | 支付金额   | 130000.00           |
| paymentTime   | String     | 否   | 支付时间   | 2024-06-21 10:00:00 |
| paymentRemark | String     | 否   | 支付备注   | 调整收款金额        |
| operatorId    | Long       | 是   | 操作人ID   | 1001                |
| operatorName  | String     | 是   | 操作人姓名 | 王五                |

**请求示例**:

```json
{
  "paymentId": 2001,
  "tenantId": 1,
  "paymentAmount": 130000.0,
  "paymentTime": "2024-06-21 10:00:00",
  "paymentRemark": "调整收款金额",
  "operatorId": 1001,
  "operatorName": "王五"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": true,
  "message": "更新成功"
}
```

### 8. 上传合同

**接口地址**: `POST /api/enterprise-training-order/contract/upload`

**接口描述**: 上传企业培训订单的合同文件

**请求参数**:

| 参数名         | 类型       | 必填 | 说明         | 示例值          |
| -------------- | ---------- | ---- | ------------ | --------------- |
| orderId        | Long       | 是   | 订单ID       | 1024            |
| tenantId       | Long       | 是   | 租户ID       | 1               |
| contractFile   | String     | 是   | 合同文件     | contract.pdf    |
| contractName   | String     | 是   | 合同名称     | ABC科技培训合同 |
| contractNumber | String     | 否   | 合同编号     | CT202406001     |
| startDate      | String     | 否   | 合同开始日期 | 2024-07-01      |
| endDate        | String     | 否   | 合同结束日期 | 2024-07-15      |
| amount         | BigDecimal | 否   | 合同金额     | 125000.00       |
| signer         | String     | 否   | 签约人       | 张三            |

**请求示例**:

```json
{
  "orderId": 1024,
  "tenantId": 1,
  "contractFile": "contract.pdf",
  "contractName": "ABC科技培训合同",
  "contractNumber": "CT202406001",
  "startDate": "2024-07-01",
  "endDate": "2024-07-15",
  "amount": 125000.0,
  "signer": "张三"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "contractId": 3001,
    "contractName": "ABC科技培训合同",
    "contractNumber": "CT202406001",
    "contractType": "paper",
    "contractStatus": "uploaded",
    "contractFileUrl": "https://example.com/contracts/CT202406001.pdf",
    "startDate": "2024-07-01",
    "endDate": "2024-07-15",
    "amount": 125000.0,
    "signer": "张三",
    "attachmentPath": "/contracts/CT202406001.pdf",
    "success": true,
    "message": "纸质合同上传成功"
  },
  "message": "操作成功"
}
```

### 9. 提交合同

**接口地址**: `POST /api/enterprise-training-order/contract/submit`

**接口描述**: 提交企业培训订单的合同

**请求参数**:

| 参数名         | 类型   | 必填 | 说明     | 示例值         |
| -------------- | ------ | ---- | -------- | -------------- |
| orderId        | Long   | 是   | 订单ID   | 1024           |
| tenantId       | Long   | 是   | 租户ID   | 1              |
| contractStatus | String | 是   | 合同状态 | signed         |
| submitRemark   | String | 否   | 提交备注 | 合同已签署完成 |

**请求示例**:

```json
{
  "orderId": 1024,
  "tenantId": 1,
  "contractStatus": "signed",
  "submitRemark": "合同已签署完成"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": true,
  "message": "提交成功"
}
```

### 10. 获取合同信息

**接口地址**: `GET /api/enterprise-training-order/contract/{orderId}`

**接口描述**: 获取企业培训订单的合同信息

**路径参数**:

| 参数名  | 类型 | 必填 | 说明   |
| ------- | ---- | ---- | ------ |
| orderId | Long | 是   | 订单ID |

**请求参数**:

| 参数名   | 类型 | 必填 | 说明   | 示例值 |
| -------- | ---- | ---- | ------ | ------ |
| tenantId | Long | 是   | 租户ID | 1      |

**请求示例**:

```bash
GET /api/enterprise-training-order/contract/1024?tenantId=1
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "contractId": 3001,
    "contractName": "ABC科技培训合同",
    "contractNumber": "CT202406001",
    "contractType": "paper",
    "contractStatus": "signed",
    "contractFileUrl": "https://example.com/contracts/CT202406001.pdf",
    "startDate": "2024-07-01",
    "endDate": "2024-07-15",
    "amount": 125000.0,
    "signer": "张三",
    "attachmentPath": "/contracts/CT202406001.pdf"
  },
  "message": "操作成功"
}
```

### 11. 发起审批

**接口地址**: `POST /api/enterprise-training-order/approval/initiate`

**接口描述**: 发起企业培训订单的审批流程

**请求参数**:

| 参数名          | 类型       | 必填 | 说明         | 示例值                              |
| --------------- | ---------- | ---- | ------------ | ----------------------------------- |
| orderId         | Long       | 是   | 订单ID       | 1024                                |
| tenantId        | Long       | 是   | 租户ID       | 1                                   |
| approvalType    | String     | 是   | 审批类型     | order_approval                      |
| approvalTitle   | String     | 是   | 审批标题     | 企业培训订单审批                    |
| approvalContent | String     | 是   | 审批内容     | 请审批ABC科技数字化转型管理培训订单 |
| approverIds     | List<Long> | 是   | 审批人ID列表 | [1001, 1002]                        |
| priority        | String     | 否   | 优先级       | normal                              |

**请求示例**:

```json
{
  "orderId": 1024,
  "tenantId": 1,
  "approvalType": "order_approval",
  "approvalTitle": "企业培训订单审批",
  "approvalContent": "请审批ABC科技数字化转型管理培训订单",
  "approverIds": [1001, 1002],
  "priority": "normal"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "success": true,
    "approvalId": "AP20240620001",
    "message": "审批发起成功"
  },
  "message": "操作成功"
}
```

### 12. 审批通过

**接口地址**: `POST /api/enterprise-training-order/approval/approve`

**接口描述**: 审批通过企业培训订单

**请求参数**:

| 参数名           | 类型   | 必填 | 说明       | 示例值                 |
| ---------------- | ------ | ---- | ---------- | ---------------------- |
| approvalId       | String | 是   | 审批ID     | AP20240620001          |
| orderId          | Long   | 是   | 订单ID     | 1024                   |
| tenantId         | Long   | 是   | 租户ID     | 1                      |
| approverId       | Long   | 是   | 审批人ID   | 1001                   |
| approverName     | String | 是   | 审批人姓名 | 张三                   |
| approvalComments | String | 否   | 审批意见   | 培训需求明确，批准立项 |
| nextStatus       | String | 是   | 下一状态   | approved               |

**请求示例**:

```json
{
  "approvalId": "AP20240620001",
  "orderId": 1024,
  "tenantId": 1,
  "approverId": 1001,
  "approverName": "张三",
  "approvalComments": "培训需求明确，批准立项",
  "nextStatus": "approved"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": true,
  "message": "审批通过成功"
}
```

### 13. 审批拒绝

**接口地址**: `POST /api/enterprise-training-order/approval/reject`

**接口描述**: 审批拒绝企业培训订单

**请求参数**:

| 参数名       | 类型   | 必填 | 说明       | 示例值           |
| ------------ | ------ | ---- | ---------- | ---------------- |
| approvalId   | String | 是   | 审批ID     | AP20240620001    |
| orderId      | Long   | 是   | 订单ID     | 1024             |
| tenantId     | Long   | 是   | 租户ID     | 1                |
| approverId   | Long   | 是   | 审批人ID   | 1001             |
| approverName | String | 是   | 审批人姓名 | 张三             |
| rejectReason | String | 是   | 拒绝原因   | 培训预算超出范围 |
| nextStatus   | String | 是   | 下一状态   | rejected         |

**请求示例**:

```json
{
  "approvalId": "AP20240620001",
  "orderId": 1024,
  "tenantId": 1,
  "approverId": 1001,
  "approverName": "张三",
  "rejectReason": "培训预算超出范围",
  "nextStatus": "rejected"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": true,
  "message": "审批拒绝成功"
}
```

### 14. 获取审批记录

**接口地址**: `GET /api/enterprise-training-order/approval/records/{orderId}`

**接口描述**: 获取企业培训订单的审批记录

**路径参数**:

| 参数名  | 类型 | 必填 | 说明   |
| ------- | ---- | ---- | ------ |
| orderId | Long | 是   | 订单ID |

**请求参数**:

| 参数名   | 类型 | 必填 | 说明   | 示例值 |
| -------- | ---- | ---- | ------ | ------ |
| tenantId | Long | 是   | 租户ID | 1      |

**请求示例**:

```bash
GET /api/enterprise-training-order/approval/records/1024?tenantId=1
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "approvalId": "AP20240620001",
        "approvalNo": "AP001",
        "approvalType": "order_approval",
        "status": "approved",
        "operatorId": 1001,
        "operatorName": "张三",
        "operatorRole": "部门经理",
        "action": "批准了订单",
        "comments": "已确认合作意向,批准立项",
        "createTime": "2024-06-20T15:30:00",
        "updateTime": "2024-06-20T15:30:00"
      }
    ],
    "total": 1
  },
  "message": "操作成功"
}
```

### 15. 获取操作日志

**接口地址**: `GET /api/enterprise-training-order/logs`

**接口描述**: 获取企业培训订单的操作日志

**请求参数**:

| 参数名   | 类型    | 必填 | 说明             | 示例值      |
| -------- | ------- | ---- | ---------------- | ----------- |
| orderNo  | String  | 是   | 订单号           | ET202406001 |
| tenantId | Long    | 是   | 租户ID           | 1           |
| page     | Integer | 否   | 页码，默认1      | 1           |
| size     | Integer | 否   | 每页大小，默认20 | 20          |

**请求示例**:

```bash
GET /api/enterprise-training-order/logs?orderNo=ET202406001&tenantId=1&page=1&size=20
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "orderNo": "ET202406001",
        "logType": "订单创建",
        "logTitle": "创建企业培训订单",
        "logContent": "创建了ABC科技数字化转型管理培训订单",
        "oldStatus": "",
        "newStatus": "draft",
        "operatorId": 1001,
        "operatorName": "张三",
        "operatorRole": "管理员",
        "relatedPartyType": "企业",
        "relatedPartyName": "ABC科技有限公司",
        "createTime": "2024-06-20T10:00:00"
      }
    ],
    "total": 1
  },
  "message": "操作成功"
}
```

### 16. 导出企业培训订单

**接口地址**: `POST /api/enterprise-training-order/export`

**接口描述**: 导出企业培训订单数据

**请求参数**:

| 参数名        | 类型   | 必填 | 说明                  | 示例值     |
| ------------- | ------ | ---- | --------------------- | ---------- |
| tenantId      | Long   | 是   | 租户ID                | 1          |
| orderStatus   | String | 否   | 订单状态              | completed  |
| paymentStatus | String | 否   | 支付状态              | paid       |
| keyword       | String | 否   | 搜索关键词            | 数字化转型 |
| startDate     | String | 否   | 开始日期              | 2024-06-01 |
| endDate       | String | 否   | 结束日期              | 2024-06-30 |
| exportType    | String | 是   | 导出类型（excel/csv） | excel      |

**请求示例**:

```json
{
  "tenantId": 1,
  "orderStatus": "completed",
  "paymentStatus": "paid",
  "keyword": "数字化转型",
  "startDate": "2024-06-01",
  "endDate": "2024-06-30",
  "exportType": "excel"
}
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "success": true,
    "downloadUrl": "https://example.com/exports/enterprise_training_orders.xlsx",
    "fileName": "enterprise_training_orders.xlsx",
    "message": "数据导出成功"
  },
  "message": "操作成功"
}
```

### 17. 获取统计数据

**接口地址**: `GET /api/enterprise-training-order/statistics`

**接口描述**: 获取企业培训订单的统计数据

**请求参数**:

| 参数名    | 类型   | 必填 | 说明     | 示例值     |
| --------- | ------ | ---- | -------- | ---------- |
| tenantId  | Long   | 是   | 租户ID   | 1          |
| startDate | String | 否   | 开始日期 | 2024-06-01 |
| endDate   | String | 否   | 结束日期 | 2024-06-30 |

**请求示例**:

```bash
GET /api/enterprise-training-order/statistics?tenantId=1&startDate=2024-06-01&endDate=2024-06-30
```

**响应示例**:

```json
{
  "code": 200,
  "data": {
    "totalOrders": 18,
    "pendingOrders": 3,
    "monthlyAmount": 2135220.0,
    "completionRate": 92.5
  },
  "message": "操作成功"
}
```

## 数据字典

### 订单状态 (orderStatus)

| 状态值              | 状态名称 | 说明                     |
| ------------------- | -------- | ------------------------ |
| draft               | 草稿     | 订单刚创建，处于草稿状态 |
| pending_approval    | 待审批   | 订单已提交，等待审批     |
| approving           | 审批中   | 订单正在审批流程中       |
| approved            | 已批准   | 订单审批通过             |
| rejected            | 已拒绝   | 订单审批被拒绝           |
| pending_payment     | 待支付   | 订单已批准，等待客户支付 |
| pending_fulfillment | 待履约   | 订单已支付，等待履约     |
| fulfilling          | 履约中   | 订单正在履约过程中       |
| completed           | 已完成   | 订单履约完成             |
| closed              | 已关闭   | 订单已关闭               |
| approval_rejected   | 审批驳回 | 订单审批被驳回           |

### 支付状态 (paymentStatus)

| 状态值    | 状态名称 | 说明             |
| --------- | -------- | ---------------- |
| pending   | 待支付   | 订单等待客户支付 |
| paid      | 已支付   | 订单已收到支付   |
| refunded  | 已退款   | 订单已退款       |
| cancelled | 已取消   | 订单支付已取消   |

### 支付类型 (paymentType)

| 类型值        | 类型名称  | 说明          |
| ------------- | --------- | ------------- |
| cash          | 现金      | 现金支付      |
| wechat        | 微信支付  | 微信支付      |
| alipay        | 支付宝    | 支付宝支付    |
| bank_transfer | 银行转账  | 银行转账支付  |
| pos           | POS机刷卡 | POS机刷卡支付 |
| other         | 其他      | 其他支付方式  |

### 培训类型 (trainingType)

| 类型值                 | 类型名称 | 说明       |
| ---------------------- | -------- | ---------- |
| skill_training         | 技能培训 | 技能类培训 |
| management_training    | 管理培训 | 管理类培训 |
| certification_training | 认证培训 | 认证类培训 |

## 错误码

| 错误码        | 错误信息                     | 说明                         |
| ------------- | ---------------------------- | ---------------------------- |
| 1-000-000-000 | 企业培训订单不存在           | 指定的订单ID不存在           |
| 1-000-000-001 | 企业培训订单状态不允许操作   | 当前订单状态不允许执行该操作 |
| 1-000-000-002 | 企业培训订单创建失败         | 创建订单时发生错误           |
| 1-000-000-003 | 企业培训订单更新失败         | 更新订单时发生错误           |
| 1-000-000-004 | 企业培训订单删除失败         | 删除订单时发生错误           |
| 1-000-000-005 | 企业培训订单收款确认失败     | 收款确认时发生错误           |
| 1-000-000-006 | 企业培训订单收款更新失败     | 收款更新时发生错误           |
| 1-000-000-007 | 企业培训订单合同上传失败     | 合同上传时发生错误           |
| 1-000-000-008 | 企业培训订单合同提交失败     | 合同提交时发生错误           |
| 1-000-000-009 | 企业培训订单审批发起失败     | 审批发起时发生错误           |
| 1-000-000-010 | 企业培训订单审批操作失败     | 审批操作时发生错误           |
| 1-000-000-011 | 企业培训订单操作日志记录失败 | 操作日志记录时发生错误       |

## 注意事项

1. **认证要求**: 所有接口都需要有效的Bearer Token进行身份认证
2. **权限控制**: 不同角色的用户只能访问其权限范围内的数据
3. **租户隔离**: 所有数据操作都需要指定有效的租户ID
4. **参数验证**: 所有必填参数都会进行验证，参数不合法会返回相应的错误信息
5. **分页查询**: 分页查询接口默认每页20条记录，最大每页100条
6. **日期格式**: 日期参数统一使用 `YYYY-MM-DD` 格式，时间参数使用 `YYYY-MM-DD HH:mm:ss` 格式
7. **金额精度**: 所有金额字段使用 `BigDecimal` 类型，保留2位小数
8. **状态流转**: 订单状态变更需要遵循预定义的状态流转规则

## 更新日志

| 版本  | 日期       | 更新内容                                         |
| ----- | ---------- | ------------------------------------------------ |
| 1.0.0 | 2024-06-20 | 初始版本，包含完整的CRUD、收款、合同、审批等功能 |
