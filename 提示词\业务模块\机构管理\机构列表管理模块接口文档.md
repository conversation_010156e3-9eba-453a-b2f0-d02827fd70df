# 机构列表管理模块API接口文档

## 1. 模块概述

本模块提供机构管理相关的API接口，包括机构列表查询、审核详情查询、审核操作等功能。

### 数据库表结构

- **主表**：`publicbiz_agency` - 机构主表
- **关联表**：`publicbiz_agency_qualification` - 机构资质文件表

---

## 2. 接口列表

### 2.1 机构列表查询接口

**接口地址**：`GET /publicbiz/agency/page`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页条数，默认20 |
| keyword | string | 否 | 机构名称或机构ID模糊搜索 |
| cooperationStatus | string | 否 | 合作状态：cooperating-合作中/suspended-已暂停/terminated-已终止 |
| reviewStatus | string | 否 | 审核状态：pending-待审核/approved-已通过/rejected-已拒绝 |
| district | string | 否 | 所属区县 |
| agencyNo | string | 否 | 机构编码 |

**响应字段**：

| 字段名                    | 类型   | 说明       |
| ------------------------- | ------ | ---------- |
| code                      | int    | 响应状态码 |
| message                   | string | 响应消息   |
| data                      | array  | 机构列表   |
| data[].id                 | long   | 机构ID     |
| data[].agency_name        | string | 机构名称   |
| data[].agency_no          | string | 机构编号   |
| data[].cooperation_status | string | 合作状态   |
| data[].review_status      | string | 审核状态   |
| data[].contact_person     | string | 联系人     |
| data[].contact_phone      | string | 联系电话   |
| data[].province           | string | 省份       |
| data[].city               | string | 城市       |
| data[].district           | string | 区县       |
| data[].application_time   | string | 申请时间   |
| total                     | int    | 总条数     |
| pageNum                   | int    | 当前页码   |
| pageSize                  | int    | 每页条数   |

**响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "agency_name": "测试机构",
      "agency_no": "AG001",
      "cooperation_status": "cooperating",
      "review_status": "pending",
      "contact_person": "张三",
      "contact_phone": "***********",
      "province": "四川省",
      "city": "成都市",
      "district": "高新区",
      "application_time": "2024-01-01 10:00:00"
    }
  ],
  "total": 100,
  "pageNum": 1,
  "pageSize": 20,
  
}
```

### 2.2 审核详情查询接口

**接口地址**：`GET /publicbiz/agency/get/{id}`

**路径参数**：

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | long | 是   | 机构ID |

**响应字段**：

| 字段名                          | 类型   | 说明             |
| ------------------------------- | ------ | ---------------- |
| code                            | int    | 响应状态码       |
| message                         | string | 响应消息         |
| data                            | object | 响应数据         |
| data.id                         | long   | 机构ID           |
| data.agency_name                | string | 机构名称         |
| data.agency_short_name          | string | 机构简称         |
| data.agency_no                  | string | 机构编号         |
| data.agency_type                | string | 机构类型         |
| data.legal_representative       | string | 法人代表         |
| data.unified_social_credit_code | string | 统一社会信用代码 |
| data.establishment_date         | string | 成立日期         |
| data.registered_address         | string | 注册地址         |
| data.operating_address          | string | 经营地址         |
| data.business_scope             | string | 经营范围         |
| data.applicant_name             | string | 申请人姓名       |
| data.applicant_phone            | string | 申请人电话       |
| data.application_time           | string | 申请时间         |
| data.contact_person             | string | 联系人           |
| data.contact_phone              | string | 联系电话         |
| data.contact_email              | string | 联系邮箱         |
| data.province                   | string | 省份             |
| data.city                       | string | 城市             |
| data.district                   | string | 区县             |
| data.cooperation_status         | string | 合作状态         |
| data.review_status              | string | 审核状态         |
| data.qualifications             | array  | 资质文件列表     |
| data.qualifications[].file_type | string | 文件类型         |
| data.qualifications[].file_name | string | 文件名           |
| data.qualifications[].file_url  | string | 文件URL          |

**响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "agency_name": "测试机构",
    "agency_short_name": "测试",
    "agency_no": "AG001",
    "agency_type": "cooperation",
    "legal_representative": "李四",
    "unified_social_credit_code": "91110000123456789X",
    "establishment_date": "2020-01-01",
    "registered_address": "四川省成都市高新区",
    "operating_address": "四川省成都市高新区",
    "business_scope": "家政服务",
    "applicant_name": "张三",
    "applicant_phone": "***********",
    "application_time": "2024-01-01 10:00:00",
    "contact_person": "张三",
    "contact_phone": "***********",
    "contact_email": "<EMAIL>",
    "province": "四川省",
    "city": "成都市",
    "district": "高新区",
    "cooperation_status": "cooperating",
    "review_status": "pending",
    "qualifications": [
      {
        "file_type": "business_license",
        "file_name": "营业执照.jpg",
        "file_url": "https://example.com/files/business_license.jpg"
      }
    ]
  }
}
```

### 2.3 审核更新接口

**接口地址**：`PUT /publicbiz/agency/update`

**请求参数**：

| 参数名       | 类型   | 必填 | 说明                                      |
| ------------ | ------ | ---- | ----------------------------------------- |
| id           | long   | 是   | 机构ID                                    |
| reviewStatus | string | 是   | 审核状态：approved-已通过/rejected-已拒绝 |
| reviewRemark | string | 否   | 审核备注                                  |

**请求示例**：

```json
{
  "id": 1,
  "reviewStatus": "approved",
  "reviewRemark": "审核通过，资料齐全"
}
```

**响应字段**：

| 字段名  | 类型   | 说明                         |
| ------- | ------ | ---------------------------- |
| code    | int    | 响应状态码                   |
| message | string | 响应消息                     |
| data    | object | 响应数据（审核接口返回null） |

**响应示例**：

```json
{
  "code": 200,
  "message": "审核成功",
  "data": null
}
```

---

## 3. 数据字典

### 3.1 合作状态 (cooperation_status)

- cooperating：合作中
- suspended：已暂停
- terminated：已终止
- pending：待审核

### 3.2 审核状态 (review_status)

- pending：待审核
- approved：已通过
- rejected：已拒绝

### 3.3 文件类型 (file_type)

- business_license：营业执照
- qualification_cert：资质证书
- contract：合同文件
- human_resources：人力资源服务许可证
- opening_permit：开户许可证
- door_photo：门头照
- organizational_structure：组织机构代码证书
- id_card：法人身份证
- other：其他附件

---

## 4. 错误码说明

| 错误码 | 说明           |
| ------ | -------------- |
| 200    | 成功           |
| 400    | 请求参数错误   |
| 401    | 未授权         |
| 403    | 权限不足       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |
