<!--
  页面名称：审批弹窗
  功能描述：支持审批通过和审批拒绝操作，调用相应的API接口
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="订单审批"
    width="700px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="approval-container">
      <!-- 审批表单 -->
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="审批结果*" prop="approvalResult">
          <el-radio-group v-model="form.approvalResult" @change="onApprovalResultChange">
            <el-radio value="approve">
              <el-icon color="#67c23a" style="margin-right: 4px"><Check /></el-icon>
              审批通过
            </el-radio>
            <el-radio value="reject">
              <el-icon color="#f56c6c" style="margin-right: 4px"><Close /></el-icon>
              审批驳回
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="驳回原因*" prop="rejectReason" v-if="form.approvalResult === 'reject'">
          <el-input
            v-model="form.rejectReason"
            type="textarea"
            :rows="4"
            placeholder="请输入驳回原因..."
          />
        </el-form-item>

        <el-form-item label="审批意见" prop="comments">
          <el-input
            v-model="form.comments"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见 (可选)..."
          />
        </el-form-item>

        <!-- 订单信息展示 -->
        <el-form-item label="订单信息">
          <div class="order-info">
            <div class="info-item">
              <span class="info-label">订单号：</span>
              <span class="info-value">{{ orderData?.orderNo }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">项目名称：</span>
              <span class="info-value">{{ orderData?.projectName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">订单金额：</span>
              <span class="info-value amount"
                >¥{{ formatAmount(orderData?.totalAmount || 0) }}</span
              >
            </div>
            <div class="info-item">
              <span class="info-label">当前状态：</span>
              <span class="info-value">
                <el-tag :type="getOrderStatusType(orderData?.orderStatus || '')">
                  {{ getOrderStatusText(orderData?.orderStatus || '') }}
                </el-tag>
              </span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :disabled="!form.approvalResult"
        >
          {{ form.approvalResult === 'approve' ? '审批通过' : '审批驳回' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import type { UniversityPracticeOrder } from '@/api/OrderCenter/UniversityPracticeCenter'
import { UniversityPracticeOrderApi } from '@/api/OrderCenter/UniversityPracticeCenter'

// Props
interface Props {
  visible: boolean
  orderData?: UniversityPracticeOrder
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: undefined
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)

// 表单数据
const form = ref({
  approvalResult: 'approve', // 审批结果：approve(通过) 或 reject(驳回)
  rejectReason: '', // 驳回原因
  comments: '' // 审批意见
})

// 表单校验规则
const rules = {
  approvalResult: [{ required: true, message: '请选择审批结果', trigger: 'change' }],
  rejectReason: [
    {
      required: true,
      message: '请输入驳回原因',
      trigger: 'blur',
      validator: (rule: any, value: string, callback: any) => {
        if (form.value.approvalResult === 'reject' && !value.trim()) {
          callback(new Error('请输入驳回原因'))
        } else {
          callback()
        }
      }
    }
  ],
  comments: [{ required: false, message: '请输入审批意见', trigger: 'blur' }]
}

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount.toLocaleString('zh-CN')
}

/** 获取订单状态类型 */
const getOrderStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'warning',
    approved: 'success',
    rejected: 'danger',
    pending_payment: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    approved: '已批准',
    rejected: '已拒绝',
    pending_payment: '待支付',
    in_progress: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 审批结果变化处理
const onApprovalResultChange = (value: string) => {
  if (value === 'approve') {
    // 选择通过时，清空驳回原因
    form.value.rejectReason = ''
  }
  console.log('审批结果变化:', value)
}

/** 提交表单 */
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    if (!props.orderData?.id || !props.orderData?.orderNo) {
      ElMessage.error('订单信息不完整')
      return
    }

    loading.value = true

    let result
    if (form.value.approvalResult === 'approve') {
      // 调用审批通过接口
      result = await UniversityPracticeOrderApi.approveOrder({
        orderId: props.orderData.id,
        orderNo: props.orderData.orderNo,
        approvalType: 'order_approval', // 固定为订单审批
        comments: form.value.comments
      })
    } else {
      // 调用审批驳回接口
      result = await UniversityPracticeOrderApi.rejectOrder({
        orderId: props.orderData.id,
        orderNo: props.orderData.orderNo,
        approvalType: 'order_approval', // 固定为订单审批
        rejectReason: form.value.rejectReason,
        comments: form.value.comments
      })
    }

    if (result.success) {
      ElMessage.success(form.value.approvalResult === 'approve' ? '审批通过成功' : '审批驳回成功')
      emit('success', {
        approvalResult: form.value.approvalResult,
        rejectReason: form.value.rejectReason,
        comments: form.value.comments,
        message: result.message
      })
      handleCancel()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('审批操作失败:', error)
    ElMessage.error('审批操作失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 取消 */
const handleCancel = () => {
  emit('update:visible', false)
  // 重置表单
  form.value = {
    approvalResult: 'approve',
    rejectReason: '',
    comments: ''
  }
}
</script>

<style scoped lang="scss">
.approval-container {
  .order-info {
    background: #f8f9fa;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 16px;

    .info-item {
      display: flex;
      margin-bottom: 12px;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-weight: 500;
        color: #606266;
        min-width: 80px;
        flex-shrink: 0;
      }

      .info-value {
        color: #303133;
        flex: 1;

        &.amount {
          color: #67c23a;
          font-weight: bold;
          font-size: 16px;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
