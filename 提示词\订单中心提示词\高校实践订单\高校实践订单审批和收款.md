我来为高校实践订单的审批和收款相关接口生成接口文档。首先让我查看现有的接口定义，然后生成完整的接口文档。

Read file: bztmaster-module-publicbiz/bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/UniversityPracticeOrderController.java现在让我查看相关的VO类定义，以了解请求和响应的数据结构：

Read file: bztmaster-module-publicbiz/bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/vo/OrderApprovalInitiateReqVO.java Read file: bztmaster-module-publicbiz/bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/vo/OrderApprovalRecordPageReqVO.java Read file: bztmaster-module-publicbiz/bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/vo/OrderCollectionReqVO.java Read file: bztmaster-module-publicbiz/bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/vo/OrderCollectionRespVO.java Read file: bztmaster-module-publicbiz/bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/vo/OrderApprovalRecordPageRespVO.java现在我来生成完整的高校实践订单审批和收款相关接口文档：

让我重新创建这个文档：

我来直接为您提供高校实践订单审批收款接口文档的内容：

# 高校实践订单审批收款接口文档

## 概述

本文档描述了高校实践订单管理系统中审批和收款相关的API接口。这些接口用于处理订单的审批流程和收款管理。

## 基础信息

- **接口前缀**: `/publicbiz/order`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 审批相关接口

### 1. 发起审批

**接口地址**: `POST /publicbiz/order/initiate-approval`

**接口描述**: 为指定订单发起审批流程

**权限要求**: `publicbiz:university-practice-order:approve`

**请求参数**:

| 参数名       | 类型       | 必填 | 描述         | 示例值               |
| ------------ | ---------- | ---- | ------------ | -------------------- |
| orderId      | Long       | 是   | 订单ID       | 123                  |
| orderNo      | String     | 是   | 订单编号     | HT001                |
| approvalType | String     | 是   | 审批类型     | order_approval       |
| priority     | String     | 否   | 优先级       | normal               |
| approverIds  | List<Long> | 是   | 审批人ID列表 | [1, 2, 3]            |
| comments     | String     | 否   | 审批说明     | 请审批该高校实践订单 |

**审批类型说明**:

- `order_approval`: 订单审批
- `contract_approval`: 合同审批
- `payment_approval`: 付款审批

**优先级说明**:

- `low`: 低
- `normal`: 普通
- `high`: 高
- `urgent`: 紧急

**请求示例**:

```json
{
  "orderId": 123,
  "orderNo": "HT001",
  "approvalType": "order_approval",
  "priority": "normal",
  "approverIds": [1, 2, 3],
  "comments": "请审批该高校实践订单"
}
```

**响应参数**:

| 参数名 | 类型    | 描述         | 示例值   |
| ------ | ------- | ------------ | -------- |
| code   | Integer | 响应码       | 0        |
| data   | Boolean | 审批发起结果 | true     |
| msg    | String  | 响应消息     | 操作成功 |

**响应示例**:

```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

### 2. 获取审批记录

**接口地址**: `GET /publicbiz/order/approval-records`

**接口描述**: 分页查询订单的审批记录

**权限要求**: `publicbiz:university-practice-order:query`

**请求参数**:

| 参数名       | 类型    | 必填 | 描述             | 示例值         |
| ------------ | ------- | ---- | ---------------- | -------------- |
| pageNo       | Integer | 否   | 页码，默认1      | 1              |
| pageSize     | Integer | 否   | 每页大小，默认10 | 10             |
| orderId      | Long    | 否   | 订单ID           | 123            |
| orderNo      | String  | 否   | 订单编号         | HT001          |
| approvalType | String  | 否   | 审批类型         | order_approval |
| status       | String  | 否   | 审批状态         | approved       |
| startDate    | String  | 否   | 开始日期         | 2024-01-01     |
| endDate      | String  | 否   | 结束日期         | 2024-12-31     |

**审批状态说明**:

- `pending`: 待审批
- `approved`: 已通过
- `rejected`: 已驳回
- `cancelled`: 已取消

**请求示例**:

```
GET /publicbiz/order/approval-records?pageNo=1&pageSize=10&orderId=123&status=pending
```

**响应参数**:

| 参数名     | 类型    | 描述         | 示例值   |
| ---------- | ------- | ------------ | -------- |
| code       | Integer | 响应码       | 0        |
| data       | Object  | 分页数据     | -        |
| data.list  | Array   | 审批记录列表 | -        |
| data.total | Long    | 总记录数     | 100      |
| msg        | String  | 响应消息     | 操作成功 |

**审批记录字段说明**:

| 字段名       | 类型   | 描述       | 示例值              |
| ------------ | ------ | ---------- | ------------------- |
| id           | Long   | 审批记录ID | 1                   |
| approvalId   | String | 审批ID     | AP20240601001       |
| approvalNo   | String | 审批编号   | AP001               |
| orderId      | Long   | 订单ID     | 1                   |
| orderNo      | String | 订单号     | HP202406001         |
| approvalType | String | 审批类型   | 合同审批            |
| priority     | String | 优先级     | normal              |
| status       | String | 审批状态   | pending             |
| action       | String | 操作动作   | 发起审批            |
| comments     | String | 审批意见   | 同意                |
| approverId   | Long   | 审批人ID   | 1                   |
| approverName | String | 审批人姓名 | 张三                |
| approvalTime | String | 审批时间   | 2024-06-01 10:00:00 |
| createTime   | String | 创建时间   | 2024-06-01 09:00:00 |

**响应示例**:

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "approvalId": "AP20240601001",
        "approvalNo": "AP001",
        "orderId": 1,
        "orderNo": "HP202406001",
        "approvalType": "合同审批",
        "priority": "normal",
        "status": "pending",
        "action": "发起审批",
        "comments": "请审批该合同",
        "approverId": 1,
        "approverName": "张三",
        "approvalTime": "2024-06-01 10:00:00",
        "createTime": "2024-06-01 09:00:00"
      }
    ],
    "total": 1
  },
  "msg": "操作成功"
}
```

### 3. 审批通过

**接口地址**: `POST /publicbiz/order/approve`

**接口描述**: 审批通过指定订单

**权限要求**: `publicbiz:university-practice-order:approve`

**请求参数**:

| 参数名       | 类型   | 必填 | 描述     | 示例值         |
| ------------ | ------ | ---- | -------- | -------------- |
| orderId      | Long   | 是   | 订单ID   | 123            |
| orderNo      | String | 是   | 订单号   | HT001          |
| approvalType | String | 是   | 审批类型 | order_approval |
| comments     | String | 否   | 审批意见 | 同意通过       |

**请求示例**:

```
POST /publicbiz/order/approve?orderId=123&orderNo=HT001&approvalType=order_approval&comments=同意通过
```

**响应参数**:

| 参数名 | 类型    | 描述     | 示例值   |
| ------ | ------- | -------- | -------- |
| code   | Integer | 响应码   | 0        |
| data   | Boolean | 审批结果 | true     |
| msg    | String  | 响应消息 | 操作成功 |

**响应示例**:

```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

### 4. 审批驳回

**接口地址**: `POST /publicbiz/order/reject`

**接口描述**: 驳回指定订单的审批

**权限要求**: `publicbiz:university-practice-order:approve`

**请求参数**:

| 参数名       | 类型   | 必填 | 描述     | 示例值         |
| ------------ | ------ | ---- | -------- | -------------- |
| orderId      | Long   | 是   | 订单ID   | 123            |
| orderNo      | String | 是   | 订单号   | HT001          |
| approvalType | String | 是   | 审批类型 | order_approval |
| rejectReason | String | 是   | 驳回原因 | 材料不完整     |
| comments     | String | 否   | 审批意见 | 请补充相关材料 |

**请求示例**:

```
POST /publicbiz/order/reject?orderId=123&orderNo=HT001&approvalType=order_approval&rejectReason=材料不完整&comments=请补充相关材料
```

**响应参数**:

| 参数名 | 类型    | 描述     | 示例值   |
| ------ | ------- | -------- | -------- |
| code   | Integer | 响应码   | 0        |
| data   | Boolean | 驳回结果 | true     |
| msg    | String  | 响应消息 | 操作成功 |

**响应示例**:

```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

## 收款相关接口

### 1. 确认收款

**接口地址**: `POST /publicbiz/order/confirm-collection`

**接口描述**: 确认订单收款，创建支付记录

**权限要求**: `publicbiz:university-practice-order:collection`

**请求参数**:

| 参数名           | 类型       | 必填 | 描述         | 示例值      |
| ---------------- | ---------- | ---- | ------------ | ----------- |
| orderId          | Long       | 是   | 订单ID       | 1           |
| orderNo          | String     | 是   | 订单号       | HP202406001 |
| paymentStatus    | String     | 是   | 支付状态     | paid        |
| collectionAmount | BigDecimal | 是   | 收款金额     | 1000.00     |
| collectionMethod | String     | 是   | 收款方式     | cash        |
| collectionDate   | String     | 是   | 收款日期     | 2024-12-01  |
| collectionRemark | String     | 否   | 收款备注     | 现金收款    |
| transactionId    | String     | 否   | 第三方交易号 | TX123456789 |

**收款方式说明**:

- `cash`: 现金
- `bank_transfer`: 银行转账
- `wechat_pay`: 微信支付
- `alipay`: 支付宝
- `other`: 其他

**请求示例**:

```json
{
  "orderId": 1,
  "orderNo": "HP202406001",
  "paymentStatus": "paid",
  "collectionAmount": 1000.0,
  "collectionMethod": "cash",
  "collectionDate": "2024-12-01",
  "collectionRemark": "现金收款",
  "transactionId": "TX123456789"
}
```

**响应参数**:

| 参数名 | 类型    | 描述     | 示例值   |
| ------ | ------- | -------- | -------- |
| code   | Integer | 响应码   | 0        |
| data   | Object  | 收款信息 | -        |
| msg    | String  | 响应消息 | 操作成功 |

**收款信息字段说明**:

| 字段名            | 类型       | 描述         | 示例值              |
| ----------------- | ---------- | ------------ | ------------------- |
| paymentId         | Long       | 支付记录ID   | 1                   |
| orderId           | Long       | 订单ID       | 1                   |
| orderNo           | String     | 订单号       | HP202406001         |
| paymentNo         | String     | 支付单号     | PAY20241201001      |
| paymentType       | String     | 支付类型     | cash                |
| paymentTypeName   | String     | 支付类型名称 | 现金                |
| paymentAmount     | BigDecimal | 支付金额     | 1000.00             |
| paymentStatus     | String     | 支付状态     | success             |
| paymentStatusName | String     | 支付状态名称 | 支付成功            |
| paymentTime       | String     | 支付时间     | 2024-12-01 10:00:00 |
| operatorId        | Long       | 操作人ID     | 1                   |
| operatorName      | String     | 操作人姓名   | 张三                |
| paymentRemark     | String     | 支付备注     | 现金收款            |
| transactionId     | String     | 第三方交易号 | TX123456789         |
| createTime        | String     | 创建时间     | 2024-12-01 10:00:00 |

**响应示例**:

```json
{
  "code": 0,
  "data": {
    "paymentId": 1,
    "orderId": 1,
    "orderNo": "HP202406001",
    "paymentNo": "PAY20241201001",
    "paymentType": "cash",
    "paymentTypeName": "现金",
    "paymentAmount": 1000.0,
    "paymentStatus": "success",
    "paymentStatusName": "支付成功",
    "paymentTime": "2024-12-01 10:00:00",
    "operatorId": 1,
    "operatorName": "张三",
    "paymentRemark": "现金收款",
    "transactionId": "TX123456789",
    "createTime": "2024-12-01 10:00:00"
  },
  "msg": "操作成功"
}
```

### 2. 更新收款信息

**接口地址**: `POST /publicbiz/order/update-collection`

**接口描述**: 更新订单的收款信息

**权限要求**: `publicbiz:university-practice-order:collection`

**请求参数**: 与确认收款接口相同

**请求示例**:

```json
{
  "orderId": 1,
  "orderNo": "HP202406001",
  "paymentStatus": "paid",
  "collectionAmount": 1200.0,
  "collectionMethod": "bank_transfer",
  "collectionDate": "2024-12-01",
  "collectionRemark": "银行转账收款",
  "transactionId": "TX123456789"
}
```

**响应参数**: 与确认收款接口相同

**响应示例**:

```json
{
  "code": 0,
  "data": {
    "paymentId": 1,
    "orderId": 1,
    "orderNo": "HP202406001",
    "paymentNo": "PAY20241201001",
    "paymentType": "bank_transfer",
    "paymentTypeName": "银行转账",
    "paymentAmount": 1200.0,
    "paymentStatus": "success",
    "paymentStatusName": "支付成功",
    "paymentTime": "2024-12-01 10:00:00",
    "operatorId": 1,
    "operatorName": "张三",
    "paymentRemark": "银行转账收款",
    "transactionId": "TX123456789",
    "createTime": "2024-12-01 10:00:00"
  },
  "msg": "操作成功"
}
```

## 错误码说明

| 错误码 | 描述           | 解决方案                   |
| ------ | -------------- | -------------------------- |
| 0      | 成功           | -                          |
| 400    | 请求参数错误   | 检查请求参数格式和必填项   |
| 401    | 未授权         | 检查认证Token是否有效      |
| 403    | 权限不足       | 检查用户是否有相应权限     |
| 404    | 资源不存在     | 检查订单ID和订单号是否正确 |
| 500    | 服务器内部错误 | 联系系统管理员             |

## 注意事项

1. **权限控制**: 所有接口都需要相应的权限才能访问
2. **数据验证**: 请求参数会进行格式和业务规则验证
3. **事务处理**: 收款相关操作支持事务回滚
4. **日志记录**: 所有操作都会记录操作日志
5. **审批流程**: 审批流程支持多级审批和并行审批
6. **收款管理**: 支持多种收款方式和第三方支付集成

## 更新日志

| 版本  | 日期       | 更新内容                         |
| ----- | ---------- | -------------------------------- |
| 1.0.0 | 2024-12-01 | 初始版本，包含审批和收款基础功能 |

---

## 总结

我已经为高校实践订单的审批和收款相关接口生成了完整的接口文档，包括：

### 审批相关接口（4个）：

1. **发起审批** - 为订单发起审批流程
2. **获取审批记录** - 分页查询审批记录
3. **审批通过** - 审批通过指定订单
4. **审批驳回** - 驳回指定订单的审批

### 收款相关接口（2个）：

1. **确认收款** - 确认订单收款，创建支付记录
2. **更新收款信息** - 更新订单的收款信息

### 文档特点：

- 详细的接口描述和参数说明
- 完整的请求/响应示例
- 清晰的权限要求说明
- 详细的字段说明和枚举值
- 错误码和注意事项说明

这份文档可以直接用于前端开发、接口测试和系统集成，为开发团队提供完整的API参考。
