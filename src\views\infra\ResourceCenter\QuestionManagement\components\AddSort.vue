<!--
  页面名称：添加分类
  功能描述：新增/编辑考题分类，支持表单校验、提交、重置、编辑回显
-->
<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑分类' : '添加分类'"
    width="500px"
    :close-on-click-modal="false"
    @close="onCancel"
    destroy-on-close
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="80px"
      v-loading="loading"
      element-loading-text="处理中..."
    >
      <el-form-item label="所属上级" prop="parentLevel">
        <el-select
          v-model="form.parentLevel"
          :placeholder="isEdit ? '编辑时不可修改上级分类' : '搜索或选择上级分类...'"
          style="width: 100%"
          clearable
          filterable
          :loading="parentLevelLoading"
          :disabled="isEdit"
        >
          <el-option
            v-for="item in parentLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="分类名称" prop="categoryName" required>
        <el-input
          v-model="form.categoryName"
          placeholder="请输入分类名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="分类代码" prop="categoryCode" required>
        <el-input
          v-model="form.categoryCode"
          placeholder="请输入分类代码"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>

      <!-- 动态显示的认定点字段 -->
      <transition name="fade-slide">
        <div v-if="showCertFields" class="cert-fields">
          <el-form-item label="认定点名称" prop="certName">
            <el-input
              v-model="form.certName"
              placeholder="请输入认定点名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="认定点代码" prop="certCode">
            <el-input
              v-model="form.certCode"
              placeholder="请输入认定点代码"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
        </div>
      </transition>

      <el-form-item label="业务模块" prop="biz" required>
        <el-select
          v-model="form.biz"
          :placeholder="props.defaultBiz || '请选择业务模块'"
          style="width: 100%"
          disabled
        >
          <el-option
            v-for="item in bizOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 添加说明 -->
    <div class="add-tips">
      <el-icon class="tips-icon"><InfoFilled /></el-icon>
      <span class="tips-title">添加说明</span>
      <ul class="tips-list">
        <li>不选择上级：添加一级分类</li>
        <li>选择一级分类：添加二级分类</li>
        <li>选择二级分类：添加三级分类（显示认定点名称）</li>
        <li>分类名称和代码为必填项</li>
      </ul>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="loading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, withDefaults } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import {
  QuestionManagementApi,
  type AddCategoryParams,
  type UpdateCategoryParams,
  type QuestionCategory
} from '@/api/infra/questionManagement'

/** 组件属性定义 */
interface Props {
  editData?: QuestionCategory | null
  defaultBiz?: string
}

/** 组件事件定义 */
const emit = defineEmits(['success'])

/** 组件属性 */
const props = withDefaults(defineProps<Props>(), {
  editData: null,
  defaultBiz: ''
})

/** 弹窗显示状态 */
const visible = defineModel<boolean>('visible', { default: false })

/** 表单引用 */
const formRef = ref()

/** 加载状态 */
const loading = ref(false)

/** 是否编辑模式 */
const isEdit = computed(() => !!props.editData?.id)

/** 是否显示认定点字段（当选择二级分类时显示） */
const showCertFields = computed(() => {
  return form.parentLevel && form.parentLevel.includes('level2')
})

/** 表单数据 */
const form = reactive({
  parentLevel: '',
  categoryName: '',
  categoryCode: '',
  certName: '',
  certCode: '',
  biz: ''
})

/** 上级分类选项 */
const parentLevelOptions = ref<
  Array<{ label: string; value: string; id: number; categoryData: any }>
>([])
const parentLevelLoading = ref(false)

/** 表单校验规则 */
const rules = reactive({
  parentLevel: [{ required: false, message: '请选择上级分类', trigger: 'change' }],
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  categoryCode: [
    { required: true, message: '请输入分类代码', trigger: 'blur' },
    { min: 2, max: 20, message: '分类代码长度在 2 到 20 个字符', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9]+$/, message: '分类代码只能包含字母和数字', trigger: 'blur' }
  ],
  certName: [
    { required: false, message: '请输入认定点名称', trigger: 'blur' },
    { max: 50, message: '认定点名称长度不能超过 50 个字符', trigger: 'blur' }
  ],
  certCode: [
    { required: false, message: '请输入认定点代码', trigger: 'blur' },
    { max: 20, message: '认定点代码长度不能超过 20 个字符', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9]*$/, message: '认定点代码只能包含字母和数字', trigger: 'blur' }
  ],
  biz: [{ required: true, message: '请选择业务模块', trigger: 'change' }]
})

/** 业务模块选项 */
const bizOptions = ref<Array<{ label: string; value: string }>>([])

/** 获取业务模块选项 */
const fetchBizOptions = async () => {
  try {
    const res = await getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 100 })
    bizOptions.value = [
      ...(res?.list || []).map((item) => ({
        label: item.label,
        value: item.value
      }))
    ]
  } catch (error) {
    console.error('获取业务模块字典数据失败:', error)
  }
}

/** 获取parentId值 */
const getParentId = (): number | null => {
  if (!form.parentLevel) {
    return null
  }
  // 从parentLevelOptions中找到匹配的选项，获取对应的分类ID
  const selectedOption = parentLevelOptions.value.find(
    (option) => option.value === form.parentLevel
  )

  if (selectedOption) {
    return selectedOption.id
  }

  return null
}

/** 获取上级分类选项 */
const fetchParentLevelOptions = async () => {
  try {
    parentLevelLoading.value = true

    // 直接使用当前的业务模块值获取分类数据
    const currentBiz = props.defaultBiz || form.biz
    console.log('当前业务模块:', currentBiz)

    if (!currentBiz) {
      // 业务模块为空时，清空上级分类选项，不显示错误
      parentLevelOptions.value = []
      return
    }

    const params = { biz: currentBiz }
    console.log('请求参数:', params)
    const result = await QuestionManagementApi.getCategoryList(params)

    // 调试：打印返回的数据结构
    console.log('获取分类列表结果:', result)
    console.log('result类型:', typeof result)
    console.log('result.list类型:', typeof (result as any)?.list)

    // 只显示一级分类和二级分类作为上级选项
    const options: Array<{ label: string; value: string; id: number; categoryData: any }> = []

    // 检查返回数据的结构并安全处理
    if (!result) {
      console.error('API返回结果为空')
      parentLevelOptions.value = []
      return
    }

    // 处理不同的返回数据格式
    let categoryList: any[] = []
    if (Array.isArray(result)) {
      // 如果直接返回数组
      categoryList = result
    } else if ((result as any).list && Array.isArray((result as any).list)) {
      // 如果返回对象格式 {list: [...]}
      categoryList = (result as any).list
    } else {
      console.error('API返回数据格式不正确:', result)
      parentLevelOptions.value = []
      return
    }

    categoryList.forEach((category) => {
      if (category.level === 1) {
        // 一级分类
        options.push({
          label: `${category.level1Name}(${category.level1Code})(一级)`,
          value: `${category.level1Code}_level1`,
          id: category.id,
          categoryData: category
        })
      } else if (category.level === 2) {
        // 二级分类
        options.push({
          label: `${category.level1Name}>${category.level2Name}(${category.level2Code})(二级)`,
          value: `${category.level2Code}_level2`,
          id: category.id,
          categoryData: category
        })
      }
      // 三级分类不作为上级选项
    })

    parentLevelOptions.value = options
  } catch (error) {
    console.error('获取上级分类数据失败:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error('错误详情:', {
      message: errorMessage,
      currentBiz: props.defaultBiz || form.biz,
      error: error
    })
    ElMessage.error(`获取上级分类数据失败: ${errorMessage}`)
    parentLevelOptions.value = []
  } finally {
    parentLevelLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  form.parentLevel = ''
  form.categoryName = ''
  form.categoryCode = ''
  form.certName = ''
  form.certCode = ''
  form.biz = props.defaultBiz || '' // 使用传入的默认业务模块
  formRef.value?.clearValidate()
}

/** 监听defaultBiz变化，更新表单业务模块 */
watch(
  () => props.defaultBiz,
  (newBiz) => {
    if (newBiz && !props.editData) {
      // 只在非编辑模式下更新业务模块
      form.biz = newBiz
      // 上级分类选项现在在弹窗打开时统一获取
    }
  },
  { immediate: true }
)

/** 编辑时回显数据 */
const fillFormData = () => {
  if (props.editData) {
    // 先设置业务模块
    form.biz = props.editData.biz || props.defaultBiz || ''

    // 根据数据层级确定上级分类和当前分类
    if (props.editData.level === 3) {
      // 三级分类，找到匹配的二级分类作为上级
      const matchingParent = parentLevelOptions.value.find(
        (opt) => opt.value === `${props.editData!.level2Code}_level2`
      )
      form.parentLevel = matchingParent?.value || ''
      form.categoryName = props.editData.level3Name || ''
      form.categoryCode = props.editData.level3Code || ''
    } else if (props.editData.level === 2) {
      // 二级分类，找到匹配的一级分类作为上级
      const matchingParent = parentLevelOptions.value.find(
        (opt) => opt.value === `${props.editData!.level1Code}_level1`
      )
      form.parentLevel = matchingParent?.value || ''
      form.categoryName = props.editData.level2Name || ''
      form.categoryCode = props.editData.level2Code || ''
    } else {
      // 一级分类，无上级
      form.parentLevel = ''
      form.categoryName = props.editData.level1Name || ''
      form.categoryCode = props.editData.level1Code || ''
    }

    form.certName = props.editData.certName || ''
    form.certCode = props.editData.certCode || ''
  }
}

/** 提交表单 */
const onSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    // 构建提交数据，根据上级分类选择确定当前添加的层级
    let submitData: AddCategoryParams

    // 获取parentId
    const parentId = getParentId()

    if (!form.parentLevel) {
      // 没有选择上级分类，添加一级分类
      submitData = {
        level1Name: form.categoryName,
        level1Code: form.categoryCode,
        level2Name: '',
        level2Code: '',
        level3Name: '',
        level3Code: '',
        certName: form.certName,
        certCode: form.certCode,
        biz: form.biz,
        bizName: form.biz, // 业务模块名称与代码相同
        parentId: null, // 一级分类没有上级
        level: 1 // 一级分类
      }
    } else if (form.parentLevel.includes('level1')) {
      // 选择了一级分类，添加二级分类
      const parentOption = parentLevelOptions.value.find((opt) => opt.value === form.parentLevel)
      const parentCode = form.parentLevel.split('_')[0]

      // 从选中的一级分类数据中获取准确的分类信息
      const parentCategoryData = parentOption?.categoryData
      const level1Name =
        parentCategoryData?.level1Name || parentOption?.label.split('(')[0] || '上级分类'
      const level1Code = parentCategoryData?.level1Code || parentCode

      submitData = {
        level1Name: level1Name,
        level1Code: level1Code,
        level2Name: form.categoryName,
        level2Code: form.categoryCode,
        level3Name: '',
        level3Code: '',
        certName: form.certName,
        certCode: form.certCode,
        biz: form.biz,
        bizName: form.biz,
        parentId: parentId, // 二级分类的上级是一级分类
        level: 2 // 二级分类
      }
    } else if (form.parentLevel.includes('level2')) {
      // 选择了二级分类，添加三级分类
      const parentOption = parentLevelOptions.value.find((opt) => opt.value === form.parentLevel)
      const parentCode = form.parentLevel.split('_')[0]

      // 从选中的二级分类数据中获取完整的一级分类信息
      const parentCategoryData = parentOption?.categoryData
      const level1Name = parentCategoryData?.level1Name || '上级一级分类'
      const level1Code = parentCategoryData?.level1Code || ''
      const level2Name = parentCategoryData?.level2Name || '上级二级分类'

      submitData = {
        level1Name: level1Name,
        level1Code: level1Code, // 从实际数据中获取一级分类代码
        level2Name: level2Name,
        level2Code: parentCode,
        level3Name: form.categoryName,
        level3Code: form.categoryCode,
        certName: form.certName,
        certCode: form.certCode,
        biz: form.biz,
        bizName: form.biz,
        parentId: parentId, // 三级分类的上级是二级分类
        level: 3 // 三级分类
      }
    } else {
      // 默认情况，添加一级分类
      submitData = {
        level1Name: form.categoryName,
        level1Code: form.categoryCode,
        level2Name: '',
        level2Code: '',
        level3Name: '',
        level3Code: '',
        certName: form.certName,
        certCode: form.certCode,
        biz: form.biz,
        bizName: form.biz,
        parentId: null, // 一级分类没有上级
        level: 1
      }
    }

    // 调试：打印提交数据
    console.log('提交数据:', submitData)
    console.log('一级分类代码:', submitData.level1Code)
    console.log('二级分类代码:', submitData.level2Code)
    console.log('三级分类代码:', submitData.level3Code)

    // 调用真实的API接口
    if (isEdit.value) {
      // 编辑分类 - 保持原有的parentId和level不变
      const updateData: UpdateCategoryParams = {
        id: props.editData!.id!,
        level1Name: submitData.level1Name,
        level1Code: submitData.level1Code,
        level2Name: submitData.level2Name,
        level2Code: submitData.level2Code,
        level3Name: submitData.level3Name,
        level3Code: submitData.level3Code,
        certName: submitData.certName,
        certCode: submitData.certCode,
        biz: submitData.biz,
        bizName: submitData.bizName,
        parentId: props.editData!.parentId || null, // 保持原有的parentId
        level: props.editData!.level // 保持原有的level
      }
      await QuestionManagementApi.updateCategory(updateData)
      ElMessage.success('分类更新成功')
    } else {
      // 新增分类
      await QuestionManagementApi.addCategory(submitData)
      ElMessage.success('分类创建成功')
    }

    visible.value = false
    // 通知父组件刷新数据
    emit('success')
  } catch (error) {
    console.error('提交分类失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 取消操作 */
const onCancel = () => {
  visible.value = false
}

/** 监听弹窗显示状态 */
watch(visible, async (newVal) => {
  if (newVal) {
    resetForm()
    // 每次打开弹窗时都获取最新的上级分类数据
    await fetchParentLevelOptions()
    // 编辑模式下需要回显数据
    if (props.editData) {
      fillFormData()
    }
  }
})

/** 监听上级分类变化，当不是二级分类时清空认定点字段 */
watch(
  () => form.parentLevel,
  (newVal) => {
    if (!newVal || !newVal.includes('level2')) {
      form.certName = ''
      form.certCode = ''
    }
  }
)

/** 组件挂载时初始化 */
onMounted(() => {
  fetchBizOptions()
  // 上级分类选项现在在弹窗打开时获取，不需要在组件挂载时预加载
})
</script>

<style scoped lang="scss">
.el-form-item {
  margin-bottom: 18px;
}

.form-item-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;

  .el-icon {
    font-size: 14px;
    color: #409eff;
  }
}

.add-tips {
  margin-top: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;

  .tips-icon {
    color: #409eff;
    margin-right: 6px;
  }

  .tips-title {
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
    display: inline-block;
  }

  .tips-list {
    margin: 8px 0 0 20px;
    padding: 0;

    li {
      margin-bottom: 4px;
      font-size: 13px;
      color: #606266;
      list-style-type: disc;
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}

/* 认定点字段容器 */
.cert-fields {
  margin: 0;
  padding: 0;
}

/* 动态字段过渡效果 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
  max-height: 200px;
  overflow: hidden;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
  max-height: 200px;
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 600px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto;
  }

  .add-tips {
    font-size: 12px;

    .tips-list li {
      font-size: 12px;
    }
  }
}
</style>
