<!--
  页面名称：订单资金列表
  功能描述：展示订单资金列表，支持搜索筛选、分页、查看详情等操作
-->
<template>
  <div class="order-funds-list">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="订单编号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入订单编号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="机构名称">
          <el-input
            v-model="searchForm.agencyName"
            placeholder="请输入机构名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="服务阿姨">
          <el-input
            v-model="searchForm.practitionerName"
            placeholder="请输入服务阿姨姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="对账单号">
          <el-input
            v-model="searchForm.statementNo"
            placeholder="请输入对账单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.orderStatus"
            placeholder="所有订单状态"
            clearable
            style="width: 150px"
          >
            <el-option label="所有订单状态" value="" />
            <el-option label="已完成" value="completed" />
            <el-option label="服务中" value="in_service" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="已退款" value="refunded" />
          </el-select>
        </el-form-item>
        <el-form-item label="资金状态">
          <el-select
            v-model="searchForm.fundStatus"
            placeholder="所有资金状态"
            clearable
            style="width: 150px"
          >
            <el-option label="所有资金状态" value="" />
            <el-option label="已结算" value="settled" />
            <el-option label="托管中" value="escrow" />
            <el-option label="待结算" value="pending" />
            <el-option label="已退款" value="refunded" />
          </el-select>
        </el-form-item>
        <el-form-item label="开票状态">
          <el-select
            v-model="searchForm.invoiceStatus"
            placeholder="所有开票状态"
            clearable
            style="width: 150px"
          >
            <el-option label="所有开票状态" value="" />
            <el-option label="已开票" value="invoiced" />
            <el-option label="未开票" value="not_invoiced" />
            <el-option label="无需开票" value="no_invoice_needed" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="年-月-日"
            end-placeholder="年-月-日"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            筛选
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table v-loading="loading" :data="tableData" style="width: 100%" border stripe>
        <el-table-column prop="orderNo" label="订单编号" width="180" />
        <el-table-column prop="packageName" label="套餐名称" min-width="200" />
        <el-table-column prop="agencyName" label="家政机构" width="150" />
        <el-table-column prop="practitionerName" label="服务阿姨" width="120" />
        <el-table-column prop="orderAmount" label="订单金额" width="120">
          <template #default="{ row }">
            <span class="amount-text">¥{{ formatAmount(row.orderAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="platformCommission" label="平台分成" width="120">
          <template #default="{ row }">
            <span class="amount-text platform-amount"
              >¥{{ formatAmount(row.platformCommission) }}</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="agencyCommission" label="机构分成" width="120">
          <template #default="{ row }">
            <span class="amount-text agency-amount">¥{{ formatAmount(row.agencyCommission) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus" label="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getOrderStatusType(row.orderStatus)" size="small">
              {{ getOrderStatusText(row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fundStatus" label="资金状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getFundStatusType(row.fundStatus)" size="small">
              {{ getFundStatusText(row.fundStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="statementNo" label="对账单号" width="150">
          <template #default="{ row }">
            {{ row.statementNo || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="invoiceStatus" label="是否开票" width="100">
          <template #default="{ row }">
            <el-tag :type="getInvoiceStatusType(row.invoiceStatus)" size="small">
              {{ getInvoiceStatusText(row.invoiceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceNo" label="发票号" width="120">
          <template #default="{ row }">
            {{ row.invoiceNo || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column prop="paymentTime" label="支付时间" width="160" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" text @click="handleViewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 订单详情弹窗 -->
    <OrderDetailDialog v-model:visible="detailDialogVisible" :order-data="currentOrder" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import OrderDetailDialog from './OrderDetailDialog.vue'
import { getOrderFundsList, exportOrderFundsList } from '@/api/settlement/order-funds'

/** 搜索表单数据 */
const searchForm = reactive({
  orderNo: '',
  agencyName: '',
  practitionerName: '',
  statementNo: '',
  orderStatus: '',
  fundStatus: '',
  invoiceStatus: '',
  dateRange: []
})

/** 表格数据 */
const tableData = ref([])

/** 加载状态 */
const loading = ref(false)

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 详情弹窗显示状态 */
const detailDialogVisible = ref(false)

/** 当前选中的订单数据 */
const currentOrder = ref(null)

/** 获取订单资金列表 */
const fetchList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      size: pagination.size,
      startDate: searchForm.dateRange?.[0] || '',
      endDate: searchForm.dateRange?.[1] || ''
    }

    const response = await getOrderFundsList(params)
    tableData.value = response.data?.list || []
    pagination.total = response.data?.total || 0
  } catch (error) {
    ElMessage.error('获取订单资金列表失败')
    console.error('获取订单资金列表失败:', error)
  } finally {
    loading.value = false
  }
}

/** 处理搜索 */
const handleSearch = () => {
  pagination.page = 1
  fetchList()
}

/** 处理重置 */
const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    agencyName: '',
    practitionerName: '',
    statementNo: '',
    orderStatus: '',
    fundStatus: '',
    invoiceStatus: '',
    dateRange: []
  })
  pagination.page = 1
  fetchList()
}

/** 处理导出 */
const handleExport = async () => {
  try {
    const params = {
      ...searchForm,
      startDate: searchForm.dateRange?.[0] || '',
      endDate: searchForm.dateRange?.[1] || ''
    }

    await exportOrderFundsList(params)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出失败:', error)
  }
}

/** 处理查看详情 */
const handleViewDetail = (row: any) => {
  currentOrder.value = row
  detailDialogVisible.value = true
}

/** 处理分页大小变化 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

/** 处理当前页变化 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

/** 获取订单状态类型 */
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    completed: 'success',
    in_service: 'warning',
    cancelled: 'danger',
    refunded: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    completed: '已完成',
    in_service: '服务中',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return statusMap[status] || status
}

/** 获取资金状态类型 */
const getFundStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    settled: 'success',
    escrow: 'warning',
    pending: 'info',
    refunded: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取资金状态文本 */
const getFundStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    settled: '已结算',
    escrow: '托管中',
    pending: '待结算',
    refunded: '已退款'
  }
  return statusMap[status] || status
}

/** 获取开票状态类型 */
const getInvoiceStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    invoiced: 'success',
    not_invoiced: 'warning',
    no_invoice_needed: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取开票状态文本 */
const getInvoiceStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    invoiced: '已开票',
    not_invoiced: '未开票',
    no_invoice_needed: '无需开票'
  }
  return statusMap[status] || status
}

/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount?.toFixed(2) || '0.00'
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.order-funds-list {
  .search-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .search-form {
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 20px;
      }
    }
  }

  .table-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .amount-text {
      font-weight: 600;

      &.agency-amount {
        color: #67c23a;
      }

      &.platform-amount {
        color: #409eff;
      }
    }

    .pagination-section {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
