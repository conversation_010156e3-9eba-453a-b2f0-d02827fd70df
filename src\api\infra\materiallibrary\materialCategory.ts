import request from '@/config/axios'

// 分类VO
export interface CategoryVO {
  id: number
  name: string
  parentId: number
  sort: number
  status: number
  description: string
  level?: number
  path?: string
  visibleOrgId?: number
  visibleOrgName?: string
  children?: CategoryVO[]
}

// 新增分类
export const createCategory = (data: {
  name: string
  parentId?: number
  sort?: number
  status?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/category/create', data })
}

// 删除分类
export const deleteCategory = (id: number) => {
  return request.post({ url: '/system/material/category/delete', data: { id } })
}

// 分类列表
export const getCategoryList = () => {
  return request.get({ url: '/system/material/category/list' })
}

// 编辑分类
export const updateCategory = (data: {
  id: number
  name: string
  parentId?: number
  sort?: number
  status?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/category/update', data })
}
