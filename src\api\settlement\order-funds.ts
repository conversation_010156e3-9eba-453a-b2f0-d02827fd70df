import request from '@/config/axios'

/**
 * 获取订单资金列表
 */
export function getOrderFundsList(params: any) {
  return request.get({
    url: '/settlement/order-funds/list',
    params
  })
}

/**
 * 获取订单详情
 */
export function getOrderDetail(orderNo: string) {
  return request.get({
    url: `/settlement/order-funds/detail/${orderNo}`
  })
}

/**
 * 导出订单资金列表
 */
export function exportOrderFundsList(params: any) {
  return request.download({
    url: '/settlement/order-funds/export',
    params
  })
}

/**
 * 导出订单详情
 */
export function exportOrderDetail(orderNo: string) {
  return request.download({
    url: `/settlement/order-funds/detail/export/${orderNo}`
  })
}

/**
 * 订单资金查询参数
 */
export interface OrderFundsQuery {
  orderNo?: string
  agencyName?: string
  practitionerName?: string
  statementNo?: string
  orderStatus?: string
  fundStatus?: string
  invoiceStatus?: string
  startDate?: string
  endDate?: string
  page: number
  size: number
}

/**
 * 订单资金信息
 */
export interface OrderFundsInfo {
  id: number
  orderNo: string
  packageName: string
  agencyName: string
  practitionerName: string
  orderAmount: number
  platformCommission: number
  agencyCommission: number
  orderStatus: string
  fundStatus: string
  statementNo?: string
  invoiceStatus: string
  invoiceNo?: string
  createTime: string
  paymentTime: string
  completionTime?: string
  customerName?: string
  customerPhone?: string
  customerAddress?: string
  specialRequirements?: string
  paymentMethod?: string
}

/**
 * 订单详情信息
 */
export interface OrderDetailInfo {
  basicInfo: {
    orderNo: string
    orderStatus: string
    createTime: string
    paymentTime: string
    completionTime?: string
    fundStatus: string
  }
  serviceInfo: {
    packageName: string
    serviceType: string
    serviceDuration: string
    serviceAddress: string
  }
  agencyInfo: {
    agencyName: string
    agencyPhone: string
    practitionerName: string
    practitionerPhone: string
  }
  customerInfo: {
    customerName: string
    customerPhone: string
    customerAddress: string
    specialRequirements: string
  }
  costInfo: {
    orderAmount: number
    agencyCommission: number
    platformCommission: number
    paymentMethod: string
  }
  serviceRecords: Array<{
    id: number
    time: string
    title: string
    description: string
    status: string
  }>
}
