<!--
  页面名称：完成任务弹窗
  功能描述：完成任务并上传完成凭证
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="完成任务"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
      <el-form-item label="最终完成人员" prop="finalPersonnel" required>
        <el-select
          v-model="formData.finalPersonnel"
          placeholder="请选择完成人员"
          style="width: 100%"
        >
          <el-option
            v-for="personnel in personnelList"
            :key="personnel.id"
            :label="personnel.name"
            :value="personnel.name"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="完成时间" prop="completionTime" required>
        <el-date-picker
          v-model="formData.completionTime"
          type="datetime"
          placeholder="选择完成时间"
          style="width: 100%"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="打卡地点" prop="punchLocation" required>
        <el-input v-model="formData.punchLocation" placeholder="请输入打卡地点" />
      </el-form-item>

      <el-form-item label="完成凭证" prop="certificateFile" required>
        <div class="upload-area">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            accept=".jpg,.jpeg,.png,.gif"
            :limit="1"
          >
            <div class="upload-content">
              <el-button type="primary" plain>选择文件</el-button>
              <span class="file-info">{{ fileInfo }}</span>
            </div>
          </el-upload>
          <div class="upload-tip"> 支持格式: JPG、PNG、GIF, 文件大小不超过5MB </div>
        </div>
      </el-form-item>

      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="formData.remarks"
          type="textarea"
          :rows="4"
          placeholder="请输入备注信息 (可选)"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading"> 确认完成 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules, UploadFile } from 'element-plus'

interface Props {
  visible: boolean
  taskData: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [taskData: any]
}>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const formRef = ref<FormInstance>()
const uploadRef = ref()
const loading = ref(false)

const formData = ref({
  finalPersonnel: '',
  completionTime: '',
  punchLocation: '',
  certificateFile: null as File | null,
  remarks: ''
})

const formRules: FormRules = {
  finalPersonnel: [{ required: true, message: '请选择完成人员', trigger: 'change' }],
  completionTime: [{ required: true, message: '请选择完成时间', trigger: 'change' }],
  punchLocation: [{ required: true, message: '请输入打卡地点', trigger: 'blur' }],
  certificateFile: [{ required: true, message: '请上传完成凭证', trigger: 'change' }]
}

// 模拟人员列表
const personnelList = ref([
  { id: '1', name: '张阿姨' },
  { id: '2', name: '李阿姨' },
  { id: '3', name: '王阿姨' },
  { id: '4', name: '赵阿姨' }
])

const fileInfo = computed(() => {
  if (formData.value.certificateFile) {
    return formData.value.certificateFile.name
  }
  return '未选择任何文件'
})

// 监听任务数据变化，初始化表单
watch(
  () => props.taskData,
  (newData) => {
    if (newData) {
      formData.value = {
        finalPersonnel: newData.currentPersonnel || '',
        completionTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        punchLocation: newData.punchLocation || '',
        certificateFile: null,
        remarks: ''
      }
    }
  },
  { immediate: true }
)

const handleFileChange = (file: UploadFile) => {
  // 检查文件大小 (5MB)
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过 5MB!')
    return
  }

  formData.value.certificateFile = file.raw || null
}

const handleClose = () => {
  formData.value = {
    finalPersonnel: '',
    completionTime: '',
    punchLocation: '',
    certificateFile: null,
    remarks: ''
  }
  emit('update:visible', false)
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 更新任务数据
    const updatedTask = {
      ...props.taskData,
      taskStatus: 'completed',
      finalPersonnel: formData.value.finalPersonnel,
      completionTime: formData.value.completionTime,
      punchLocation: formData.value.punchLocation,
      remarks: formData.value.remarks,
      certificateFile: formData.value.certificateFile
    }

    emit('success', updatedTask)
    ElMessage.success('任务完成成功')
    handleClose()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.upload-area {
  .upload-content {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;

    .file-info {
      color: #606266;
      font-size: 14px;
    }
  }

  .upload-tip {
    color: #909399;
    font-size: 12px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
