<!--
  页面名称：订单详情弹窗
  功能描述：展示订单详细信息，包含基础信息、服务信息、机构信息、客户信息、费用信息、服务记录等
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="订单详情"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="order-detail-dialog"
  >
    <div class="dialog-content">
      <!-- 基础信息 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          <span>基础信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>订单编号：</label>
            <span>{{ orderDetail?.orderNo }}</span>
          </div>
          <div class="info-item">
            <label>订单状态：</label>
            <el-tag :type="getOrderStatusType(orderDetail?.orderStatus)" size="small">
              {{ getOrderStatusText(orderDetail?.orderStatus) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>下单时间：</label>
            <span>{{ orderDetail?.createTime }}</span>
          </div>
          <div class="info-item">
            <label>支付时间：</label>
            <span>{{ orderDetail?.paymentTime }}</span>
          </div>
        </div>
      </div>

      <!-- 服务信息 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Service /></el-icon>
          <span>服务信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>套餐名称：</label>
            <span>{{ orderDetail?.packageName }}</span>
          </div>
          <div class="info-item">
            <label>服务类型：</label>
            <span>{{ getServiceType(orderDetail?.packageName) }}</span>
          </div>
          <div class="info-item">
            <label>服务时长：</label>
            <span>{{ getServiceDuration(orderDetail?.packageName) }}</span>
          </div>
          <div class="info-item">
            <label>服务地址：</label>
            <span class="address-value">北京市朝阳区建国路88号金地中心A座1201</span>
          </div>
        </div>
      </div>

      <!-- 机构与服务人员 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><UserFilled /></el-icon>
          <span>机构与服务人员</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>家政机构：</label>
            <span>{{ orderDetail?.agencyName }}</span>
          </div>
          <div class="info-item">
            <label>机构联系电话：</label>
            <span>010-12345678</span>
          </div>
          <div class="info-item">
            <label>服务阿姨：</label>
            <span>{{ orderDetail?.practitionerName }}</span>
          </div>
          <div class="info-item">
            <label>阿姨联系电话：</label>
            <span>138-0000-1234</span>
          </div>
        </div>
      </div>

      <!-- 客户信息 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><User /></el-icon>
          <span>客户信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>客户姓名：</label>
            <span>李女士</span>
          </div>
          <div class="info-item">
            <label>联系电话：</label>
            <span>139-0000-5678</span>
          </div>
          <div class="info-item">
            <label>客户地址：</label>
            <span class="address-value">北京市朝阳区建国路88号金地中心A座1201</span>
          </div>
          <div class="info-item">
            <label>特殊要求：</label>
            <span class="requirement-value">需要有育儿经验,会做月子餐</span>
          </div>
        </div>
      </div>

      <!-- 费用信息 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Money /></el-icon>
          <span>费用信息</span>
        </div>
        <div class="cost-info">
          <div class="cost-item">
            <label>订单金额：</label>
            <span class="amount-value total-amount"
              >¥{{ formatAmount(orderDetail?.orderAmount) }}</span
            >
          </div>
          <div class="cost-item">
            <label>机构分成：</label>
            <span class="amount-value agency-amount"
              >¥{{ formatAmount(orderDetail?.agencyCommission) }}</span
            >
          </div>
          <div class="cost-item">
            <label>平台分成：</label>
            <span class="amount-value platform-amount"
              >¥{{ formatAmount(orderDetail?.platformCommission) }}</span
            >
          </div>
          <div class="cost-item">
            <label>支付方式：</label>
            <span>微信支付</span>
          </div>
        </div>
      </div>

      <!-- 服务记录 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Clock /></el-icon>
          <span>服务记录</span>
        </div>
        <div class="service-records">
          <el-timeline>
            <el-timeline-item
              v-for="record in getServiceRecords()"
              :key="record.id"
              :timestamp="record.time"
              placement="top"
              :color="getTimelineColor(record.status)"
            >
              <div class="timeline-content">
                <div class="timeline-title">{{ record.title }}</div>
                <div class="timeline-description">{{ record.description }}</div>
                <el-tag
                  :type="getStatusTagType(record.status)"
                  size="small"
                  class="timeline-status"
                >
                  {{ record.status }}
                </el-tag>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出详情</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Service, UserFilled, User, Money, Clock } from '@element-plus/icons-vue'
import { getOrderDetail, exportOrderDetail } from '@/api/settlement/order-funds'

/** 弹窗显示状态 */
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

/** 订单详情数据 */
const orderDetail = ref<any>(null)

/** 加载状态 */
const loading = ref(false)

/** 组件属性 */
const props = defineProps<{
  visible: boolean
  orderData: any
}>()

/** 组件事件 */
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

/** 处理关闭 */
const handleClose = () => {
  dialogVisible.value = false
}

/** 监听订单数据变化，获取详情 */
watch(
  () => props.orderData,
  async (newData) => {
    if (newData && newData.orderNo) {
      await fetchOrderDetail(newData.orderNo)
    }
  },
  { immediate: true }
)

/** 获取订单详情 */
const fetchOrderDetail = async (orderNo: string) => {
  loading.value = true
  try {
    const response = await getOrderDetail(orderNo)
    orderDetail.value = response.data || props.orderData
  } catch (error) {
    ElMessage.error('获取订单详情失败')
    console.error('获取订单详情失败:', error)
    // 如果API失败，使用传入的订单数据作为备选
    orderDetail.value = props.orderData
  } finally {
    loading.value = false
  }
}

/** 处理导出 */
const handleExport = async () => {
  try {
    await exportOrderDetail(props.orderData?.orderNo)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出失败:', error)
  }
}

/** 获取服务类型 */
const getServiceType = (packageName: string): string => {
  if (packageName?.includes('月嫂')) return '月嫂服务'
  if (packageName?.includes('保洁')) return '保洁服务'
  if (packageName?.includes('整理')) return '整理服务'
  if (packageName?.includes('清洗')) return '清洗服务'
  if (packageName?.includes('育儿嫂')) return '育儿服务'
  return '家政服务'
}

/** 获取服务时长 */
const getServiceDuration = (packageName: string): string => {
  if (packageName?.includes('26天')) return '26天'
  if (packageName?.includes('30天')) return '30天'
  if (packageName?.includes('4小时')) return '4小时'
  if (packageName?.includes('6小时')) return '6小时'
  if (packageName?.includes('3小时')) return '3小时'
  return '按需服务'
}

/** 获取服务记录 */
const getServiceRecords = () => {
  return [
    {
      id: 1,
      time: '2024-08-01 10:30',
      title: '订单创建',
      description: '客户下单成功,等待机构确认',
      status: '已完成'
    },
    {
      id: 2,
      time: '2024-08-01 11:00',
      title: '机构确认',
      description: '阳光家政确认接单,安排服务人员',
      status: '已完成'
    },
    {
      id: 3,
      time: '2024-08-01 14:30',
      title: '服务人员确认',
      description: '王阿姨确认服务时间和地点',
      status: '已完成'
    },
    {
      id: 4,
      time: '2024-08-02 08:00',
      title: '服务开始',
      description: '王阿姨到达客户家中,开始26天月嫂服务',
      status: '已完成'
    },
    {
      id: 5,
      time: '2024-08-15 18:00',
      title: '服务完成',
      description: '26天月嫂服务圆满完成,客户满意',
      status: '已完成'
    }
  ]
}

/** 获取订单状态类型 */
const getOrderStatusType = (status: string): 'success' | 'warning' | 'danger' | 'info' => {
  const statusMap: Record<string, 'success' | 'warning' | 'danger' | 'info'> = {
    completed: 'success',
    in_service: 'warning',
    cancelled: 'danger',
    refunded: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    completed: '已完成',
    in_service: '服务中',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return statusMap[status] || status
}

/** 获取时间线颜色 */
const getTimelineColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    已完成: '#67c23a',
    进行中: '#409eff',
    待处理: '#e6a23c',
    失败: '#f56c6c'
  }
  return colorMap[status] || '#909399'
}

/** 获取状态标签类型 */
const getStatusTagType = (
  status: string
): 'success' | 'primary' | 'warning' | 'danger' | 'info' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'danger' | 'info'> = {
    已完成: 'success',
    进行中: 'primary',
    待处理: 'warning',
    失败: 'danger'
  }
  return typeMap[status] || 'info'
}

/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount?.toFixed(2) || '0.00'
}
</script>

<style scoped lang="scss">
.order-detail-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: white;
    }
  }

  .dialog-content {
    max-height: 60vh;
    overflow-y: auto;

    .info-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      background: #fafafa;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-weight: 600;
        color: #303133;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 100px;
            flex-shrink: 0;
          }

          span {
            color: #303133;
            flex: 1;

            &.address-value,
            &.requirement-value {
              word-break: break-all;
              line-height: 1.5;
            }
          }
        }
      }

      .cost-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .cost-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 100px;
          }

          .amount-value {
            font-weight: 600;
            font-size: 16px;

            &.total-amount {
              color: #f56c6c;
            }

            &.agency-amount {
              color: #67c23a;
            }

            &.platform-amount {
              color: #409eff;
            }
          }
        }
      }

      .service-records {
        .timeline-content {
          .timeline-title {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .timeline-description {
            color: #606266;
            margin-bottom: 8px;
            line-height: 1.5;
          }

          .timeline-status {
            margin-top: 4px;
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
