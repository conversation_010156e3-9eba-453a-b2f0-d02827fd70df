import request from '@/config/axios'
import qs from 'qs'

// 图片VO
export interface ImageVO {
  id: number
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId: number
  description?: string
  createTime?: string
  visibleOrgId?: number
  visibleOrgName?: string
}

// 图片列表
export const getImageList = (params: {
  pageNo: number
  pageSize: number
  name?: string
  sourceOrgId?: number
  categoryId?: number
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.get({ url: '/system/material/image/list', params })
}

// 新增图片
export const createImage = (data: {
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/image/create', data })
}

// 编辑图片
export const updateImage = (data: {
  id: number
  name: string
  url: string
  sourceOrgId: number
  sourceOrgName: string
  categoryId?: number
  description?: string
  visibleOrgId?: number
  visibleOrgName?: string
}) => {
  return request.post({ url: '/system/material/image/update', data })
}

// 删除图片
export const deleteImage = (id: number) => {
  return request.post({
    url: '/system/material/image/delete',
    data: qs.stringify({ id }), // 转为 a=1&b=2 形式
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 图片详情
export const getImageDetail = (id: number) => {
  return request.get({ url: '/system/material/image/detail', params: { id } })
}
