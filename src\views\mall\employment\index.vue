<!--
  页面名称：就业服务管理
  功能描述：展示就业服务相关功能，包括机构管理、任务管理、轮播图管理、资讯管理等
-->
<template>
  <div class="employment-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2><i class="fas fa-user-tie"></i> 就业管理</h2>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="function-tabs">
      <el-tab-pane label="机构管理" name="agency">
      <div class="tab-section-title">机构列表</div>
        <AgencyManagement />
      </el-tab-pane>
      <el-tab-pane label="任务管理" name="tasks">
      <div class="tab-section-title">任务列表</div>
        <TaskManagement />
      </el-tab-pane>
      <el-tab-pane label="轮播图管理" name="carousel">
      <div class="tab-section-title">轮播图列表</div>
        <CarouselManagement />
      </el-tab-pane>
      <el-tab-pane label="资讯管理" name="news">
      <div class="tab-section-title">资讯列表</div>
        <NewsManagement />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AgencyManagement from './components/AgencyManagement.vue'
import TaskManagement from './components/TaskManagement.vue'
import CarouselManagement from './components/CarouselManagement.vue'
import NewsManagement from './components/NewsManagement.vue'

/** 当前激活的标签页 */
const activeTab = ref('agency')
</script>

<style scoped lang="scss">
.employment-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    font-size: 24px;
    color: #343a40;
    display: flex;
    align-items: center;

    i {
      margin-right: 10px;
      color: #3498db;
    }
  }
}

.function-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  :deep(.el-tabs__header) {
    margin: 0;
    padding: 0 20px;
    border-bottom: 1px solid #dee2e6;
  }

  :deep(.el-tabs__content) {
    padding: 20px;
  }
}

.tab-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding: 10px 0 12px 0;
  position: relative;
}

.tab-section-title::after {
  content: '';
  display: block;
  height: 1px;
  background: #e4e7ed;
  margin-top: 10px;
}
</style>
